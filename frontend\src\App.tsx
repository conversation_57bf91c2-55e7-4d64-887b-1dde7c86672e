import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

import { store } from './store';
import { useAppDispatch, useAppSelector } from './hooks/redux';
import { initializeAuth } from './store/slices/authSlice';
import { ROUTES, THEME_CONFIG } from './constants';
import HelpPage from './pages/help/HelpPage';
import WorkflowPage from './pages/help/WorkflowPage';

// 页面组件
import LoginPage from './pages/auth/LoginPage';
import DashboardPage from './pages/dashboard/DashboardPage';
import MainLayout from './components/layout/MainLayout';
import ProtectedRoute from './components/auth/ProtectedRoute';
import ErrorBoundary from './components/ErrorBoundary';

// BOM管理页面
import CoreBOMListPage from './pages/bom/CoreBOMListPage';
import CoreBOMCreatePage from './pages/bom/CoreBOMCreatePage';
import CoreBOMEditPage from './pages/bom/CoreBOMEditPage';
import CoreBOMViewPage from './pages/bom/CoreBOMViewPage';
import OrderBOMListPage from './pages/bom/OrderBOMListPage';
import OrderBOMCreatePage from './pages/bom/OrderBOMCreatePage';
import OrderBOMDerivePage from './pages/bom/OrderBOMDerivePage';
import OrderBOMViewPage from './pages/bom/OrderBOMViewPage';

// 物料管理页面
import MaterialListPage from './pages/material/MaterialListPage';
import MaterialCreatePage from './pages/material/MaterialCreatePage';
import MaterialEditPage from './pages/material/MaterialEditPage';

// 库存管理页面
import InventoryListPage from './pages/inventory/InventoryListPage';
import InventoryReceivePage from './pages/inventory/InventoryReceivePage';
import InventoryIssuePage from './pages/inventory/InventoryIssuePage';
import InventoryAdjustPage from './pages/inventory/InventoryAdjustPage';
import RemnantListPage from './pages/inventory/RemnantListPage';
import CuttingPlanPage from './pages/inventory/CuttingPlanPage';
import BatchTrackingPage from './pages/inventory/BatchTrackingPage';

// 采购管理页面
import PurchaseListPage from './pages/purchase/PurchaseListPage';
import PurchaseRequisitionPage from './pages/purchase/PurchaseRequisitionPage';
import MRPCalculationPage from './pages/purchase/MRPCalculationPage';
import PurchaseOptimizationPage from './pages/purchase/PurchaseOptimizationPage';

// 成本管理页面
import CostAnalysisPage from './pages/cost/CostAnalysisPage';
import CostReportsPage from './pages/cost/CostReportsPage';
import WasteTrackingPage from './pages/cost/WasteTrackingPage';
import StandardCostPage from './pages/cost/StandardCostPage';

// 服务管理页面
import ServiceBOMListPage from './pages/service/ServiceBOMListPage';
import DeviceArchivePage from './pages/service/DeviceArchivePage';
import MaintenancePage from './pages/service/MaintenancePage';
import AsBuiltBOMPage from './pages/service/AsBuiltBOMPage';
import SparePartsPage from './pages/service/SparePartsPage';

// ECN管理页面
import ECNListPage from './pages/ecn/ECNListPage';
import ECNCreatePage from './pages/ecn/ECNCreatePage';
import ECNReviewPage from './pages/ecn/ECNReviewPage';

// 报告页面
import ReportsPage from './pages/reports/ReportsPage';
import DashboardConfigPage from './pages/reports/DashboardConfigPage';

// 系统管理页面
import UserListPage from './pages/system/UserListPage';
import RoleListPage from './pages/system/RoleListPage';
import PermissionListPage from './pages/system/PermissionListPage';
import SystemConfigPage from './pages/system/SystemConfigPage';
import AuditLogPage from './pages/system/AuditLogPage';

// 移动端页面
import MobilePage from './pages/mobile/MobilePage';
import MobileScanPage from './pages/mobile/MobileScanPage';
import MobileInventoryPage from './pages/mobile/MobileInventoryPage';

// 帮助手册页面
import HelpPage from './pages/help/HelpPage';

// 设置dayjs中文
dayjs.locale('zh-cn');

const AppContent: React.FC = () => {
  const dispatch = useAppDispatch();
  const { isAuthenticated } = useAppSelector(state => state.auth);

  useEffect(() => {
    dispatch(initializeAuth());
  }, [dispatch]);

  return (
    <Routes>
      {/* 登录页面 */}
      <Route path={ROUTES.LOGIN} element={<LoginPage />} />
      
      {/* 受保护的路由 */}
      <Route path="/" element={<ProtectedRoute><MainLayout /></ProtectedRoute>}>
        {/* 仪表板 */}
        <Route index element={<Navigate to={ROUTES.DASHBOARD} replace />} />
        <Route path={ROUTES.DASHBOARD} element={<DashboardPage />} />
        
        {/* BOM管理 */}
        <Route path={ROUTES.CORE_BOM} element={<CoreBOMListPage />} />
        <Route path={ROUTES.CORE_BOM_CREATE} element={<CoreBOMCreatePage />} />
        <Route path={ROUTES.CORE_BOM_EDIT} element={<CoreBOMEditPage />} />
        <Route path={ROUTES.CORE_BOM_VIEW} element={<CoreBOMViewPage />} />
        
        <Route path={ROUTES.ORDER_BOM} element={<OrderBOMListPage />} />
        <Route path={ROUTES.ORDER_BOM_CREATE} element={<OrderBOMCreatePage />} />
        <Route path={ROUTES.ORDER_BOM_DERIVE} element={<OrderBOMDerivePage />} />
        <Route path={ROUTES.ORDER_BOM_VIEW} element={<OrderBOMViewPage />} />
        
        {/* 物料管理 */}
        <Route path={ROUTES.MATERIALS} element={<MaterialListPage />} />
        <Route path={ROUTES.MATERIALS_CREATE} element={<MaterialCreatePage />} />
        <Route path={ROUTES.MATERIALS_EDIT} element={<MaterialEditPage />} />
        
        {/* 库存管理 */}
        <Route path={ROUTES.INVENTORY} element={<InventoryListPage />} />
        <Route path={ROUTES.INVENTORY_RECEIVE} element={<InventoryReceivePage />} />
        <Route path={ROUTES.INVENTORY_ISSUE} element={<InventoryIssuePage />} />
        <Route path={ROUTES.INVENTORY_ADJUST} element={<InventoryAdjustPage />} />
        <Route path={ROUTES.REMNANTS} element={<RemnantListPage />} />
        <Route path={ROUTES.CUTTING_PLAN} element={<CuttingPlanPage />} />
        <Route path={ROUTES.BATCH_TRACKING} element={<BatchTrackingPage />} />
        
        {/* 采购管理 */}
        <Route path={ROUTES.PURCHASE} element={<PurchaseListPage />} />
        <Route path={ROUTES.PURCHASE_REQUISITION} element={<PurchaseRequisitionPage />} />
        <Route path={ROUTES.MRP_CALCULATION} element={<MRPCalculationPage />} />
        <Route path={ROUTES.PURCHASE_OPTIMIZATION} element={<PurchaseOptimizationPage />} />
        
        {/* 成本管理 */}
        <Route path={ROUTES.COST_ANALYSIS} element={<CostAnalysisPage />} />
        <Route path={ROUTES.COST_REPORTS} element={<CostReportsPage />} />
        <Route path={ROUTES.WASTE_TRACKING} element={<WasteTrackingPage />} />
        <Route path={ROUTES.STANDARD_COST} element={<StandardCostPage />} />
        
        {/* 服务管理 */}
        <Route path={ROUTES.SERVICE_BOM} element={<ServiceBOMListPage />} />
        <Route path={ROUTES.DEVICE_ARCHIVE} element={<DeviceArchivePage />} />
        <Route path={ROUTES.MAINTENANCE} element={<MaintenancePage />} />
        <Route path={ROUTES.AS_BUILT_BOM} element={<AsBuiltBOMPage />} />
            <Route path={ROUTES.SPARE_PARTS} element={<SparePartsPage />} />
        
        {/* ECN管理 */}
        <Route path={ROUTES.ECN} element={<ECNListPage />} />
        <Route path={ROUTES.ECN_CREATE} element={<ECNCreatePage />} />
        <Route path={ROUTES.ECN_REVIEW} element={<ECNReviewPage />} />
        
        {/* 报告 */}
        <Route path={ROUTES.REPORTS} element={<ReportsPage />} />
        <Route path={ROUTES.DASHBOARD_CONFIG} element={<DashboardConfigPage />} />
        
        {/* 系统管理 */}
        <Route path={ROUTES.USERS} element={<UserListPage />} />
            <Route path={ROUTES.ROLES} element={<RoleListPage />} />
            <Route path={ROUTES.PERMISSIONS} element={<PermissionListPage />} />
            <Route path={ROUTES.SYSTEM_CONFIG} element={<SystemConfigPage />} />
            <Route path={ROUTES.AUDIT_LOG} element={<AuditLogPage />} />
        
        {/* 移动端 */}
        <Route path={ROUTES.MOBILE} element={<MobilePage />} />
        <Route path={ROUTES.MOBILE_SCAN} element={<MobileScanPage />} />
        <Route path={ROUTES.MOBILE_INVENTORY} element={<MobileInventoryPage />} />

        {/* 帮助手册 */}
        <Route path={ROUTES.HELP} element={<HelpPage />} />
        <Route path={ROUTES.HELP_FUNCTIONS} element={<HelpPage />} />
        <Route path={ROUTES.HELP_ROLES} element={<HelpPage />} />
        <Route path={ROUTES.HELP_KNOWLEDGE} element={<HelpPage />} />
        <Route path={ROUTES.HELP_WORKFLOWS} element={<WorkflowPage />} />
        <Route path={ROUTES.HELP_PROCESSES} element={<HelpPage />} />
        <Route path={ROUTES.HELP_SEARCH} element={<HelpPage />} />
        <Route path="/help/:category/:id" element={<HelpPage />} />
      </Route>
      
      {/* 404页面 */}
      <Route path="*" element={<Navigate to={ROUTES.DASHBOARD} replace />} />
    </Routes>
  );
};

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <ConfigProvider 
        locale={zhCN}
        theme={{
          token: {
            colorPrimary: THEME_CONFIG.primaryColor,
            borderRadius: THEME_CONFIG.borderRadius,
          },
        }}
      >
        <AntdApp>
          <ErrorBoundary>
            <Router>
              <AppContent />
            </Router>
          </ErrorBoundary>
        </AntdApp>
      </ConfigProvider>
    </Provider>
  );
};

export default App;
