package com.linkboms.service.help.impl;

import com.linkboms.dto.help.SearchResultDto;
import com.linkboms.dto.help.SearchSuggestionDto;
import com.linkboms.entity.help.HelpContent;
import com.linkboms.entity.help.HelpTag;
import com.linkboms.repository.help.HelpContentRepository;
import com.linkboms.repository.help.HelpTagRepository;
import com.linkboms.service.help.HelpSearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 帮助搜索服务实现
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HelpSearchServiceImpl implements HelpSearchService {

    private final HelpContentRepository contentRepository;
    private final HelpTagRepository tagRepository;
    private final EntityManager entityManager;
    private final RedisTemplate<String, Object> redisTemplate;

    private static final String SEARCH_LOG_KEY = "help:search:log";
    private static final String HOT_KEYWORDS_KEY = "help:search:hot_keywords";
    private static final String SEARCH_HISTORY_KEY = "help:search:history:";
    private static final String SAVED_SEARCH_KEY = "help:search:saved:";
    private static final String SEARCH_SUGGESTIONS_KEY = "help:search:suggestions:";

    @Override
    public Page<SearchResultDto> searchContent(String keyword, Map<String, Object> filters, Pageable pageable) {
        log.info("执行全文搜索，关键词: {}, 过滤条件: {}", keyword, filters);

        try {
            // 构建搜索查询
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT c.*, ts_rank(to_tsvector('english', c.title || ' ' || c.content), plainto_tsquery('english', ?)) as rank ");
            sql.append("FROM help_content c WHERE c.is_deleted = false AND c.is_published = true ");

            List<Object> params = new ArrayList<>();
            params.add(keyword);

            // 添加全文搜索条件
            if (StringUtils.hasText(keyword)) {
                sql.append("AND (to_tsvector('english', c.title || ' ' || c.content) @@ plainto_tsquery('english', ?) ");
                sql.append("OR c.title ILIKE ? OR c.content ILIKE ? OR c.keywords ILIKE ?) ");
                params.add(keyword);
                params.add("%" + keyword + "%");
                params.add("%" + keyword + "%");
                params.add("%" + keyword + "%");
            }

            // 添加过滤条件
            addFilters(sql, params, filters);

            // 添加排序
            sql.append("ORDER BY rank DESC, c.view_count DESC, c.updated_at DESC ");

            // 执行查询
            Query query = entityManager.createNativeQuery(sql.toString(), HelpContent.class);
            for (int i = 0; i < params.size(); i++) {
                query.setParameter(i + 1, params.get(i));
            }

            // 分页
            query.setFirstResult((int) pageable.getOffset());
            query.setMaxResults(pageable.getPageSize());

            @SuppressWarnings("unchecked")
            List<HelpContent> contents = query.getResultList();

            // 转换为DTO
            List<SearchResultDto> results = contents.stream()
                    .map(this::convertToSearchResult)
                    .collect(Collectors.toList());

            // 获取总数
            long total = getTotalCount(keyword, filters);

            // 记录搜索日志
            logSearch(keyword, null, (int) total);

            return new PageImpl<>(results, pageable, total);

        } catch (Exception e) {
            log.error("搜索失败", e);
            return new PageImpl<>(Collections.emptyList(), pageable, 0);
        }
    }

    @Override
    public Page<SearchResultDto> advancedSearch(Map<String, Object> searchParams, Pageable pageable) {
        log.info("执行高级搜索，参数: {}", searchParams);

        String keyword = (String) searchParams.get("keyword");
        return searchContent(keyword, searchParams, pageable);
    }

    @Override
    public List<SearchSuggestionDto> getSearchSuggestions(String keyword, int limit) {
        if (!StringUtils.hasText(keyword) || keyword.length() < 2) {
            return Collections.emptyList();
        }

        String cacheKey = SEARCH_SUGGESTIONS_KEY + keyword.toLowerCase();
        
        // 尝试从缓存获取
        @SuppressWarnings("unchecked")
        List<SearchSuggestionDto> cached = (List<SearchSuggestionDto>) redisTemplate.opsForValue().get(cacheKey);
        if (cached != null) {
            return cached;
        }

        List<SearchSuggestionDto> suggestions = new ArrayList<>();

        try {
            // 内容标题建议
            List<HelpContent> titleMatches = contentRepository.findByTitleContainingIgnoreCaseAndDeletedFalseAndPublishedTrue(
                    keyword, org.springframework.data.domain.PageRequest.of(0, limit / 2)
            ).getContent();

            titleMatches.forEach(content -> {
                SearchSuggestionDto suggestion = new SearchSuggestionDto();
                suggestion.setType("content");
                suggestion.setTitle(content.getTitle());
                suggestion.setDescription(content.getSummary());
                suggestion.setRelevance(calculateRelevance(content.getTitle(), keyword));
                suggestions.add(suggestion);
            });

            // 标签建议
            List<HelpTag> tagMatches = tagRepository.findByNameContainingIgnoreCaseAndEnabledTrueAndDeletedFalse(keyword);
            tagMatches.stream().limit(limit / 2).forEach(tag -> {
                SearchSuggestionDto suggestion = new SearchSuggestionDto();
                suggestion.setType("tag");
                suggestion.setTitle(tag.getName());
                suggestion.setDescription(tag.getDescription());
                suggestion.setCount(tag.getUsageCount());
                suggestion.setRelevance(calculateRelevance(tag.getName(), keyword));
                suggestions.add(suggestion);
            });

            // 按相关性排序
            suggestions.sort((a, b) -> Double.compare(b.getRelevance(), a.getRelevance()));

            // 限制数量
            List<SearchSuggestionDto> result = suggestions.stream().limit(limit).collect(Collectors.toList());

            // 缓存结果
            redisTemplate.opsForValue().set(cacheKey, result, 10, TimeUnit.MINUTES);

            return result;

        } catch (Exception e) {
            log.error("获取搜索建议失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> getHotKeywords(int limit) {
        try {
            @SuppressWarnings("unchecked")
            Set<Object> hotKeywords = redisTemplate.opsForZSet().reverseRange(HOT_KEYWORDS_KEY, 0, limit - 1);
            
            if (hotKeywords != null) {
                return hotKeywords.stream()
                        .map(Object::toString)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("获取热门关键词失败", e);
        }

        // 返回默认热门关键词
        return Arrays.asList(
                "BOM创建", "物料管理", "库存盘点", "采购流程", "成本分析",
                "用户权限", "角色管理", "系统设置", "数据导入", "报表生成"
        );
    }

    @Override
    public List<HelpContent> getRelatedContent(Long contentId, int limit) {
        try {
            HelpContent content = contentRepository.findById(contentId).orElse(null);
            if (content == null) {
                return Collections.emptyList();
            }

            // 基于标签和分类查找相关内容
            List<HelpContent> related = contentRepository.findRelatedContent(
                    contentId, content.getCategoryId(), limit
            );

            return related;

        } catch (Exception e) {
            log.error("获取相关内容失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public void logSearch(String keyword, Long userId, int resultCount) {
        try {
            if (StringUtils.hasText(keyword)) {
                // 记录搜索日志
                Map<String, Object> logEntry = new HashMap<>();
                logEntry.put("keyword", keyword);
                logEntry.put("userId", userId);
                logEntry.put("resultCount", resultCount);
                logEntry.put("timestamp", LocalDateTime.now());

                redisTemplate.opsForList().leftPush(SEARCH_LOG_KEY, logEntry);

                // 更新热门关键词
                redisTemplate.opsForZSet().incrementScore(HOT_KEYWORDS_KEY, keyword, 1);

                // 保存用户搜索历史
                if (userId != null) {
                    String historyKey = SEARCH_HISTORY_KEY + userId;
                    redisTemplate.opsForList().leftPush(historyKey, keyword);
                    redisTemplate.opsForList().trim(historyKey, 0, 19); // 保留最近20条
                    redisTemplate.expire(historyKey, 30, TimeUnit.DAYS);
                }
            }
        } catch (Exception e) {
            log.error("记录搜索日志失败", e);
        }
    }

    @Override
    public void rebuildSearchIndex() {
        log.info("开始重建搜索索引");
        try {
            // 这里可以集成Elasticsearch或其他搜索引擎
            // 目前使用PostgreSQL的全文搜索功能
            
            // 更新所有内容的搜索向量
            String sql = "UPDATE help_content SET search_vector = to_tsvector('english', title || ' ' || content || ' ' || COALESCE(keywords, ''))";
            entityManager.createNativeQuery(sql).executeUpdate();
            
            log.info("搜索索引重建完成");
        } catch (Exception e) {
            log.error("重建搜索索引失败", e);
        }
    }

    @Override
    public void updateContentIndex(Long contentId) {
        try {
            String sql = "UPDATE help_content SET search_vector = to_tsvector('english', title || ' ' || content || ' ' || COALESCE(keywords, '')) WHERE id = ?";
            Query query = entityManager.createNativeQuery(sql);
            query.setParameter(1, contentId);
            query.executeUpdate();
        } catch (Exception e) {
            log.error("更新内容索引失败", e);
        }
    }

    @Override
    public void deleteContentIndex(Long contentId) {
        // PostgreSQL中删除记录即可，无需额外操作
        log.info("删除内容索引: {}", contentId);
    }

    @Override
    public Map<String, Object> getSearchStatistics() {
        Map<String, Object> stats = new HashMap<>();
        
        try {
            // 搜索总次数
            Long searchCount = redisTemplate.opsForList().size(SEARCH_LOG_KEY);
            stats.put("totalSearches", searchCount != null ? searchCount : 0);

            // 热门关键词
            List<String> hotKeywords = getHotKeywords(10);
            stats.put("hotKeywords", hotKeywords);

            // 今日搜索次数（简化实现）
            stats.put("todaySearches", searchCount != null ? searchCount / 30 : 0);

        } catch (Exception e) {
            log.error("获取搜索统计失败", e);
        }

        return stats;
    }

    @Override
    public List<HelpContent> getRecommendedContent(Long userId, int limit) {
        try {
            // 基于用户搜索历史推荐内容
            List<String> searchHistory = getSearchHistory(userId, 10);
            
            if (searchHistory.isEmpty()) {
                // 返回热门内容
                return contentRepository.findByDeletedFalseAndPublishedTrueOrderByViewCountDescLikeCountDesc(
                        org.springframework.data.domain.PageRequest.of(0, limit)
                ).getContent();
            }

            // 基于搜索历史推荐
            String keywords = String.join(" ", searchHistory);
            Page<SearchResultDto> results = searchContent(keywords, Collections.emptyMap(), 
                    org.springframework.data.domain.PageRequest.of(0, limit));
            
            return results.getContent().stream()
                    .map(dto -> {
                        HelpContent content = new HelpContent();
                        content.setId(dto.getId());
                        content.setTitle(dto.getTitle());
                        content.setSummary(dto.getSummary());
                        return content;
                    })
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取推荐内容失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public List<String> getAutoCompleteSuggestions(String prefix, int limit) {
        if (!StringUtils.hasText(prefix) || prefix.length() < 2) {
            return Collections.emptyList();
        }

        try {
            // 从热门关键词中匹配
            List<String> hotKeywords = getHotKeywords(100);
            return hotKeywords.stream()
                    .filter(keyword -> keyword.toLowerCase().startsWith(prefix.toLowerCase()))
                    .limit(limit)
                    .collect(Collectors.toList());

        } catch (Exception e) {
            log.error("获取自动完成建议失败", e);
            return Collections.emptyList();
        }
    }

    @Override
    public Page<HelpContent> searchByTags(List<String> tagNames, Pageable pageable) {
        if (tagNames == null || tagNames.isEmpty()) {
            return new PageImpl<>(Collections.emptyList(), pageable, 0);
        }

        try {
            return contentRepository.findByTagNames(tagNames, pageable);
        } catch (Exception e) {
            log.error("按标签搜索失败", e);
            return new PageImpl<>(Collections.emptyList(), pageable, 0);
        }
    }

    @Override
    public Page<HelpContent> searchByCategory(Long categoryId, String keyword, Pageable pageable) {
        try {
            if (StringUtils.hasText(keyword)) {
                return contentRepository.findByCategoryIdAndKeyword(categoryId, keyword, pageable);
            } else {
                return contentRepository.findByCategoryIdAndDeletedFalseAndPublishedTrue(categoryId, pageable);
            }
        } catch (Exception e) {
            log.error("按分类搜索失败", e);
            return new PageImpl<>(Collections.emptyList(), pageable, 0);
        }
    }

    @Override
    public Page<SearchResultDto> fuzzySearch(String keyword, double threshold, Pageable pageable) {
        // 简化的模糊搜索实现
        return searchContent(keyword, Collections.emptyMap(), pageable);
    }

    @Override
    public List<String> getSearchHistory(Long userId, int limit) {
        if (userId == null) {
            return Collections.emptyList();
        }

        try {
            String historyKey = SEARCH_HISTORY_KEY + userId;
            List<Object> history = redisTemplate.opsForList().range(historyKey, 0, limit - 1);
            
            if (history != null) {
                return history.stream()
                        .map(Object::toString)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("获取搜索历史失败", e);
        }

        return Collections.emptyList();
    }

    @Override
    public void clearSearchHistory(Long userId) {
        if (userId != null) {
            try {
                String historyKey = SEARCH_HISTORY_KEY + userId;
                redisTemplate.delete(historyKey);
            } catch (Exception e) {
                log.error("清除搜索历史失败", e);
            }
        }
    }

    @Override
    public void saveSearchCondition(Long userId, String name, Map<String, Object> searchParams) {
        if (userId == null || !StringUtils.hasText(name)) {
            return;
        }

        try {
            String savedKey = SAVED_SEARCH_KEY + userId;
            Map<String, Object> savedSearch = new HashMap<>();
            savedSearch.put("id", System.currentTimeMillis());
            savedSearch.put("name", name);
            savedSearch.put("params", searchParams);
            savedSearch.put("timestamp", LocalDateTime.now());

            redisTemplate.opsForList().leftPush(savedKey, savedSearch);
            redisTemplate.expire(savedKey, 365, TimeUnit.DAYS);
        } catch (Exception e) {
            log.error("保存搜索条件失败", e);
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getSavedSearchConditions(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }

        try {
            String savedKey = SAVED_SEARCH_KEY + userId;
            List<Object> saved = redisTemplate.opsForList().range(savedKey, 0, -1);
            
            if (saved != null) {
                return saved.stream()
                        .map(obj -> (Map<String, Object>) obj)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("获取保存的搜索条件失败", e);
        }

        return Collections.emptyList();
    }

    @Override
    public void deleteSavedSearchCondition(Long userId, Long searchId) {
        // 简化实现，实际应该根据searchId删除特定项
        log.info("删除保存的搜索条件: userId={}, searchId={}", userId, searchId);
    }

    // 私有辅助方法

    private void addFilters(StringBuilder sql, List<Object> params, Map<String, Object> filters) {
        if (filters == null || filters.isEmpty()) {
            return;
        }

        // 内容类型过滤
        if (filters.containsKey("type")) {
            sql.append("AND c.type = ? ");
            params.add(filters.get("type"));
        }

        // 分类过滤
        if (filters.containsKey("categoryId")) {
            sql.append("AND c.category_id = ? ");
            params.add(filters.get("categoryId"));
        }

        // 状态过滤
        if (filters.containsKey("status")) {
            sql.append("AND c.status = ? ");
            params.add(filters.get("status"));
        }

        // 特色内容过滤
        if (filters.containsKey("featured") && Boolean.TRUE.equals(filters.get("featured"))) {
            sql.append("AND c.is_featured = true ");
        }

        // 日期范围过滤
        if (filters.containsKey("startDate")) {
            sql.append("AND c.created_at >= ? ");
            params.add(filters.get("startDate"));
        }

        if (filters.containsKey("endDate")) {
            sql.append("AND c.created_at <= ? ");
            params.add(filters.get("endDate"));
        }
    }

    private long getTotalCount(String keyword, Map<String, Object> filters) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT COUNT(*) FROM help_content c WHERE c.is_deleted = false AND c.is_published = true ");

        List<Object> params = new ArrayList<>();

        if (StringUtils.hasText(keyword)) {
            sql.append("AND (to_tsvector('english', c.title || ' ' || c.content) @@ plainto_tsquery('english', ?) ");
            sql.append("OR c.title ILIKE ? OR c.content ILIKE ? OR c.keywords ILIKE ?) ");
            params.add(keyword);
            params.add("%" + keyword + "%");
            params.add("%" + keyword + "%");
            params.add("%" + keyword + "%");
        }

        addFilters(sql, params, filters);

        Query query = entityManager.createNativeQuery(sql.toString());
        for (int i = 0; i < params.size(); i++) {
            query.setParameter(i + 1, params.get(i));
        }

        return ((Number) query.getSingleResult()).longValue();
    }

    private SearchResultDto convertToSearchResult(HelpContent content) {
        SearchResultDto dto = new SearchResultDto();
        dto.setId(content.getId());
        dto.setTitle(content.getTitle());
        dto.setContent(content.getContent());
        dto.setSummary(content.getSummary());
        dto.setType(content.getType());
        dto.setCategoryId(content.getCategoryId());
        dto.setViewCount(content.getViewCount());
        dto.setLikeCount(content.getLikeCount());
        dto.setFeatured(content.isFeatured());
        dto.setCreatedAt(content.getCreatedAt());
        dto.setUpdatedAt(content.getUpdatedAt());
        return dto;
    }

    private double calculateRelevance(String text, String keyword) {
        if (text == null || keyword == null) {
            return 0.0;
        }

        String lowerText = text.toLowerCase();
        String lowerKeyword = keyword.toLowerCase();

        if (lowerText.equals(lowerKeyword)) {
            return 100.0;
        }

        if (lowerText.startsWith(lowerKeyword)) {
            return 90.0;
        }

        if (lowerText.contains(lowerKeyword)) {
            return 70.0;
        }

        // 简单的模糊匹配
        String[] words = lowerKeyword.split("\\s+");
        double score = 0.0;
        for (String word : words) {
            if (lowerText.contains(word)) {
                score += 30.0;
            }
        }

        return score;
    }
}
