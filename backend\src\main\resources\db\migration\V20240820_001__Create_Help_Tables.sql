-- 创建帮助手册相关数据表
-- 作者: Link-BOM-S Team
-- 版本: 1.0.0
-- 创建时间: 2024-08-20

-- 1. 帮助分类表
CREATE TABLE help_category (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description VARCHAR(500),
    parent_id BIGINT,
    category_path VARCHAR(500),
    level INTEGER NOT NULL DEFAULT 0,
    sort_order INTEGER NOT NULL DEFAULT 0,
    icon VARCHAR(50),
    is_visible BOOLEAN NOT NULL DEFAULT TRUE,
    is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    creator <PERSON><PERSON><PERSON><PERSON>(100),
    creator_id BIGINT,
    updater VARCHAR(100),
    updater_id BIGINT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_help_category_parent FOREIG<PERSON> KEY (parent_id) REFERENCES help_category(id),
    CONSTRAINT uk_help_category_name UNIQUE (name, is_deleted),
    CONSTRAINT uk_help_category_path UNIQUE (category_path, is_deleted)
);

-- 创建分类表索引
CREATE INDEX idx_help_category_parent_id ON help_category(parent_id);
CREATE INDEX idx_help_category_level ON help_category(level);
CREATE INDEX idx_help_category_sort_order ON help_category(sort_order);
CREATE INDEX idx_help_category_enabled ON help_category(is_enabled);
CREATE INDEX idx_help_category_visible ON help_category(is_visible);
CREATE INDEX idx_help_category_deleted ON help_category(is_deleted);

-- 2. 帮助标签表
CREATE TABLE help_tag (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    description VARCHAR(200),
    type VARCHAR(20) NOT NULL DEFAULT 'CONTENT',
    color VARCHAR(20),
    usage_count BIGINT NOT NULL DEFAULT 0,
    is_enabled BOOLEAN NOT NULL DEFAULT TRUE,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    creator VARCHAR(100),
    creator_id BIGINT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_help_tag_type CHECK (type IN ('CONTENT', 'CATEGORY', 'SYSTEM', 'CUSTOM'))
);

-- 创建标签表索引
CREATE INDEX idx_help_tag_type ON help_tag(type);
CREATE INDEX idx_help_tag_usage_count ON help_tag(usage_count);
CREATE INDEX idx_help_tag_enabled ON help_tag(is_enabled);
CREATE INDEX idx_help_tag_deleted ON help_tag(is_deleted);

-- 3. 帮助内容表
CREATE TABLE help_content (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    summary VARCHAR(500),
    type VARCHAR(50) NOT NULL,
    category_id BIGINT NOT NULL,
    parent_id BIGINT,
    sort_order INTEGER NOT NULL DEFAULT 0,
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT',
    view_count BIGINT NOT NULL DEFAULT 0,
    like_count BIGINT NOT NULL DEFAULT 0,
    dislike_count BIGINT NOT NULL DEFAULT 0,
    author VARCHAR(100),
    author_id BIGINT,
    reviewer VARCHAR(100),
    reviewer_id BIGINT,
    reviewed_at TIMESTAMP,
    version VARCHAR(20) DEFAULT '1.0',
    is_featured BOOLEAN NOT NULL DEFAULT FALSE,
    is_published BOOLEAN NOT NULL DEFAULT FALSE,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    publish_at TIMESTAMP,
    expire_at TIMESTAMP,
    keywords VARCHAR(1000),
    meta_description VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_help_content_category FOREIGN KEY (category_id) REFERENCES help_category(id),
    CONSTRAINT fk_help_content_parent FOREIGN KEY (parent_id) REFERENCES help_content(id),
    CONSTRAINT chk_help_content_type CHECK (type IN ('FUNCTION_GUIDE', 'ROLE_PERMISSION', 'INDUSTRY_KNOWLEDGE', 'WORKFLOW', 'BUSINESS_PROCESS', 'FAQ', 'TUTORIAL', 'ANNOUNCEMENT', 'DIAGRAM', 'VIDEO')),
    CONSTRAINT chk_help_content_status CHECK (status IN ('DRAFT', 'PENDING_REVIEW', 'APPROVED', 'PUBLISHED', 'ARCHIVED', 'REJECTED'))
);

-- 创建内容表索引
CREATE INDEX idx_help_content_category_id ON help_content(category_id);
CREATE INDEX idx_help_content_type ON help_content(type);
CREATE INDEX idx_help_content_status ON help_content(status);
CREATE INDEX idx_help_content_author_id ON help_content(author_id);
CREATE INDEX idx_help_content_featured ON help_content(is_featured);
CREATE INDEX idx_help_content_published ON help_content(is_published);
CREATE INDEX idx_help_content_deleted ON help_content(is_deleted);
CREATE INDEX idx_help_content_view_count ON help_content(view_count);
CREATE INDEX idx_help_content_like_count ON help_content(like_count);
CREATE INDEX idx_help_content_created_at ON help_content(created_at);
CREATE INDEX idx_help_content_updated_at ON help_content(updated_at);

-- 创建全文搜索索引
CREATE INDEX idx_help_content_title_gin ON help_content USING gin(to_tsvector('english', title));
CREATE INDEX idx_help_content_content_gin ON help_content USING gin(to_tsvector('english', content));
CREATE INDEX idx_help_content_keywords_gin ON help_content USING gin(to_tsvector('english', keywords));

-- 4. 帮助内容标签关联表
CREATE TABLE help_content_tag (
    content_id BIGINT NOT NULL,
    tag_id BIGINT NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (content_id, tag_id),
    CONSTRAINT fk_help_content_tag_content FOREIGN KEY (content_id) REFERENCES help_content(id) ON DELETE CASCADE,
    CONSTRAINT fk_help_content_tag_tag FOREIGN KEY (tag_id) REFERENCES help_tag(id) ON DELETE CASCADE
);

-- 5. 帮助附件表
CREATE TABLE help_attachment (
    id BIGSERIAL PRIMARY KEY,
    content_id BIGINT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    content_type VARCHAR(100),
    file_size BIGINT,
    type VARCHAR(20) NOT NULL,
    description VARCHAR(500),
    download_count BIGINT NOT NULL DEFAULT 0,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    uploader VARCHAR(100),
    uploader_id BIGINT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_help_attachment_content FOREIGN KEY (content_id) REFERENCES help_content(id) ON DELETE CASCADE,
    CONSTRAINT chk_help_attachment_type CHECK (type IN ('IMAGE', 'DOCUMENT', 'VIDEO', 'AUDIO', 'ARCHIVE', 'OTHER'))
);

-- 创建附件表索引
CREATE INDEX idx_help_attachment_content_id ON help_attachment(content_id);
CREATE INDEX idx_help_attachment_type ON help_attachment(type);
CREATE INDEX idx_help_attachment_uploader_id ON help_attachment(uploader_id);
CREATE INDEX idx_help_attachment_deleted ON help_attachment(is_deleted);

-- 6. 帮助反馈表
CREATE TABLE help_feedback (
    id BIGSERIAL PRIMARY KEY,
    content_id BIGINT NOT NULL,
    type VARCHAR(20) NOT NULL,
    rating INTEGER,
    content VARCHAR(1000),
    contact VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    reply VARCHAR(1000),
    replier VARCHAR(100),
    replier_id BIGINT,
    replied_at TIMESTAMP,
    is_anonymous BOOLEAN NOT NULL DEFAULT FALSE,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    submitter VARCHAR(100),
    submitter_id BIGINT,
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_help_feedback_content FOREIGN KEY (content_id) REFERENCES help_content(id) ON DELETE CASCADE,
    CONSTRAINT chk_help_feedback_type CHECK (type IN ('LIKE', 'DISLIKE', 'COMMENT', 'SUGGESTION', 'BUG_REPORT', 'CONTENT_ERROR', 'IMPROVEMENT')),
    CONSTRAINT chk_help_feedback_status CHECK (status IN ('PENDING', 'PROCESSING', 'RESOLVED', 'REJECTED', 'CLOSED')),
    CONSTRAINT chk_help_feedback_rating CHECK (rating IS NULL OR (rating >= 1 AND rating <= 5))
);

-- 创建反馈表索引
CREATE INDEX idx_help_feedback_content_id ON help_feedback(content_id);
CREATE INDEX idx_help_feedback_type ON help_feedback(type);
CREATE INDEX idx_help_feedback_status ON help_feedback(status);
CREATE INDEX idx_help_feedback_submitter_id ON help_feedback(submitter_id);
CREATE INDEX idx_help_feedback_rating ON help_feedback(rating);
CREATE INDEX idx_help_feedback_deleted ON help_feedback(is_deleted);
CREATE INDEX idx_help_feedback_created_at ON help_feedback(created_at);

-- 7. 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表创建更新时间触发器
CREATE TRIGGER update_help_category_updated_at BEFORE UPDATE ON help_category FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_help_tag_updated_at BEFORE UPDATE ON help_tag FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_help_content_updated_at BEFORE UPDATE ON help_content FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_help_attachment_updated_at BEFORE UPDATE ON help_attachment FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_help_feedback_updated_at BEFORE UPDATE ON help_feedback FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 8. 插入初始数据

-- 插入根分类
INSERT INTO help_category (name, description, category_path, level, sort_order, icon) VALUES
('功能指南', '系统各功能模块的详细操作指南', '/功能指南', 0, 1, 'BookOutlined'),
('角色权限', '各用户角色的权限说明和管理', '/角色权限', 0, 2, 'UserOutlined'),
('行业知识', '天线行业相关的专业知识库', '/行业知识', 0, 3, 'DatabaseOutlined'),
('业务流程', '完整的业务操作流程指导', '/业务流程', 0, 4, 'ToolOutlined'),
('常见问题', '用户常遇问题的解答集合', '/常见问题', 0, 5, 'QuestionCircleOutlined'),
('视频教程', '直观的视频操作教程', '/视频教程', 0, 6, 'FileTextOutlined');

-- 插入子分类
INSERT INTO help_category (name, description, parent_id, category_path, level, sort_order) VALUES
('BOM管理', 'BOM创建、编辑、审核等功能说明', 1, '/功能指南/BOM管理', 1, 1),
('物料管理', '物料信息维护和管理功能', 1, '/功能指南/物料管理', 1, 2),
('库存管理', '库存查询、盘点、调拨等功能', 1, '/功能指南/库存管理', 1, 3),
('采购管理', '采购申请、订单、收货等流程', 1, '/功能指南/采购管理', 1, 4),
('成本分析', '成本计算和分析功能说明', 1, '/功能指南/成本分析', 1, 5);

-- 插入系统标签
INSERT INTO help_tag (name, description, type, color) VALUES
('新手入门', '适合新用户的基础教程', 'SYSTEM', 'green'),
('高级功能', '系统高级功能说明', 'SYSTEM', 'blue'),
('常用操作', '日常工作中的常用操作', 'SYSTEM', 'orange'),
('故障排除', '问题诊断和解决方案', 'SYSTEM', 'red'),
('最佳实践', '推荐的操作方法和经验', 'SYSTEM', 'purple'),
('更新说明', '系统更新和新功能介绍', 'SYSTEM', 'cyan');

-- 提交事务
COMMIT;
