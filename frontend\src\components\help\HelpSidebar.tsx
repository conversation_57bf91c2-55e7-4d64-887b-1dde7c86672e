import React, { useState } from 'react';
import {
  Menu,
  Input,
  Tree,
  Typography,
  Space,
  Tag,
  Divider,
  But<PERSON>,
  Tooltip,
  Badge
} from 'antd';
import {
  SearchOutlined,
  FolderOutlined,
  FolderOpenOutlined,
  FileTextOutlined,
  TagOutlined,
  HomeOutlined,
  QuestionCircleOutlined,
  BookOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { HelpCategory, HelpTag } from '../../types/help';

const { Search } = Input;
const { Text } = Typography;
const { TreeNode } = Tree;

interface HelpSidebarProps {
  categories: HelpCategory[];
  popularTags: HelpTag[];
  collapsed: boolean;
  selectedCategoryId?: number | null;
  onCategorySelect: (categoryId: number) => void;
  onSearch: (keyword: string) => void;
}

/**
 * 帮助手册侧边栏组件
 */
const HelpSidebar: React.FC<HelpSidebarProps> = ({
  categories,
  popularTags,
  collapsed,
  selectedCategoryId,
  onCategorySelect,
  onSearch
}) => {
  const [searchKeyword, setSearchKeyword] = useState('');
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    if (value.trim()) {
      onSearch(value.trim());
    }
  };

  // 处理分类选择
  const handleCategorySelect = (selectedKeys: React.Key[]) => {
    if (selectedKeys.length > 0) {
      const categoryId = Number(selectedKeys[0]);
      setSelectedKeys(selectedKeys);
      onCategorySelect(categoryId);
    }
  };

  // 处理展开/收起
  const handleExpand = (expandedKeys: React.Key[]) => {
    setExpandedKeys(expandedKeys);
  };

  // 渲染分类树节点
  const renderTreeNodes = (categories: HelpCategory[]): React.ReactNode => {
    return categories.map((category) => (
      <TreeNode
        key={category.id}
        title={
          <Space>
            <Text ellipsis style={{ maxWidth: collapsed ? 0 : 180 }}>
              {category.name}
            </Text>
            {category.contentCount && category.contentCount > 0 && (
              <Badge 
                count={category.contentCount} 
                size="small" 
                style={{ backgroundColor: '#f0f0f0', color: '#666' }}
              />
            )}
          </Space>
        }
        icon={
          category.children && category.children.length > 0 ? (
            expandedKeys.includes(category.id) ? (
              <FolderOpenOutlined />
            ) : (
              <FolderOutlined />
            )
          ) : (
            <FileTextOutlined />
          )
        }
      >
        {category.children && category.children.length > 0 && 
         renderTreeNodes(category.children)}
      </TreeNode>
    ));
  };

  // 渲染快捷菜单
  const renderQuickMenu = () => {
    const menuItems = [
      {
        key: 'home',
        icon: <HomeOutlined />,
        label: '首页',
        onClick: () => window.location.href = '/help'
      },
      {
        key: 'functions',
        icon: <BookOutlined />,
        label: '功能指南',
        onClick: () => onCategorySelect(1) // 假设功能指南分类ID为1
      },
      {
        key: 'faq',
        icon: <QuestionCircleOutlined />,
        label: '常见问题',
        onClick: () => onCategorySelect(2) // 假设FAQ分类ID为2
      },
      {
        key: 'settings',
        icon: <SettingOutlined />,
        label: '系统设置',
        onClick: () => onCategorySelect(3) // 假设系统设置分类ID为3
      }
    ];

    return (
      <Menu
        mode="inline"
        inlineCollapsed={collapsed}
        style={{ border: 'none' }}
        items={menuItems.map(item => ({
          key: item.key,
          icon: item.icon,
          label: item.label,
          onClick: item.onClick
        }))}
      />
    );
  };

  // 渲染热门标签
  const renderPopularTags = () => {
    if (collapsed || popularTags.length === 0) {
      return null;
    }

    return (
      <div style={{ padding: '16px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text strong>
            <TagOutlined /> 热门标签
          </Text>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
            {popularTags.slice(0, 10).map((tag) => (
              <Tag
                key={tag.id}
                color={tag.color || 'blue'}
                style={{ cursor: 'pointer', margin: '2px' }}
                onClick={() => onSearch(`tag:${tag.name}`)}
              >
                {tag.name}
                {tag.usageCount > 0 && (
                  <span style={{ marginLeft: '4px', opacity: 0.7 }}>
                    ({tag.usageCount})
                  </span>
                )}
              </Tag>
            ))}
          </div>
        </Space>
      </div>
    );
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo 区域 */}
      <div
        style={{
          padding: '16px',
          borderBottom: '1px solid #f0f0f0',
          textAlign: 'center'
        }}
      >
        {!collapsed ? (
          <Space direction="vertical" size="small">
            <BookOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
            <Text strong>帮助手册</Text>
          </Space>
        ) : (
          <Tooltip title="帮助手册" placement="right">
            <BookOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
          </Tooltip>
        )}
      </div>

      {/* 搜索框 */}
      {!collapsed && (
        <div style={{ padding: '16px' }}>
          <Search
            placeholder="搜索帮助内容..."
            allowClear
            enterButton={<SearchOutlined />}
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            onSearch={handleSearch}
          />
        </div>
      )}

      {/* 快捷菜单 */}
      <div style={{ borderBottom: '1px solid #f0f0f0' }}>
        {renderQuickMenu()}
      </div>

      {/* 分类树 */}
      <div style={{ flex: 1, overflow: 'auto', padding: '8px' }}>
        {!collapsed && (
          <div style={{ padding: '8px 16px' }}>
            <Text strong>内容分类</Text>
          </div>
        )}
        
        <Tree
          showIcon
          showLine={{ showLeafIcon: false }}
          expandedKeys={expandedKeys}
          selectedKeys={selectedKeys}
          onExpand={handleExpand}
          onSelect={handleCategorySelect}
          style={{ background: 'transparent' }}
        >
          {renderTreeNodes(categories)}
        </Tree>
      </div>

      {/* 热门标签 */}
      {!collapsed && (
        <>
          <Divider style={{ margin: '8px 0' }} />
          {renderPopularTags()}
        </>
      )}

      {/* 底部操作 */}
      {!collapsed && (
        <div style={{ padding: '16px', borderTop: '1px solid #f0f0f0' }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button
              type="link"
              size="small"
              icon={<QuestionCircleOutlined />}
              style={{ padding: 0, height: 'auto' }}
              onClick={() => onSearch('帮助')}
            >
              使用帮助
            </Button>
            <Button
              type="link"
              size="small"
              icon={<SettingOutlined />}
              style={{ padding: 0, height: 'auto' }}
            >
              设置
            </Button>
          </Space>
        </div>
      )}
    </div>
  );
};

export default HelpSidebar;
