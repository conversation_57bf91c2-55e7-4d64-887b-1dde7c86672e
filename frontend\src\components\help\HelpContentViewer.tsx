import React, { useState, useEffect } from 'react';
import {
  Typo<PERSON>,
  Space,
  Button,
  Tag,
  Divider,
  Rate,
  Card,
  Avatar,
  List,
  Modal,
  Form,
  Input,
  Select,
  message,
  Affix,
  Anchor,
  BackTop
} from 'antd';
import {
  LikeOutlined,
  LikeFilled,
  EyeOutlined,
  ShareAltOutlined,
  PrinterOutlined,
  BookmarkOutlined,
  BookmarkFilled,
  ClockCircleOutlined,
  UserOutlined,
  TagOutlined,
  MessageOutlined,
  EditOutlined
} from '@ant-design/icons';
import { HelpContent, FeedbackForm, FeedbackType } from '../../types/help';
import { useAppDispatch } from '../../hooks/redux';
import { likeContent, submitFeedback } from '../../store/slices/helpSlice';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

interface HelpContentViewerProps {
  content: HelpContent;
  onCategorySelect: (categoryId: number) => void;
  onContentSelect: (contentId: number) => void;
}

/**
 * 帮助内容查看器组件
 */
const HelpContentViewer: React.FC<HelpContentViewerProps> = ({
  content,
  onCategorySelect,
  onContentSelect
}) => {
  const dispatch = useAppDispatch();
  const [liked, setLiked] = useState(false);
  const [bookmarked, setBookmarked] = useState(false);
  const [feedbackVisible, setFeedbackVisible] = useState(false);
  const [feedbackForm] = Form.useForm();

  // 处理点赞
  const handleLike = () => {
    if (!liked) {
      dispatch(likeContent(content.id));
      setLiked(true);
      message.success('点赞成功');
    }
  };

  // 处理收藏
  const handleBookmark = () => {
    setBookmarked(!bookmarked);
    message.success(bookmarked ? '取消收藏' : '收藏成功');
  };

  // 处理分享
  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: content.title,
        text: content.summary || content.content.substring(0, 100),
        url: window.location.href
      });
    } else {
      navigator.clipboard.writeText(window.location.href);
      message.success('链接已复制到剪贴板');
    }
  };

  // 处理打印
  const handlePrint = () => {
    window.print();
  };

  // 提交反馈
  const handleSubmitFeedback = async (values: any) => {
    try {
      const feedback: FeedbackForm = {
        contentId: content.id,
        type: values.type,
        rating: values.rating,
        content: values.content,
        contact: values.contact,
        anonymous: values.anonymous || false
      };

      await dispatch(submitFeedback(feedback));
      message.success('反馈提交成功');
      setFeedbackVisible(false);
      feedbackForm.resetFields();
    } catch (error) {
      message.error('反馈提交失败');
    }
  };

  // 生成目录
  const generateTOC = () => {
    const headings = content.content.match(/<h[1-6][^>]*>.*?<\/h[1-6]>/gi);
    if (!headings || headings.length === 0) return null;

    const tocItems = headings.map((heading, index) => {
      const level = parseInt(heading.match(/<h([1-6])/)?.[1] || '1');
      const text = heading.replace(/<[^>]*>/g, '');
      const id = `heading-${index}`;
      
      return {
        key: id,
        href: `#${id}`,
        title: text,
        level
      };
    });

    return (
      <Card title="目录" size="small" style={{ marginBottom: '24px' }}>
        <Anchor
          affix={false}
          items={tocItems}
          onClick={(e, link) => {
            e.preventDefault();
            const element = document.getElementById(link.href.substring(1));
            if (element) {
              element.scrollIntoView({ behavior: 'smooth' });
            }
          }}
        />
      </Card>
    );
  };

  // 渲染内容（处理HTML）
  const renderContent = () => {
    // 这里应该使用安全的HTML渲染，比如DOMPurify
    // 为了演示，我们简单处理
    let processedContent = content.content;
    
    // 添加标题ID用于目录跳转
    processedContent = processedContent.replace(
      /<h([1-6])([^>]*)>/gi,
      (match, level, attrs, offset) => {
        const index = (content.content.substring(0, offset).match(/<h[1-6]/gi) || []).length;
        return `<h${level}${attrs} id="heading-${index}">`;
      }
    );

    return (
      <div 
        dangerouslySetInnerHTML={{ __html: processedContent }}
        style={{ 
          lineHeight: '1.8',
          fontSize: '14px'
        }}
      />
    );
  };

  return (
    <div>
      {/* 内容头部 */}
      <div style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* 标题和元信息 */}
          <div>
            <Title level={2} style={{ marginBottom: '8px' }}>
              {content.title}
            </Title>
            
            <Space wrap>
              <Tag color="blue">{content.type}</Tag>
              {content.featured && <Tag color="gold">特色</Tag>}
              {content.tags?.map(tag => (
                <Tag key={tag.id} color={tag.color}>
                  {tag.name}
                </Tag>
              ))}
            </Space>
          </div>

          {/* 摘要 */}
          {content.summary && (
            <Card size="small" style={{ background: '#f9f9f9' }}>
              <Paragraph style={{ margin: 0, fontStyle: 'italic' }}>
                {content.summary}
              </Paragraph>
            </Card>
          )}

          {/* 元数据 */}
          <Space wrap>
            <Space>
              <UserOutlined />
              <Text type="secondary">{content.author || '系统管理员'}</Text>
            </Space>
            <Space>
              <ClockCircleOutlined />
              <Text type="secondary">更新于 {content.updatedAt}</Text>
            </Space>
            <Space>
              <EyeOutlined />
              <Text type="secondary">{content.viewCount} 次浏览</Text>
            </Space>
            <Space>
              <LikeOutlined />
              <Text type="secondary">{content.likeCount} 次点赞</Text>
            </Space>
          </Space>

          {/* 操作按钮 */}
          <Space wrap>
            <Button
              type={liked ? 'primary' : 'default'}
              icon={liked ? <LikeFilled /> : <LikeOutlined />}
              onClick={handleLike}
            >
              {liked ? '已点赞' : '点赞'} ({content.likeCount})
            </Button>
            
            <Button
              type={bookmarked ? 'primary' : 'default'}
              icon={bookmarked ? <BookmarkFilled /> : <BookmarkOutlined />}
              onClick={handleBookmark}
            >
              {bookmarked ? '已收藏' : '收藏'}
            </Button>
            
            <Button icon={<ShareAltOutlined />} onClick={handleShare}>
              分享
            </Button>
            
            <Button icon={<PrinterOutlined />} onClick={handlePrint}>
              打印
            </Button>
            
            <Button 
              icon={<MessageOutlined />} 
              onClick={() => setFeedbackVisible(true)}
            >
              反馈
            </Button>
          </Space>
        </Space>
      </div>

      <Divider />

      {/* 主要内容区域 */}
      <div style={{ display: 'flex', gap: '24px' }}>
        {/* 左侧目录（桌面端） */}
        <div style={{ width: '200px', display: window.innerWidth > 768 ? 'block' : 'none' }}>
          <Affix offsetTop={80}>
            {generateTOC()}
          </Affix>
        </div>

        {/* 主要内容 */}
        <div style={{ flex: 1, minWidth: 0 }}>
          {renderContent()}
        </div>
      </div>

      {/* 反馈模态框 */}
      <Modal
        title="提交反馈"
        open={feedbackVisible}
        onCancel={() => setFeedbackVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={feedbackForm}
          layout="vertical"
          onFinish={handleSubmitFeedback}
        >
          <Form.Item
            name="type"
            label="反馈类型"
            rules={[{ required: true, message: '请选择反馈类型' }]}
          >
            <Select placeholder="请选择反馈类型">
              <Option value={FeedbackType.COMMENT}>评论</Option>
              <Option value={FeedbackType.SUGGESTION}>建议</Option>
              <Option value={FeedbackType.BUG_REPORT}>错误报告</Option>
              <Option value={FeedbackType.CONTENT_ERROR}>内容错误</Option>
              <Option value={FeedbackType.IMPROVEMENT}>改进建议</Option>
            </Select>
          </Form.Item>

          <Form.Item name="rating" label="评分">
            <Rate />
          </Form.Item>

          <Form.Item
            name="content"
            label="反馈内容"
            rules={[{ required: true, message: '请输入反馈内容' }]}
          >
            <TextArea
              rows={4}
              placeholder="请详细描述您的反馈..."
              maxLength={1000}
              showCount
            />
          </Form.Item>

          <Form.Item name="contact" label="联系方式（可选）">
            <Input placeholder="邮箱或电话，便于我们联系您" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                提交反馈
              </Button>
              <Button onClick={() => setFeedbackVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 回到顶部 */}
      <BackTop />
    </div>
  );
};

export default HelpContentViewer;
