package com.linkboms.controller.help;

import com.linkboms.entity.help.HelpContent;
import com.linkboms.service.help.HelpContentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 帮助内容控制器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/help/content")
@RequiredArgsConstructor
@Tag(name = "帮助内容管理", description = "帮助手册内容的增删改查和管理功能")
public class HelpContentController {

    private final HelpContentService helpContentService;

    @PostMapping
    @Operation(summary = "创建帮助内容", description = "创建新的帮助手册内容")
    public ResponseEntity<HelpContent> createContent(
            @Valid @RequestBody HelpContent content) {
        log.info("创建帮助内容请求: {}", content.getTitle());
        
        HelpContent createdContent = helpContentService.createContent(content);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdContent);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新帮助内容", description = "更新指定ID的帮助内容")
    public ResponseEntity<HelpContent> updateContent(
            @Parameter(description = "内容ID") @PathVariable Long id,
            @Valid @RequestBody HelpContent content) {
        log.info("更新帮助内容请求: {}", id);
        
        HelpContent updatedContent = helpContentService.updateContent(id, content);
        return ResponseEntity.ok(updatedContent);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取帮助内容", description = "根据ID获取帮助内容详情")
    public ResponseEntity<HelpContent> getContent(
            @Parameter(description = "内容ID") @PathVariable Long id) {
        log.info("获取帮助内容请求: {}", id);
        
        HelpContent content = helpContentService.getContentById(id);
        
        // 增加浏览量
        helpContentService.incrementViewCount(id);
        
        return ResponseEntity.ok(content);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除帮助内容", description = "软删除指定ID的帮助内容")
    public ResponseEntity<Void> deleteContent(
            @Parameter(description = "内容ID") @PathVariable Long id) {
        log.info("删除帮助内容请求: {}", id);
        
        helpContentService.deleteContent(id);
        return ResponseEntity.noContent().build();
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除帮助内容", description = "批量软删除多个帮助内容")
    public ResponseEntity<Void> batchDeleteContent(
            @RequestBody List<Long> ids) {
        log.info("批量删除帮助内容请求: {}", ids);
        
        helpContentService.batchDeleteContent(ids);
        return ResponseEntity.noContent().build();
    }

    @PutMapping("/{id}/restore")
    @Operation(summary = "恢复帮助内容", description = "恢复已删除的帮助内容")
    public ResponseEntity<Void> restoreContent(
            @Parameter(description = "内容ID") @PathVariable Long id) {
        log.info("恢复帮助内容请求: {}", id);
        
        helpContentService.restoreContent(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping
    @Operation(summary = "分页查询帮助内容", description = "分页获取帮助内容列表")
    public ResponseEntity<Page<HelpContent>> getContentList(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "updatedAt") String sort,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String direction) {
        
        Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) 
            ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));
        
        Page<HelpContent> contentPage = helpContentService.getContentList(pageable);
        return ResponseEntity.ok(contentPage);
    }

    @GetMapping("/type/{type}")
    @Operation(summary = "按类型查询帮助内容", description = "根据内容类型分页查询帮助内容")
    public ResponseEntity<Page<HelpContent>> getContentByType(
            @Parameter(description = "内容类型") @PathVariable HelpContent.ContentType type,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "updatedAt"));
        Page<HelpContent> contentPage = helpContentService.getContentByType(type, pageable);
        return ResponseEntity.ok(contentPage);
    }

    @GetMapping("/category/{categoryId}")
    @Operation(summary = "按分类查询帮助内容", description = "根据分类ID分页查询帮助内容")
    public ResponseEntity<Page<HelpContent>> getContentByCategory(
            @Parameter(description = "分类ID") @PathVariable Long categoryId,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "updatedAt"));
        Page<HelpContent> contentPage = helpContentService.getContentByCategory(categoryId, pageable);
        return ResponseEntity.ok(contentPage);
    }

    @GetMapping("/status/{status}")
    @Operation(summary = "按状态查询帮助内容", description = "根据内容状态分页查询帮助内容")
    public ResponseEntity<Page<HelpContent>> getContentByStatus(
            @Parameter(description = "内容状态") @PathVariable HelpContent.ContentStatus status,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "updatedAt"));
        Page<HelpContent> contentPage = helpContentService.getContentByStatus(status, pageable);
        return ResponseEntity.ok(contentPage);
    }

    @GetMapping("/author/{author}")
    @Operation(summary = "按作者查询帮助内容", description = "根据作者分页查询帮助内容")
    public ResponseEntity<Page<HelpContent>> getContentByAuthor(
            @Parameter(description = "作者") @PathVariable String author,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "updatedAt"));
        Page<HelpContent> contentPage = helpContentService.getContentByAuthor(author, pageable);
        return ResponseEntity.ok(contentPage);
    }

    @GetMapping("/search")
    @Operation(summary = "搜索帮助内容", description = "根据关键词搜索帮助内容")
    public ResponseEntity<Page<HelpContent>> searchContent(
            @Parameter(description = "搜索关键词") @RequestParam String keyword,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "updatedAt"));
        Page<HelpContent> contentPage = helpContentService.searchContent(keyword, pageable);
        return ResponseEntity.ok(contentPage);
    }

    @GetMapping("/search/advanced")
    @Operation(summary = "高级搜索帮助内容", description = "根据多个条件进行高级搜索")
    public ResponseEntity<Page<HelpContent>> advancedSearchContent(
            @Parameter(description = "搜索关键词") @RequestParam(required = false) String keyword,
            @Parameter(description = "内容类型") @RequestParam(required = false) HelpContent.ContentType type,
            @Parameter(description = "分类ID") @RequestParam(required = false) Long categoryId,
            @Parameter(description = "内容状态") @RequestParam(required = false) HelpContent.ContentStatus status,
            @Parameter(description = "作者") @RequestParam(required = false) String author,
            @Parameter(description = "开始日期") @RequestParam(required = false) 
                @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startDate,
            @Parameter(description = "结束日期") @RequestParam(required = false) 
                @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endDate,
            @Parameter(description = "标签列表") @RequestParam(required = false) List<String> tags,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "updatedAt"));
        Page<HelpContent> contentPage = helpContentService.advancedSearchContent(
            keyword, type, categoryId, status, author, startDate, endDate, tags, pageable);
        return ResponseEntity.ok(contentPage);
    }

    @GetMapping("/search/fulltext")
    @Operation(summary = "全文搜索帮助内容", description = "全文搜索帮助内容")
    public ResponseEntity<Page<HelpContent>> fullTextSearch(
            @Parameter(description = "搜索关键词") @RequestParam String keyword,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<HelpContent> contentPage = helpContentService.fullTextSearch(keyword, pageable);
        return ResponseEntity.ok(contentPage);
    }

    @GetMapping("/popular")
    @Operation(summary = "获取热门内容", description = "获取热门帮助内容列表")
    public ResponseEntity<List<HelpContent>> getPopularContent(
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") int limit) {
        
        List<HelpContent> popularContent = helpContentService.getPopularContent(limit);
        return ResponseEntity.ok(popularContent);
    }

    @GetMapping("/latest")
    @Operation(summary = "获取最新内容", description = "获取最新帮助内容列表")
    public ResponseEntity<List<HelpContent>> getLatestContent(
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") int limit) {
        
        List<HelpContent> latestContent = helpContentService.getLatestContent(limit);
        return ResponseEntity.ok(latestContent);
    }

    @GetMapping("/recommended")
    @Operation(summary = "获取推荐内容", description = "获取推荐帮助内容列表")
    public ResponseEntity<List<HelpContent>> getRecommendedContent(
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") int limit) {
        
        List<HelpContent> recommendedContent = helpContentService.getRecommendedContent(limit);
        return ResponseEntity.ok(recommendedContent);
    }

    @GetMapping("/featured")
    @Operation(summary = "获取特色内容", description = "获取特色帮助内容列表")
    public ResponseEntity<List<HelpContent>> getFeaturedContent(
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") int limit) {
        
        List<HelpContent> featuredContent = helpContentService.getFeaturedContent(limit);
        return ResponseEntity.ok(featuredContent);
    }

    @GetMapping("/pinned")
    @Operation(summary = "获取置顶内容", description = "获取置顶帮助内容列表")
    public ResponseEntity<List<HelpContent>> getPinnedContent() {
        List<HelpContent> pinnedContent = helpContentService.getPinnedContent();
        return ResponseEntity.ok(pinnedContent);
    }

    @GetMapping("/{id}/related")
    @Operation(summary = "获取相关内容", description = "获取与指定内容相关的帮助内容")
    public ResponseEntity<List<HelpContent>> getRelatedContent(
            @Parameter(description = "内容ID") @PathVariable Long id,
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "5") int limit) {
        
        List<HelpContent> relatedContent = helpContentService.getRelatedContent(id, limit);
        return ResponseEntity.ok(relatedContent);
    }

    @PostMapping("/{id}/like")
    @Operation(summary = "点赞内容", description = "为指定内容点赞")
    public ResponseEntity<Void> likeContent(
            @Parameter(description = "内容ID") @PathVariable Long id) {
        log.info("点赞帮助内容: {}", id);
        
        helpContentService.incrementLikeCount(id);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{id}/like")
    @Operation(summary = "取消点赞", description = "取消对指定内容的点赞")
    public ResponseEntity<Void> unlikeContent(
            @Parameter(description = "内容ID") @PathVariable Long id) {
        log.info("取消点赞帮助内容: {}", id);
        
        helpContentService.decrementLikeCount(id);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/publish")
    @Operation(summary = "发布内容", description = "发布指定的帮助内容")
    public ResponseEntity<Void> publishContent(
            @Parameter(description = "内容ID") @PathVariable Long id,
            @Parameter(description = "审核者") @RequestParam String reviewer) {
        log.info("发布帮助内容: {}, 审核者: {}", id, reviewer);
        
        helpContentService.publishContent(id, reviewer);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/unpublish")
    @Operation(summary = "撤回发布", description = "撤回已发布的帮助内容")
    public ResponseEntity<Void> unpublishContent(
            @Parameter(description = "内容ID") @PathVariable Long id) {
        log.info("撤回发布帮助内容: {}", id);
        
        helpContentService.unpublishContent(id);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/approve")
    @Operation(summary = "审核通过", description = "审核通过指定的帮助内容")
    public ResponseEntity<Void> approveContent(
            @Parameter(description = "内容ID") @PathVariable Long id,
            @Parameter(description = "审核者") @RequestParam String reviewer) {
        log.info("审核通过帮助内容: {}, 审核者: {}", id, reviewer);
        
        helpContentService.approveContent(id, reviewer);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/reject")
    @Operation(summary = "审核拒绝", description = "审核拒绝指定的帮助内容")
    public ResponseEntity<Void> rejectContent(
            @Parameter(description = "内容ID") @PathVariable Long id,
            @Parameter(description = "审核者") @RequestParam String reviewer,
            @Parameter(description = "拒绝原因") @RequestParam String reason) {
        log.info("审核拒绝帮助内容: {}, 审核者: {}, 原因: {}", id, reviewer, reason);
        
        helpContentService.rejectContent(id, reviewer, reason);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/featured")
    @Operation(summary = "设置特色状态", description = "设置内容的特色状态")
    public ResponseEntity<Void> setFeatured(
            @Parameter(description = "内容ID") @PathVariable Long id,
            @Parameter(description = "是否特色") @RequestParam boolean featured) {
        log.info("设置帮助内容特色状态: {}, 特色: {}", id, featured);
        
        helpContentService.setFeatured(id, featured);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/pinned")
    @Operation(summary = "设置置顶状态", description = "设置内容的置顶状态")
    public ResponseEntity<Void> setPinned(
            @Parameter(description = "内容ID") @PathVariable Long id,
            @Parameter(description = "是否置顶") @RequestParam boolean pinned) {
        log.info("设置帮助内容置顶状态: {}, 置顶: {}", id, pinned);
        
        helpContentService.setPinned(id, pinned);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/batch/status")
    @Operation(summary = "批量更新状态", description = "批量更新多个内容的状态")
    public ResponseEntity<Void> batchUpdateStatus(
            @RequestBody Map<String, Object> request) {
        @SuppressWarnings("unchecked")
        List<Long> ids = (List<Long>) request.get("ids");
        HelpContent.ContentStatus status = HelpContent.ContentStatus.valueOf(
            (String) request.get("status"));
        
        log.info("批量更新帮助内容状态: {}, 状态: {}", ids, status);
        
        helpContentService.batchUpdateStatus(ids, status);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/tags")
    @Operation(summary = "添加标签", description = "为内容添加标签")
    public ResponseEntity<Void> addTag(
            @Parameter(description = "内容ID") @PathVariable Long id,
            @Parameter(description = "标签名称") @RequestParam String tagName) {
        log.info("为帮助内容添加标签: {}, 标签: {}", id, tagName);
        
        helpContentService.addTag(id, tagName);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{id}/tags")
    @Operation(summary = "移除标签", description = "从内容中移除标签")
    public ResponseEntity<Void> removeTag(
            @Parameter(description = "内容ID") @PathVariable Long id,
            @Parameter(description = "标签名称") @RequestParam String tagName) {
        log.info("从帮助内容移除标签: {}, 标签: {}", id, tagName);
        
        helpContentService.removeTag(id, tagName);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/tags/batch")
    @Operation(summary = "批量添加标签", description = "为内容批量添加标签")
    public ResponseEntity<Void> batchAddTags(
            @Parameter(description = "内容ID") @PathVariable Long id,
            @RequestBody List<String> tagNames) {
        log.info("为帮助内容批量添加标签: {}, 标签: {}", id, tagNames);
        
        helpContentService.batchAddTags(id, tagNames);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/attachments")
    @Operation(summary = "上传附件", description = "为内容上传附件")
    public ResponseEntity<Void> uploadAttachment(
            @Parameter(description = "内容ID") @PathVariable Long id,
            @Parameter(description = "附件文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "附件描述") @RequestParam(required = false) String description) {
        log.info("为帮助内容上传附件: {}, 文件: {}", id, file.getOriginalFilename());
        
        helpContentService.uploadAttachment(id, file, description);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{id}/attachments/{attachmentId}")
    @Operation(summary = "删除附件", description = "删除内容的附件")
    public ResponseEntity<Void> deleteAttachment(
            @Parameter(description = "内容ID") @PathVariable Long id,
            @Parameter(description = "附件ID") @PathVariable Long attachmentId) {
        log.info("删除帮助内容附件: {}, 附件: {}", id, attachmentId);
        
        helpContentService.deleteAttachment(id, attachmentId);
        return ResponseEntity.noContent().build();
    }

    @PostMapping("/{id}/copy")
    @Operation(summary = "复制内容", description = "复制指定的帮助内容")
    public ResponseEntity<HelpContent> copyContent(
            @Parameter(description = "内容ID") @PathVariable Long id,
            @Parameter(description = "新标题") @RequestParam String newTitle) {
        log.info("复制帮助内容: {}, 新标题: {}", id, newTitle);
        
        HelpContent copiedContent = helpContentService.copyContent(id, newTitle);
        return ResponseEntity.status(HttpStatus.CREATED).body(copiedContent);
    }

    @GetMapping("/{id}/export")
    @Operation(summary = "导出内容", description = "导出指定格式的帮助内容")
    public ResponseEntity<byte[]> exportContent(
            @Parameter(description = "内容ID") @PathVariable Long id,
            @Parameter(description = "导出格式") @RequestParam(defaultValue = "pdf") String format) {
        log.info("导出帮助内容: {}, 格式: {}", id, format);
        
        byte[] exportData = helpContentService.exportContent(id, format);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "content_" + id + "." + format);
        
        return ResponseEntity.ok()
            .headers(headers)
            .body(exportData);
    }

    @PostMapping("/import")
    @Operation(summary = "导入内容", description = "从文件导入帮助内容")
    public ResponseEntity<List<HelpContent>> importContent(
            @Parameter(description = "导入文件") @RequestParam("file") MultipartFile file) {
        log.info("导入帮助内容: {}", file.getOriginalFilename());
        
        List<HelpContent> importedContent = helpContentService.importContent(file);
        return ResponseEntity.status(HttpStatus.CREATED).body(importedContent);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取内容统计", description = "获取帮助内容的统计信息")
    public ResponseEntity<Map<String, Object>> getContentStatistics() {
        Map<String, Object> statistics = helpContentService.getContentStatistics();
        return ResponseEntity.ok(statistics);
    }

    @GetMapping("/statistics/type")
    @Operation(summary = "按类型统计", description = "按内容类型获取统计信息")
    public ResponseEntity<Map<HelpContent.ContentType, Long>> getContentStatisticsByType() {
        Map<HelpContent.ContentType, Long> statistics = helpContentService.getContentStatisticsByType();
        return ResponseEntity.ok(statistics);
    }

    @GetMapping("/statistics/status")
    @Operation(summary = "按状态统计", description = "按内容状态获取统计信息")
    public ResponseEntity<Map<HelpContent.ContentStatus, Long>> getContentStatisticsByStatus() {
        Map<HelpContent.ContentStatus, Long> statistics = helpContentService.getContentStatisticsByStatus();
        return ResponseEntity.ok(statistics);
    }

    @GetMapping("/statistics/category")
    @Operation(summary = "按分类统计", description = "按分类获取统计信息")
    public ResponseEntity<Map<String, Long>> getContentStatisticsByCategory() {
        Map<String, Long> statistics = helpContentService.getContentStatisticsByCategory();
        return ResponseEntity.ok(statistics);
    }

    @GetMapping("/statistics/author")
    @Operation(summary = "按作者统计", description = "按作者获取统计信息")
    public ResponseEntity<Map<String, Long>> getContentStatisticsByAuthor() {
        Map<String, Long> statistics = helpContentService.getContentStatisticsByAuthor();
        return ResponseEntity.ok(statistics);
    }

    @GetMapping("/{id}/preview")
    @Operation(summary = "预览内容", description = "预览帮助内容")
    public ResponseEntity<String> previewContent(
            @Parameter(description = "内容ID") @PathVariable Long id) {
        String preview = helpContentService.previewContent(id);
        return ResponseEntity.ok(preview);
    }

    @PostMapping("/validate")
    @Operation(summary = "验证内容", description = "验证帮助内容的有效性")
    public ResponseEntity<List<String>> validateContent(
            @Valid @RequestBody HelpContent content) {
        List<String> errors = helpContentService.validateContent(content);
        return ResponseEntity.ok(errors);
    }

    @PostMapping("/cleanup")
    @Operation(summary = "清理过期内容", description = "清理指定天数前的过期内容")
    public ResponseEntity<Void> cleanupExpiredContent(
            @Parameter(description = "过期天数") @RequestParam(defaultValue = "365") int days) {
        log.info("清理过期帮助内容，天数: {}", days);
        
        helpContentService.cleanupExpiredContent(days);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/rebuild-index")
    @Operation(summary = "重建搜索索引", description = "重建帮助内容的搜索索引")
    public ResponseEntity<Void> rebuildSearchIndex() {
        log.info("重建帮助内容搜索索引");
        
        helpContentService.rebuildSearchIndex();
        return ResponseEntity.ok().build();
    }
}