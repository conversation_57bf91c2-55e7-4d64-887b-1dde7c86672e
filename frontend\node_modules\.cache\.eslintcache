[{"D:\\customerDemo\\Link-BOM-S\\frontend\\src\\index.tsx": "1", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\App.tsx": "2", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\hooks\\redux.ts": "3", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\authSlice.ts": "4", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\CoreBOMListPage.tsx": "5", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\CoreBOMEditPage.tsx": "6", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\OrderBOMViewPage.tsx": "7", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\OrderBOMCreatePage.tsx": "8", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\auth\\LoginPage.tsx": "9", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\CoreBOMViewPage.tsx": "10", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\dashboard\\DashboardPage.tsx": "11", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\OrderBOMDerivePage.tsx": "12", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\CoreBOMCreatePage.tsx": "13", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\OrderBOMListPage.tsx": "14", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\material\\MaterialEditPage.tsx": "15", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\material\\MaterialListPage.tsx": "16", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\material\\MaterialCreatePage.tsx": "17", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\purchase\\PurchaseListPage.tsx": "18", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\purchase\\MRPCalculationPage.tsx": "19", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\purchase\\PurchaseRequisitionPage.tsx": "20", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\InventoryReceivePage.tsx": "21", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\InventoryAdjustPage.tsx": "22", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\InventoryListPage.tsx": "23", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\InventoryIssuePage.tsx": "24", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\RemnantListPage.tsx": "25", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\MaintenancePage.tsx": "26", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\DeviceArchivePage.tsx": "27", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\ServiceBOMListPage.tsx": "28", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\cost\\CostReportsPage.tsx": "29", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\cost\\WasteTrackingPage.tsx": "30", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\cost\\CostAnalysisPage.tsx": "31", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\ecn\\ECNReviewPage.tsx": "32", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\ecn\\ECNCreatePage.tsx": "33", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\ecn\\ECNListPage.tsx": "34", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\RoleListPage.tsx": "35", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\PermissionListPage.tsx": "36", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\SystemConfigPage.tsx": "37", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\UserListPage.tsx": "38", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\bomSlice.ts": "39", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\index.ts": "40", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\mobile\\MobileInventoryPage.tsx": "41", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\constants\\index.ts": "42", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\reports\\ReportsPage.tsx": "43", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\authService.ts": "44", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\mobile\\MobilePage.tsx": "45", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\reports\\DashboardConfigPage.tsx": "46", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\mobile\\MobileScanPage.tsx": "47", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\layout\\MainLayout.tsx": "48", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\auth\\ProtectedRoute.tsx": "49", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\utils\\index.ts": "50", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\costSlice.ts": "51", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\serviceSlice.ts": "52", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\systemSlice.ts": "53", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\ecnSlice.ts": "54", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\purchaseSlice.ts": "55", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\inventorySlice.ts": "56", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\bomService.ts": "57", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\dashboardSlice.ts": "58", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\materialSlice.ts": "59", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\api.ts": "60", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\mockService.ts": "61", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\reportSlice.ts": "62", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\mobileSlice.ts": "63", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\purchase\\PurchaseOptimizationPage.tsx": "64", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\CuttingPlanPage.tsx": "65", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\cost\\StandardCostPage.tsx": "66", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\AsBuiltBOMPage.tsx": "67", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\SparePartsPage.tsx": "68", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\BatchTrackingPage.tsx": "69", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\AuditLogPage.tsx": "70", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\utils\\format.ts": "71", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\utils\\errorHandler.ts": "72", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\ErrorBoundary.tsx": "73", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\index.ts": "74", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\LoadingSpinner.tsx": "75", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\ConfirmDialog.tsx": "76", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\helpSlice.ts": "77", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\helpService.ts": "78", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\data\\helpData.ts": "79", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\help\\HelpPage.tsx": "80", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\HelpSidebar.tsx": "81", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\HelpNavigation.tsx": "82", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\WorkflowDiagram.tsx": "83", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\HelpSearch.tsx": "84", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\HelpContentViewer.tsx": "85", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\AdvancedSearch.tsx": "86", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\DiagramGallery.tsx": "87", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\HelpIndex.tsx": "88", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\MermaidDiagram.tsx": "89", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\MobileHelpNavigation.tsx": "90", "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\ResponsiveHelpLayout.tsx": "91"}, {"size": 274, "mtime": 1755518446419, "results": "92", "hashOfConfig": "93"}, {"size": 9571, "mtime": 1755671228341, "results": "94", "hashOfConfig": "93"}, {"size": 302, "mtime": 1755519336391, "results": "95", "hashOfConfig": "93"}, {"size": 4582, "mtime": 1755519350062, "results": "96", "hashOfConfig": "93"}, {"size": 31519, "mtime": 1755671228559, "results": "97", "hashOfConfig": "93"}, {"size": 13884, "mtime": 1755671228542, "results": "98", "hashOfConfig": "93"}, {"size": 21603, "mtime": 1755581011049, "results": "99", "hashOfConfig": "93"}, {"size": 22899, "mtime": 1755565866646, "results": "100", "hashOfConfig": "93"}, {"size": 10283, "mtime": 1755671228463, "results": "101", "hashOfConfig": "93"}, {"size": 13460, "mtime": 1755562238203, "results": "102", "hashOfConfig": "93"}, {"size": 13634, "mtime": 1755671228625, "results": "103", "hashOfConfig": "93"}, {"size": 20684, "mtime": 1755520159683, "results": "104", "hashOfConfig": "93"}, {"size": 23406, "mtime": 1755671228522, "results": "105", "hashOfConfig": "93"}, {"size": 21223, "mtime": 1755671228601, "results": "106", "hashOfConfig": "93"}, {"size": 13648, "mtime": 1755573231678, "results": "107", "hashOfConfig": "93"}, {"size": 36327, "mtime": 1755572839571, "results": "108", "hashOfConfig": "93"}, {"size": 11298, "mtime": 1755573217826, "results": "109", "hashOfConfig": "93"}, {"size": 12594, "mtime": 1755520828797, "results": "110", "hashOfConfig": "93"}, {"size": 20576, "mtime": 1755523135141, "results": "111", "hashOfConfig": "93"}, {"size": 34813, "mtime": 1755571791179, "results": "112", "hashOfConfig": "93"}, {"size": 15203, "mtime": 1755557741610, "results": "113", "hashOfConfig": "93"}, {"size": 27134, "mtime": 1755562150437, "results": "114", "hashOfConfig": "93"}, {"size": 13085, "mtime": 1755520668101, "results": "115", "hashOfConfig": "93"}, {"size": 17716, "mtime": 1755557839009, "results": "116", "hashOfConfig": "93"}, {"size": 17612, "mtime": 1755562223558, "results": "117", "hashOfConfig": "93"}, {"size": 25043, "mtime": 1755561862802, "results": "118", "hashOfConfig": "93"}, {"size": 29223, "mtime": 1755580736326, "results": "119", "hashOfConfig": "93"}, {"size": 28044, "mtime": 1755562324181, "results": "120", "hashOfConfig": "93"}, {"size": 17016, "mtime": 1755580759818, "results": "121", "hashOfConfig": "93"}, {"size": 23346, "mtime": 1755522967724, "results": "122", "hashOfConfig": "93"}, {"size": 19158, "mtime": 1755522887496, "results": "123", "hashOfConfig": "93"}, {"size": 16947, "mtime": 1755574373580, "results": "124", "hashOfConfig": "93"}, {"size": 23457, "mtime": 1755566270710, "results": "125", "hashOfConfig": "93"}, {"size": 19479, "mtime": 1755574804133, "results": "126", "hashOfConfig": "93"}, {"size": 20258, "mtime": 1755575685171, "results": "127", "hashOfConfig": "93"}, {"size": 20345, "mtime": 1755575798570, "results": "128", "hashOfConfig": "93"}, {"size": 16420, "mtime": 1755564532370, "results": "129", "hashOfConfig": "93"}, {"size": 24769, "mtime": 1755575576802, "results": "130", "hashOfConfig": "93"}, {"size": 9478, "mtime": 1755558105594, "results": "131", "hashOfConfig": "93"}, {"size": 1323, "mtime": 1755661160856, "results": "132", "hashOfConfig": "93"}, {"size": 24719, "mtime": 1755574967863, "results": "133", "hashOfConfig": "93"}, {"size": 7249, "mtime": 1755660948949, "results": "134", "hashOfConfig": "93"}, {"size": 15653, "mtime": 1755562269601, "results": "135", "hashOfConfig": "93"}, {"size": 1979, "mtime": 1755519494522, "results": "136", "hashOfConfig": "93"}, {"size": 11377, "mtime": 1755557630337, "results": "137", "hashOfConfig": "93"}, {"size": 14180, "mtime": 1755564589861, "results": "138", "hashOfConfig": "93"}, {"size": 2055, "mtime": 1755557657795, "results": "139", "hashOfConfig": "93"}, {"size": 10426, "mtime": 1755661498419, "results": "140", "hashOfConfig": "93"}, {"size": 1699, "mtime": 1755557454415, "results": "141", "hashOfConfig": "93"}, {"size": 8344, "mtime": 1755671228659, "results": "142", "hashOfConfig": "93"}, {"size": 4305, "mtime": 1755522368618, "results": "143", "hashOfConfig": "93"}, {"size": 5046, "mtime": 1755522780403, "results": "144", "hashOfConfig": "93"}, {"size": 3286, "mtime": 1755524346737, "results": "145", "hashOfConfig": "93"}, {"size": 750, "mtime": 1755519439483, "results": "146", "hashOfConfig": "93"}, {"size": 5785, "mtime": 1755523200785, "results": "147", "hashOfConfig": "93"}, {"size": 9392, "mtime": 1755558000151, "results": "148", "hashOfConfig": "93"}, {"size": 5254, "mtime": 1755519512471, "results": "149", "hashOfConfig": "93"}, {"size": 863, "mtime": 1755519453612, "results": "150", "hashOfConfig": "93"}, {"size": 2495, "mtime": 1755520540514, "results": "151", "hashOfConfig": "93"}, {"size": 7297, "mtime": 1755671228633, "results": "152", "hashOfConfig": "93"}, {"size": 11688, "mtime": 1755519525516, "results": "153", "hashOfConfig": "93"}, {"size": 5356, "mtime": 1755524378863, "results": "154", "hashOfConfig": "93"}, {"size": 5943, "mtime": 1755524179452, "results": "155", "hashOfConfig": "93"}, {"size": 18216, "mtime": 1755590155728, "results": "156", "hashOfConfig": "93"}, {"size": 25123, "mtime": 1755584942836, "results": "157", "hashOfConfig": "93"}, {"size": 22873, "mtime": 1755596815082, "results": "158", "hashOfConfig": "93"}, {"size": 26860, "mtime": 1755585650333, "results": "159", "hashOfConfig": "93"}, {"size": 25963, "mtime": 1755585992648, "results": "160", "hashOfConfig": "93"}, {"size": 24621, "mtime": 1755593153838, "results": "161", "hashOfConfig": "93"}, {"size": 19519, "mtime": 1755656370953, "results": "162", "hashOfConfig": "93"}, {"size": 351, "mtime": 1755596741541, "results": "163", "hashOfConfig": "93"}, {"size": 6702, "mtime": 1755671228648, "results": "164", "hashOfConfig": "93"}, {"size": 3071, "mtime": 1755671228394, "results": "165", "hashOfConfig": "93"}, {"size": 240, "mtime": 1755671228410, "results": "166", "hashOfConfig": "93"}, {"size": 1119, "mtime": 1755671228441, "results": "167", "hashOfConfig": "93"}, {"size": 4483, "mtime": 1755671228379, "results": "168", "hashOfConfig": "93"}, {"size": 8851, "mtime": 1755661137101, "results": "169", "hashOfConfig": "93"}, {"size": 8842, "mtime": 1755661101971, "results": "170", "hashOfConfig": "93"}, {"size": 16189, "mtime": 1755661783349, "results": "171", "hashOfConfig": "93"}, {"size": 11116, "mtime": 1755662445632, "results": "172", "hashOfConfig": "93"}, {"size": 9128, "mtime": 1755662554862, "results": "173", "hashOfConfig": "93"}, {"size": 5792, "mtime": 1755661198814, "results": "174", "hashOfConfig": "93"}, {"size": 9307, "mtime": 1755662429891, "results": "175", "hashOfConfig": "93"}, {"size": 8732, "mtime": 1755661234393, "results": "176", "hashOfConfig": "93"}, {"size": 15539, "mtime": 1755662215998, "results": "177", "hashOfConfig": "93"}, {"size": 10844, "mtime": 1755670880490, "results": "178", "hashOfConfig": "93"}, {"size": 10916, "mtime": 1755661751820, "results": "179", "hashOfConfig": "93"}, {"size": 11726, "mtime": 1755662459471, "results": "180", "hashOfConfig": "93"}, {"size": 9492, "mtime": 1755671088859, "results": "181", "hashOfConfig": "93"}, {"size": 9590, "mtime": 1755662498598, "results": "182", "hashOfConfig": "93"}, {"size": 4380, "mtime": 1755662033092, "results": "183", "hashOfConfig": "93"}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "jl5yua", {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 16, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\index.tsx", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\App.tsx", ["457"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\hooks\\redux.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\authSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\CoreBOMListPage.tsx", ["458", "459", "460", "461", "462", "463", "464", "465", "466", "467"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\CoreBOMEditPage.tsx", ["468", "469", "470", "471", "472"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\OrderBOMViewPage.tsx", ["473"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\OrderBOMCreatePage.tsx", ["474", "475", "476", "477", "478"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\auth\\LoginPage.tsx", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\CoreBOMViewPage.tsx", ["479", "480"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\dashboard\\DashboardPage.tsx", ["481", "482", "483", "484", "485", "486", "487", "488", "489"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\OrderBOMDerivePage.tsx", ["490", "491", "492", "493", "494"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\CoreBOMCreatePage.tsx", ["495", "496", "497", "498", "499", "500", "501", "502"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\bom\\OrderBOMListPage.tsx", ["503", "504", "505", "506", "507", "508", "509", "510"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\material\\MaterialEditPage.tsx", ["511"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\material\\MaterialListPage.tsx", ["512", "513", "514", "515", "516", "517"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\material\\MaterialCreatePage.tsx", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\purchase\\PurchaseListPage.tsx", ["518", "519", "520", "521"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\purchase\\MRPCalculationPage.tsx", ["522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\purchase\\PurchaseRequisitionPage.tsx", ["534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547", "548", "549"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\InventoryReceivePage.tsx", ["550", "551", "552", "553", "554", "555", "556"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\InventoryAdjustPage.tsx", ["557", "558", "559", "560"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\InventoryListPage.tsx", ["561", "562", "563", "564", "565", "566"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\InventoryIssuePage.tsx", ["567", "568", "569", "570", "571"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\RemnantListPage.tsx", ["572", "573", "574", "575", "576", "577", "578", "579"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\MaintenancePage.tsx", ["580", "581", "582"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\DeviceArchivePage.tsx", ["583", "584", "585", "586", "587", "588"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\ServiceBOMListPage.tsx", ["589", "590", "591", "592", "593", "594"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\cost\\CostReportsPage.tsx", ["595", "596", "597"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\cost\\WasteTrackingPage.tsx", ["598", "599", "600", "601", "602"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\cost\\CostAnalysisPage.tsx", ["603", "604", "605", "606", "607", "608"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\ecn\\ECNReviewPage.tsx", ["609", "610", "611", "612", "613", "614", "615", "616", "617", "618", "619", "620", "621", "622", "623", "624", "625", "626", "627", "628", "629", "630", "631", "632", "633"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\ecn\\ECNCreatePage.tsx", ["634", "635", "636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\ecn\\ECNListPage.tsx", ["649", "650", "651", "652", "653", "654", "655", "656"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\RoleListPage.tsx", ["657", "658", "659", "660", "661", "662", "663"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\PermissionListPage.tsx", ["664", "665", "666", "667", "668", "669", "670", "671", "672", "673"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\SystemConfigPage.tsx", ["674", "675", "676", "677", "678", "679", "680", "681", "682", "683"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\UserListPage.tsx", ["684", "685", "686", "687", "688", "689", "690"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\bomSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\index.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\mobile\\MobileInventoryPage.tsx", ["691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\constants\\index.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\reports\\ReportsPage.tsx", ["702", "703", "704", "705", "706", "707"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\authService.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\mobile\\MobilePage.tsx", ["708", "709", "710", "711"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\reports\\DashboardConfigPage.tsx", ["712", "713", "714", "715"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\mobile\\MobileScanPage.tsx", ["716"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\layout\\MainLayout.tsx", ["717"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\utils\\index.ts", ["718", "719", "720"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\costSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\serviceSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\systemSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\ecnSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\purchaseSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\inventorySlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\bomService.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\dashboardSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\materialSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\api.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\mockService.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\reportSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\mobileSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\purchase\\PurchaseOptimizationPage.tsx", ["721", "722", "723", "724", "725", "726"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\CuttingPlanPage.tsx", ["727", "728", "729", "730"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\cost\\StandardCostPage.tsx", ["731"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\AsBuiltBOMPage.tsx", ["732", "733", "734", "735", "736", "737", "738", "739", "740", "741", "742", "743"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\service\\SparePartsPage.tsx", ["744", "745", "746", "747", "748", "749", "750", "751", "752"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\inventory\\BatchTrackingPage.tsx", ["753", "754", "755", "756"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\system\\AuditLogPage.tsx", ["757", "758"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\utils\\format.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\utils\\errorHandler.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\ErrorBoundary.tsx", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\index.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\LoadingSpinner.tsx", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\ConfirmDialog.tsx", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\store\\slices\\helpSlice.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\services\\helpService.ts", ["759"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\data\\helpData.ts", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\pages\\help\\HelpPage.tsx", ["760", "761", "762", "763", "764", "765"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\HelpSidebar.tsx", ["766", "767", "768"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\HelpNavigation.tsx", ["769"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\WorkflowDiagram.tsx", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\HelpSearch.tsx", ["770"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\HelpContentViewer.tsx", ["771", "772", "773", "774"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\AdvancedSearch.tsx", ["775", "776", "777", "778"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\DiagramGallery.tsx", ["779", "780", "781", "782"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\HelpIndex.tsx", ["783", "784", "785", "786", "787"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\MermaidDiagram.tsx", ["788", "789"], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\MobileHelpNavigation.tsx", [], [], "D:\\customerDemo\\Link-BOM-S\\frontend\\src\\components\\help\\ResponsiveHelpLayout.tsx", [], [], {"ruleId": "790", "severity": 1, "message": "791", "line": 93, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 93, "endColumn": 26}, {"ruleId": "790", "severity": 1, "message": "794", "line": 1, "column": 38, "nodeType": "792", "messageId": "793", "endLine": 1, "endColumn": 45}, {"ruleId": "790", "severity": 1, "message": "795", "line": 31, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 31, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "796", "line": 43, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 43, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "797", "line": 55, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 55, "endColumn": 23}, {"ruleId": "790", "severity": 1, "message": "798", "line": 64, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 64, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "799", "line": 69, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 69, "endColumn": 20}, {"ruleId": "800", "severity": 1, "message": "801", "line": 90, "column": 6, "nodeType": "802", "endLine": 90, "endColumn": 76, "suggestions": "803"}, {"ruleId": "800", "severity": 1, "message": "804", "line": 98, "column": 6, "nodeType": "802", "endLine": 98, "endColumn": 72, "suggestions": "805"}, {"ruleId": "790", "severity": 1, "message": "806", "line": 194, "column": 13, "nodeType": "792", "messageId": "793", "endLine": 194, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "806", "line": 247, "column": 13, "nodeType": "792", "messageId": "793", "endLine": 247, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "807", "line": 35, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 35, "endColumn": 20}, {"ruleId": "790", "severity": 1, "message": "797", "line": 36, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 36, "endColumn": 23}, {"ruleId": "790", "severity": 1, "message": "808", "line": 37, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 37, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "809", "line": 37, "column": 24, "nodeType": "792", "messageId": "793", "endLine": 37, "endColumn": 33}, {"ruleId": "790", "severity": 1, "message": "806", "line": 98, "column": 13, "nodeType": "792", "messageId": "793", "endLine": 98, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "810", "line": 436, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 436, "endColumn": 21}, {"ruleId": "790", "severity": 1, "message": "811", "line": 21, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 21, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "812", "line": 22, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 22, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "795", "line": 30, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 30, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "813", "line": 32, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 32, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "814", "line": 667, "column": 29, "nodeType": "792", "messageId": "793", "endLine": 667, "endColumn": 33}, {"ruleId": "790", "severity": 1, "message": "815", "line": 16, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 16, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "816", "line": 48, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 48, "endColumn": 21}, {"ruleId": "790", "severity": 1, "message": "817", "line": 10, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 10, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "818", "line": 15, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 15, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "819", "line": 17, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 17, "endColumn": 7}, {"ruleId": "790", "severity": 1, "message": "820", "line": 20, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 20, "endColumn": 18}, {"ruleId": "790", "severity": 1, "message": "821", "line": 23, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 23, "endColumn": 23}, {"ruleId": "790", "severity": 1, "message": "822", "line": 24, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 24, "endColumn": 16}, {"ruleId": "790", "severity": 1, "message": "823", "line": 28, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 28, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "824", "line": 32, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 32, "endColumn": 12}, {"ruleId": "790", "severity": 1, "message": "825", "line": 33, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 33, "endColumn": 7}, {"ruleId": "790", "severity": 1, "message": "826", "line": 17, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 17, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "827", "line": 20, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 20, "endColumn": 6}, {"ruleId": "790", "severity": 1, "message": "828", "line": 27, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 27, "endColumn": 21}, {"ruleId": "790", "severity": 1, "message": "829", "line": 28, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 28, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "830", "line": 29, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 29, "endColumn": 21}, {"ruleId": "790", "severity": 1, "message": "815", "line": 21, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 21, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "831", "line": 29, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 29, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "832", "line": 30, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 30, "endColumn": 13}, {"ruleId": "790", "severity": 1, "message": "833", "line": 31, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 31, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "834", "line": 37, "column": 18, "nodeType": "792", "messageId": "793", "endLine": 37, "endColumn": 37}, {"ruleId": "790", "severity": 1, "message": "797", "line": 38, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 38, "endColumn": 23}, {"ruleId": "790", "severity": 1, "message": "808", "line": 39, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 39, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "809", "line": 39, "column": 24, "nodeType": "792", "messageId": "793", "endLine": 39, "endColumn": 33}, {"ruleId": "790", "severity": 1, "message": "812", "line": 20, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 20, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "795", "line": 27, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 27, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "835", "line": 39, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 39, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "797", "line": 47, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 47, "endColumn": 23}, {"ruleId": "790", "severity": 1, "message": "836", "line": 56, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 56, "endColumn": 20}, {"ruleId": "790", "severity": 1, "message": "837", "line": 65, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 65, "endColumn": 22}, {"ruleId": "800", "severity": 1, "message": "801", "line": 70, "column": 6, "nodeType": "802", "endLine": 70, "endColumn": 92, "suggestions": "838"}, {"ruleId": "790", "severity": 1, "message": "806", "line": 230, "column": 13, "nodeType": "792", "messageId": "793", "endLine": 230, "endColumn": 19}, {"ruleId": "800", "severity": 1, "message": "839", "line": 64, "column": 6, "nodeType": "802", "endLine": 64, "endColumn": 10, "suggestions": "840"}, {"ruleId": "790", "severity": 1, "message": "795", "line": 32, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 32, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "841", "line": 41, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 41, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "796", "line": 44, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 44, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "842", "line": 64, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 64, "endColumn": 20}, {"ruleId": "800", "severity": 1, "message": "801", "line": 90, "column": 6, "nodeType": "802", "endLine": 90, "endColumn": 94, "suggestions": "843"}, {"ruleId": "790", "severity": 1, "message": "806", "line": 150, "column": 13, "nodeType": "792", "messageId": "793", "endLine": 150, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "844", "line": 29, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 29, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "845", "line": 32, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 32, "endColumn": 28}, {"ruleId": "790", "severity": 1, "message": "846", "line": 49, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 49, "endColumn": 25}, {"ruleId": "800", "severity": 1, "message": "847", "line": 59, "column": 6, "nodeType": "802", "endLine": 59, "endColumn": 51, "suggestions": "848"}, {"ruleId": "790", "severity": 1, "message": "818", "line": 8, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 8, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "812", "line": 18, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 18, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "815", "line": 19, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 19, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "849", "line": 27, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 27, "endColumn": 21}, {"ruleId": "790", "severity": 1, "message": "850", "line": 29, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 29, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "851", "line": 32, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 32, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "852", "line": 44, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 44, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "853", "line": 65, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 65, "endColumn": 21}, {"ruleId": "790", "severity": 1, "message": "854", "line": 65, "column": 23, "nodeType": "792", "messageId": "793", "endLine": 65, "endColumn": 30}, {"ruleId": "790", "severity": 1, "message": "855", "line": 71, "column": 26, "nodeType": "792", "messageId": "793", "endLine": 71, "endColumn": 43}, {"ruleId": "790", "severity": 1, "message": "856", "line": 73, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 73, "endColumn": 21}, {"ruleId": "790", "severity": 1, "message": "857", "line": 73, "column": 23, "nodeType": "792", "messageId": "793", "endLine": 73, "endColumn": 37}, {"ruleId": "790", "severity": 1, "message": "811", "line": 24, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 24, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "812", "line": 25, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 25, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "858", "line": 26, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 26, "endColumn": 8}, {"ruleId": "790", "severity": 1, "message": "859", "line": 28, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 28, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "817", "line": 29, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 29, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "826", "line": 31, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 31, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "795", "line": 38, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 38, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "860", "line": 41, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 41, "endColumn": 20}, {"ruleId": "790", "severity": 1, "message": "851", "line": 43, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 43, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "835", "line": 45, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 45, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "821", "line": 46, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 46, "endColumn": 23}, {"ruleId": "790", "severity": 1, "message": "861", "line": 47, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 47, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "831", "line": 49, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 49, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "862", "line": 50, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 50, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "863", "line": 57, "column": 22, "nodeType": "792", "messageId": "793", "endLine": 57, "endColumn": 31}, {"ruleId": "790", "severity": 1, "message": "864", "line": 64, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 64, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "865", "line": 15, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 15, "endColumn": 14}, {"ruleId": "790", "severity": 1, "message": "858", "line": 19, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 19, "endColumn": 8}, {"ruleId": "790", "severity": 1, "message": "851", "line": 28, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 28, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "866", "line": 41, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 41, "endColumn": 13}, {"ruleId": "790", "severity": 1, "message": "867", "line": 45, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 45, "endColumn": 28}, {"ruleId": "800", "severity": 1, "message": "847", "line": 57, "column": 6, "nodeType": "802", "endLine": 57, "endColumn": 35, "suggestions": "868"}, {"ruleId": "790", "severity": 1, "message": "806", "line": 88, "column": 13, "nodeType": "792", "messageId": "793", "endLine": 88, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "812", "line": 23, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 23, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "869", "line": 38, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 38, "endColumn": 18}, {"ruleId": "790", "severity": 1, "message": "806", "line": 102, "column": 13, "nodeType": "792", "messageId": "793", "endLine": 102, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "806", "line": 119, "column": 13, "nodeType": "792", "messageId": "793", "endLine": 119, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "827", "line": 10, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 10, "endColumn": 6}, {"ruleId": "790", "severity": 1, "message": "795", "line": 21, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 21, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "870", "line": 23, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 23, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "871", "line": 35, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 35, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "872", "line": 45, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 45, "endColumn": 20}, {"ruleId": "800", "severity": 1, "message": "847", "line": 54, "column": 6, "nodeType": "802", "endLine": 54, "endColumn": 73, "suggestions": "873"}, {"ruleId": "790", "severity": 1, "message": "865", "line": 15, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 15, "endColumn": 14}, {"ruleId": "790", "severity": 1, "message": "869", "line": 30, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 30, "endColumn": 18}, {"ruleId": "790", "severity": 1, "message": "874", "line": 44, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 44, "endColumn": 26}, {"ruleId": "800", "severity": 1, "message": "847", "line": 56, "column": 6, "nodeType": "802", "endLine": 56, "endColumn": 35, "suggestions": "875"}, {"ruleId": "790", "severity": 1, "message": "806", "line": 87, "column": 13, "nodeType": "792", "messageId": "793", "endLine": 87, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "876", "line": 22, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 22, "endColumn": 13}, {"ruleId": "790", "severity": 1, "message": "877", "line": 26, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 26, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "869", "line": 34, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 34, "endColumn": 18}, {"ruleId": "790", "severity": 1, "message": "829", "line": 35, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 35, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "851", "line": 36, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 36, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "878", "line": 38, "column": 8, "nodeType": "792", "messageId": "793", "endLine": 38, "endColumn": 13}, {"ruleId": "790", "severity": 1, "message": "879", "line": 51, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 51, "endColumn": 19}, {"ruleId": "800", "severity": 1, "message": "847", "line": 64, "column": 6, "nodeType": "802", "endLine": 64, "endColumn": 55, "suggestions": "880"}, {"ruleId": "790", "severity": 1, "message": "881", "line": 34, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 34, "endColumn": 20}, {"ruleId": "790", "severity": 1, "message": "869", "line": 39, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 39, "endColumn": 18}, {"ruleId": "790", "severity": 1, "message": "806", "line": 99, "column": 13, "nodeType": "792", "messageId": "793", "endLine": 99, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "796", "line": 34, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 34, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "795", "line": 37, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 37, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "882", "line": 40, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 40, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "883", "line": 55, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 55, "endColumn": 25}, {"ruleId": "800", "severity": 1, "message": "847", "line": 71, "column": 6, "nodeType": "802", "endLine": 71, "endColumn": 35, "suggestions": "884"}, {"ruleId": "790", "severity": 1, "message": "806", "line": 108, "column": 13, "nodeType": "792", "messageId": "793", "endLine": 108, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "885", "line": 33, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 33, "endColumn": 20}, {"ruleId": "790", "severity": 1, "message": "886", "line": 34, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 34, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "851", "line": 40, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 40, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "869", "line": 41, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 41, "endColumn": 18}, {"ruleId": "790", "severity": 1, "message": "864", "line": 51, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 51, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "806", "line": 94, "column": 13, "nodeType": "792", "messageId": "793", "endLine": 94, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "887", "line": 33, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 33, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "888", "line": 48, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 48, "endColumn": 22}, {"ruleId": "800", "severity": 1, "message": "847", "line": 62, "column": 6, "nodeType": "802", "endLine": 62, "endColumn": 29, "suggestions": "889"}, {"ruleId": "790", "severity": 1, "message": "890", "line": 22, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 22, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "869", "line": 33, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 33, "endColumn": 18}, {"ruleId": "790", "severity": 1, "message": "891", "line": 65, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 65, "endColumn": 24}, {"ruleId": "800", "severity": 1, "message": "847", "line": 79, "column": 6, "nodeType": "802", "endLine": 79, "endColumn": 28, "suggestions": "892"}, {"ruleId": "790", "severity": 1, "message": "806", "line": 119, "column": 13, "nodeType": "792", "messageId": "793", "endLine": 119, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "890", "line": 18, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 18, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "893", "line": 23, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 23, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "807", "line": 52, "column": 26, "nodeType": "792", "messageId": "793", "endLine": 52, "endColumn": 36}, {"ruleId": "790", "severity": 1, "message": "894", "line": 60, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 60, "endColumn": 23}, {"ruleId": "790", "severity": 1, "message": "854", "line": 60, "column": 25, "nodeType": "792", "messageId": "793", "endLine": 60, "endColumn": 32}, {"ruleId": "800", "severity": 1, "message": "847", "line": 71, "column": 6, "nodeType": "802", "endLine": 71, "endColumn": 32, "suggestions": "895"}, {"ruleId": "790", "severity": 1, "message": "896", "line": 11, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 11, "endColumn": 6}, {"ruleId": "790", "severity": 1, "message": "897", "line": 12, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 12, "endColumn": 6}, {"ruleId": "790", "severity": 1, "message": "898", "line": 18, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 18, "endColumn": 8}, {"ruleId": "790", "severity": 1, "message": "890", "line": 19, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 19, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "859", "line": 21, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 21, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "899", "line": 24, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 24, "endColumn": 7}, {"ruleId": "790", "severity": 1, "message": "811", "line": 26, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 26, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "815", "line": 27, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 27, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "900", "line": 29, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 29, "endColumn": 13}, {"ruleId": "790", "severity": 1, "message": "877", "line": 34, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 34, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "901", "line": 35, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 35, "endColumn": 14}, {"ruleId": "790", "severity": 1, "message": "882", "line": 36, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 36, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "845", "line": 37, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 37, "endColumn": 28}, {"ruleId": "790", "severity": 1, "message": "813", "line": 41, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 41, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "796", "line": 42, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 42, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "902", "line": 44, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 44, "endColumn": 18}, {"ruleId": "790", "severity": 1, "message": "861", "line": 45, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 45, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "807", "line": 49, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 49, "endColumn": 20}, {"ruleId": "790", "severity": 1, "message": "903", "line": 50, "column": 15, "nodeType": "792", "messageId": "793", "endLine": 50, "endColumn": 27}, {"ruleId": "790", "severity": 1, "message": "866", "line": 55, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 55, "endColumn": 13}, {"ruleId": "790", "severity": 1, "message": "814", "line": 60, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 60, "endColumn": 14}, {"ruleId": "790", "severity": 1, "message": "904", "line": 68, "column": 27, "nodeType": "792", "messageId": "793", "endLine": 68, "endColumn": 45}, {"ruleId": "790", "severity": 1, "message": "905", "line": 69, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 69, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "906", "line": 69, "column": 21, "nodeType": "792", "messageId": "793", "endLine": 69, "endColumn": 33}, {"ruleId": "800", "severity": 1, "message": "907", "line": 74, "column": 6, "nodeType": "802", "endLine": 74, "endColumn": 10, "suggestions": "908"}, {"ruleId": "790", "severity": 1, "message": "826", "line": 20, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 20, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "811", "line": 22, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 22, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "815", "line": 23, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 23, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "859", "line": 25, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 25, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "877", "line": 33, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 33, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "901", "line": 34, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 34, "endColumn": 14}, {"ruleId": "790", "severity": 1, "message": "882", "line": 35, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 35, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "829", "line": 37, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 37, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "851", "line": 38, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 38, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "796", "line": 39, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 39, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "835", "line": 40, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 40, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "807", "line": 44, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 44, "endColumn": 20}, {"ruleId": "790", "severity": 1, "message": "909", "line": 45, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 45, "endColumn": 13}, {"ruleId": "790", "severity": 1, "message": "866", "line": 50, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 50, "endColumn": 13}, {"ruleId": "800", "severity": 1, "message": "910", "line": 75, "column": 6, "nodeType": "802", "endLine": 75, "endColumn": 8, "suggestions": "911"}, {"ruleId": "790", "severity": 1, "message": "912", "line": 13, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 13, "endColumn": 8}, {"ruleId": "790", "severity": 1, "message": "913", "line": 14, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 14, "endColumn": 7}, {"ruleId": "790", "severity": 1, "message": "890", "line": 27, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 27, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "795", "line": 31, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 31, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "862", "line": 38, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 38, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "844", "line": 45, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 45, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "914", "line": 53, "column": 16, "nodeType": "792", "messageId": "793", "endLine": 53, "endColumn": 20}, {"ruleId": "800", "severity": 1, "message": "915", "line": 76, "column": 6, "nodeType": "802", "endLine": 76, "endColumn": 103, "suggestions": "916"}, {"ruleId": "790", "severity": 1, "message": "890", "line": 23, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 23, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "795", "line": 30, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 30, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "917", "line": 42, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 42, "endColumn": 16}, {"ruleId": "790", "severity": 1, "message": "918", "line": 42, "column": 18, "nodeType": "792", "messageId": "793", "endLine": 42, "endColumn": 29}, {"ruleId": "790", "severity": 1, "message": "807", "line": 43, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 43, "endColumn": 20}, {"ruleId": "790", "severity": 1, "message": "864", "line": 57, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 57, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "806", "line": 181, "column": 13, "nodeType": "792", "messageId": "793", "endLine": 181, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "919", "line": 21, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 21, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "858", "line": 23, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 23, "endColumn": 8}, {"ruleId": "790", "severity": 1, "message": "795", "line": 29, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 29, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "920", "line": 36, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 36, "endColumn": 18}, {"ruleId": "790", "severity": 1, "message": "921", "line": 37, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 37, "endColumn": 23}, {"ruleId": "790", "severity": 1, "message": "917", "line": 41, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 41, "endColumn": 16}, {"ruleId": "790", "severity": 1, "message": "918", "line": 41, "column": 18, "nodeType": "792", "messageId": "793", "endLine": 41, "endColumn": 29}, {"ruleId": "790", "severity": 1, "message": "807", "line": 42, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 42, "endColumn": 20}, {"ruleId": "790", "severity": 1, "message": "864", "line": 50, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 50, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "806", "line": 221, "column": 13, "nodeType": "792", "messageId": "793", "endLine": 221, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "898", "line": 15, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 15, "endColumn": 8}, {"ruleId": "790", "severity": 1, "message": "890", "line": 16, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 16, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "811", "line": 17, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 17, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "796", "line": 27, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 27, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "813", "line": 28, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 28, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "922", "line": 32, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 32, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "923", "line": 33, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 33, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "924", "line": 34, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 34, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "925", "line": 49, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 49, "endColumn": 23}, {"ruleId": "800", "severity": 1, "message": "847", "line": 59, "column": 6, "nodeType": "802", "endLine": 59, "endColumn": 8, "suggestions": "926"}, {"ruleId": "790", "severity": 1, "message": "876", "line": 23, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 23, "endColumn": 13}, {"ruleId": "790", "severity": 1, "message": "890", "line": 26, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 26, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "795", "line": 30, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 30, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "917", "line": 44, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 44, "endColumn": 16}, {"ruleId": "790", "severity": 1, "message": "927", "line": 44, "column": 18, "nodeType": "792", "messageId": "793", "endLine": 44, "endColumn": 28}, {"ruleId": "790", "severity": 1, "message": "864", "line": 52, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 52, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "806", "line": 190, "column": 13, "nodeType": "792", "messageId": "793", "endLine": 190, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "858", "line": 10, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 10, "endColumn": 8}, {"ruleId": "790", "severity": 1, "message": "898", "line": 24, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 24, "endColumn": 8}, {"ruleId": "790", "severity": 1, "message": "919", "line": 27, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 27, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "928", "line": 28, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 28, "endColumn": 8}, {"ruleId": "790", "severity": 1, "message": "815", "line": 29, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 29, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "795", "line": 33, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 33, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "902", "line": 37, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 37, "endColumn": 18}, {"ruleId": "790", "severity": 1, "message": "821", "line": 44, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 44, "endColumn": 23}, {"ruleId": "790", "severity": 1, "message": "851", "line": 47, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 47, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "901", "line": 48, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 48, "endColumn": 14}, {"ruleId": "790", "severity": 1, "message": "929", "line": 56, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 56, "endColumn": 14}, {"ruleId": "790", "severity": 1, "message": "930", "line": 1, "column": 27, "nodeType": "792", "messageId": "793", "endLine": 1, "endColumn": 36}, {"ruleId": "790", "severity": 1, "message": "812", "line": 17, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 17, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "877", "line": 33, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 33, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "931", "line": 34, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 34, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "851", "line": 36, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 36, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "845", "line": 38, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 38, "endColumn": 28}, {"ruleId": "790", "severity": 1, "message": "898", "line": 15, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 15, "endColumn": 8}, {"ruleId": "790", "severity": 1, "message": "807", "line": 35, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 35, "endColumn": 20}, {"ruleId": "790", "severity": 1, "message": "932", "line": 42, "column": 11, "nodeType": "792", "messageId": "793", "endLine": 42, "endColumn": 24}, {"ruleId": "790", "severity": 1, "message": "854", "line": 42, "column": 26, "nodeType": "792", "messageId": "793", "endLine": 42, "endColumn": 33}, {"ruleId": "790", "severity": 1, "message": "890", "line": 20, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 20, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "933", "line": 28, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 28, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "920", "line": 30, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 30, "endColumn": 18}, {"ruleId": "800", "severity": 1, "message": "847", "line": 115, "column": 6, "nodeType": "802", "endLine": 115, "endColumn": 8, "suggestions": "934"}, {"ruleId": "790", "severity": 1, "message": "914", "line": 6, "column": 16, "nodeType": "792", "messageId": "793", "endLine": 6, "endColumn": 20}, {"ruleId": "790", "severity": 1, "message": "927", "line": 37, "column": 18, "nodeType": "792", "messageId": "793", "endLine": 37, "endColumn": 28}, {"ruleId": "790", "severity": 1, "message": "935", "line": 2, "column": 24, "nodeType": "792", "messageId": "793", "endLine": 2, "endColumn": 38}, {"ruleId": "936", "severity": 1, "message": "937", "line": 262, "column": 34, "nodeType": "938", "messageId": "939", "endLine": 262, "endColumn": 35}, {"ruleId": "936", "severity": 1, "message": "937", "line": 262, "column": 40, "nodeType": "938", "messageId": "939", "endLine": 262, "endColumn": 41}, {"ruleId": "790", "severity": 1, "message": "940", "line": 12, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 12, "endColumn": 8}, {"ruleId": "790", "severity": 1, "message": "828", "line": 27, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 27, "endColumn": 21}, {"ruleId": "790", "severity": 1, "message": "869", "line": 29, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 29, "endColumn": 18}, {"ruleId": "790", "severity": 1, "message": "829", "line": 30, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 30, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "807", "line": 34, "column": 26, "nodeType": "792", "messageId": "793", "endLine": 34, "endColumn": 36}, {"ruleId": "790", "severity": 1, "message": "806", "line": 86, "column": 13, "nodeType": "792", "messageId": "793", "endLine": 86, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "886", "line": 30, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 30, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "941", "line": 37, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 37, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "829", "line": 38, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 38, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "942", "line": 40, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 40, "endColumn": 24}, {"ruleId": "790", "severity": 1, "message": "828", "line": 29, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 29, "endColumn": 21}, {"ruleId": "790", "severity": 1, "message": "930", "line": 1, "column": 27, "nodeType": "792", "messageId": "793", "endLine": 1, "endColumn": 36}, {"ruleId": "790", "severity": 1, "message": "811", "line": 24, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 24, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "876", "line": 25, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 25, "endColumn": 13}, {"ruleId": "790", "severity": 1, "message": "795", "line": 29, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 29, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "931", "line": 35, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 35, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "902", "line": 39, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 39, "endColumn": 18}, {"ruleId": "790", "severity": 1, "message": "823", "line": 40, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 40, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "882", "line": 41, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 41, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "943", "line": 42, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 42, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "944", "line": 48, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 48, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "945", "line": 114, "column": 19, "nodeType": "792", "messageId": "793", "endLine": 114, "endColumn": 29}, {"ruleId": "790", "severity": 1, "message": "946", "line": 115, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 115, "endColumn": 23}, {"ruleId": "790", "severity": 1, "message": "812", "line": 17, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 17, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "890", "line": 20, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 20, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "876", "line": 22, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 22, "endColumn": 13}, {"ruleId": "790", "severity": 1, "message": "811", "line": 23, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 23, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "795", "line": 30, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 30, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "829", "line": 34, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 34, "endColumn": 22}, {"ruleId": "790", "severity": 1, "message": "796", "line": 37, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 37, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "946", "line": 94, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 94, "endColumn": 23}, {"ruleId": "800", "severity": 1, "message": "847", "line": 218, "column": 6, "nodeType": "802", "endLine": 218, "endColumn": 8, "suggestions": "947"}, {"ruleId": "790", "severity": 1, "message": "913", "line": 11, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 11, "endColumn": 7}, {"ruleId": "790", "severity": 1, "message": "795", "line": 26, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 26, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "902", "line": 28, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 28, "endColumn": 18}, {"ruleId": "800", "severity": 1, "message": "948", "line": 262, "column": 6, "nodeType": "802", "endLine": 262, "endColumn": 8, "suggestions": "949"}, {"ruleId": "790", "severity": 1, "message": "812", "line": 20, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 20, "endColumn": 11}, {"ruleId": "790", "severity": 1, "message": "795", "line": 24, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 24, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "950", "line": 2, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 2, "endColumn": 14}, {"ruleId": "790", "severity": 1, "message": "951", "line": 19, "column": 8, "nodeType": "792", "messageId": "793", "endLine": 19, "endColumn": 19}, {"ruleId": "790", "severity": 1, "message": "952", "line": 31, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 31, "endColumn": 16}, {"ruleId": "790", "severity": 1, "message": "953", "line": 31, "column": 18, "nodeType": "792", "messageId": "793", "endLine": 31, "endColumn": 23}, {"ruleId": "790", "severity": 1, "message": "954", "line": 53, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 53, "endColumn": 26}, {"ruleId": "790", "severity": 1, "message": "955", "line": 53, "column": 28, "nodeType": "792", "messageId": "793", "endLine": 53, "endColumn": 47}, {"ruleId": "800", "severity": 1, "message": "956", "line": 84, "column": 6, "nodeType": "802", "endLine": 84, "endColumn": 25, "suggestions": "957"}, {"ruleId": "790", "severity": 1, "message": "958", "line": 5, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 5, "endColumn": 7}, {"ruleId": "790", "severity": 1, "message": "890", "line": 12, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 12, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "959", "line": 77, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 77, "endColumn": 21}, {"ruleId": "790", "severity": 1, "message": "960", "line": 2, "column": 10, "nodeType": "792", "messageId": "793", "endLine": 2, "endColumn": 14}, {"ruleId": "790", "severity": 1, "message": "930", "line": 1, "column": 27, "nodeType": "792", "messageId": "793", "endLine": 1, "endColumn": 36}, {"ruleId": "790", "severity": 1, "message": "890", "line": 7, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 7, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "817", "line": 17, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 17, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "845", "line": 34, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 34, "endColumn": 28}, {"ruleId": "790", "severity": 1, "message": "961", "line": 48, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 48, "endColumn": 14}, {"ruleId": "790", "severity": 1, "message": "930", "line": 1, "column": 27, "nodeType": "792", "messageId": "793", "endLine": 1, "endColumn": 36}, {"ruleId": "790", "severity": 1, "message": "890", "line": 15, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 15, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "962", "line": 17, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 17, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "963", "line": 26, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 26, "endColumn": 15}, {"ruleId": "790", "severity": 1, "message": "964", "line": 12, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 12, "endColumn": 7}, {"ruleId": "790", "severity": 1, "message": "817", "line": 13, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 13, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "795", "line": 20, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 20, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "862", "line": 21, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 21, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "965", "line": 9, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 9, "endColumn": 9}, {"ruleId": "790", "severity": 1, "message": "815", "line": 13, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 13, "endColumn": 10}, {"ruleId": "790", "severity": 1, "message": "795", "line": 20, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 20, "endColumn": 17}, {"ruleId": "790", "severity": 1, "message": "966", "line": 31, "column": 3, "nodeType": "792", "messageId": "793", "endLine": 31, "endColumn": 16}, {"ruleId": "790", "severity": 1, "message": "961", "line": 37, "column": 9, "nodeType": "792", "messageId": "793", "endLine": 37, "endColumn": 14}, {"ruleId": "800", "severity": 1, "message": "967", "line": 42, "column": 6, "nodeType": "802", "endLine": 42, "endColumn": 8, "suggestions": "968"}, {"ruleId": "800", "severity": 1, "message": "969", "line": 48, "column": 6, "nodeType": "802", "endLine": 48, "endColumn": 32, "suggestions": "970"}, "@typescript-eslint/no-unused-vars", "'isAuthenticated' is assigned a value but never used.", "Identifier", "unusedVar", "'useMemo' is defined but never used.", "'SearchOutlined' is defined but never used.", "'UploadOutlined' is defined but never used.", "'ConfirmDialog' is defined but never used.", "'coreBOMs' is assigned a value but never used.", "'copyingBOM' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["971"], "React Hook useCallback has a missing dependency: 'pagination'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["972"], "'values' is assigned a value but never used.", "'formatDate' is defined but never used.", "'errorHandler' is defined but never used.", "'ErrorType' is defined but never used.", "'pendingItems' is assigned a value but never used.", "'Upload' is defined but never used.", "'Progress' is defined but never used.", "'DownloadOutlined' is defined but never used.", "'form' is assigned a value but never used.", "'Tooltip' is defined but never used.", "'currentBOM' is assigned a value but never used.", "'Avatar' is defined but never used.", "'Select' is defined but never used.", "'Tabs' is defined but never used.", "'ArrowUpOutlined' is defined but never used.", "'ShoppingCartOutlined' is defined but never used.", "'InboxOutlined' is defined but never used.", "'ToolOutlined' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'Checkbox' is defined but never used.", "'Tag' is defined but never used.", "'CalculatorOutlined' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'InfoCircleOutlined' is defined but never used.", "'CopyOutlined' is defined but never used.", "'UpOutlined' is defined but never used.", "'DownOutlined' is defined but never used.", "'MATERIAL_CATEGORIES' is defined but never used.", "'UserOutlined' is defined but never used.", "'orderBOMs' is assigned a value but never used.", "'copyingOrder' is assigned a value but never used.", ["973"], "React Hook useEffect has a missing dependency: 'loadMaterialData'. Either include it or remove the dependency array.", ["974"], "'DollarOutlined' is defined but never used.", "'materials' is assigned a value but never used.", ["975"], "'CalendarOutlined' is defined but never used.", "'ExclamationCircleOutlined' is defined but never used.", "'purchaseOrders' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["976"], "'PlayCircleOutlined' is defined but never used.", "'ExportOutlined' is defined but never used.", "'ClockCircleOutlined' is defined but never used.", "'Legend' is defined but never used.", "'mrpResults' is assigned a value but never used.", "'loading' is assigned a value but never used.", "'setSelectedOrders' is assigned a value but never used.", "'currentStep' is assigned a value but never used.", "'setCurrentStep' is assigned a value but never used.", "'Badge' is defined but never used.", "'Timeline' is defined but never used.", "'FileExcelOutlined' is defined but never used.", "'SendOutlined' is defined but never used.", "'FilterOutlined' is defined but never used.", "'Paragraph' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'InputNumber' is defined but never used.", "'Step' is assigned a value but never used.", "'inventoryReceives' is assigned a value but never used.", ["977"], "'WarningOutlined' is defined but never used.", "'ImportOutlined' is defined but never used.", "'Inventory' is defined but never used.", "'inventory' is assigned a value but never used.", ["978"], "'inventoryIssues' is assigned a value but never used.", ["979"], "'DatePicker' is defined but never used.", "'EditOutlined' is defined but never used.", "'dayjs' is defined but never used.", "'remnants' is assigned a value but never used.", ["980"], "'FileImageOutlined' is defined but never used.", "'FileTextOutlined' is defined but never used.", "'deviceArchives' is assigned a value but never used.", ["981"], "'ApartmentOutlined' is defined but never used.", "'BarChartOutlined' is defined but never used.", "'RiseOutlined' is defined but never used.", "'costReports' is assigned a value but never used.", ["982"], "'Divider' is defined but never used.", "'wasteTracking' is assigned a value but never used.", ["983"], "'FallOutlined' is defined but never used.", "'costAnalysis' is assigned a value but never used.", ["984"], "'Row' is defined but never used.", "'Col' is defined but never used.", "'Alert' is defined but never used.", "'Rate' is defined but never used.", "'Popconfirm' is defined but never used.", "'EyeOutlined' is defined but never used.", "'HistoryOutlined' is defined but never used.", "'AffectedItem' is defined but never used.", "'setCurrentUserRole' is assigned a value but never used.", "'activeTab' is assigned a value but never used.", "'setActiveTab' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadECNData'. Either include it or remove the dependency array.", ["985"], "'ECN' is defined but never used.", "React Hook useEffect has a missing dependency: 'form'. Either include it or remove the dependency array.", ["986"], "'Modal' is defined but never used.", "'Form' is defined but never used.", "'Text' is assigned a value but never used.", "React Hook useEffect has an unnecessary dependency: 'pagination.current'. Either exclude it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["987"], "'ROUTES' is defined but never used.", "'PERMISSIONS' is defined but never used.", "'Switch' is defined but never used.", "'SettingOutlined' is defined but never used.", "'SecurityScanOutlined' is defined but never used.", "'MailOutlined' is defined but never used.", "'BellOutlined' is defined but never used.", "'GlobalOutlined' is defined but never used.", "'systemConfig' is assigned a value but never used.", ["988"], "'USER_ROLES' is defined but never used.", "'Radio' is defined but never used.", "'Title' is assigned a value but never used.", "'useEffect' is defined but never used.", "'DeleteOutlined' is defined but never used.", "'workbenchData' is assigned a value but never used.", "'DragOutlined' is defined but never used.", ["989"], "'NUMBER_FORMATS' is defined but never used.", "no-mixed-operators", "Unexpected mix of '&' and '|'. Use parentheses to clarify the intended order of operations.", "BinaryExpression", "unexpectedMixedOperator", "'Input' is defined but never used.", "'PauseCircleOutlined' is defined but never used.", "'formatCurrency' is defined but never used.", "'CloudUploadOutlined' is defined but never used.", "'Option' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'searchKeyword' is assigned a value but never used.", ["990"], "React Hook useEffect has missing dependencies: 'loadBatchData' and 'loadStatistics'. Either include them or remove the dependency array.", ["991"], "'HelpContent' is defined but never used.", "'HelpSidebar' is defined but never used.", "'Content' is assigned a value but never used.", "'Sider' is assigned a value but never used.", "'sidebarCollapsed' is assigned a value but never used.", "'setSidebarCollapsed' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'loadContent'. Either include it or remove the dependency array.", ["992"], "'Card' is defined but never used.", "'searchValue' is assigned a value but never used.", "'Menu' is defined but never used.", "'Panel' is assigned a value but never used.", "'Slider' is defined but never used.", "'TagsOutlined' is defined but never used.", "'List' is defined but never used.", "'Button' is defined but never used.", "'RightOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'initializeMermaid'. Either include it or remove the dependency array.", ["993"], "React Hook useEffect has a missing dependency: 'renderDiagram'. Either include it or remove the dependency array.", ["994"], {"desc": "995", "fix": "996"}, {"desc": "997", "fix": "998"}, {"desc": "999", "fix": "1000"}, {"desc": "1001", "fix": "1002"}, {"desc": "1003", "fix": "1004"}, {"desc": "1005", "fix": "1006"}, {"desc": "1007", "fix": "1008"}, {"desc": "1009", "fix": "1010"}, {"desc": "1007", "fix": "1011"}, {"desc": "1012", "fix": "1013"}, {"desc": "1007", "fix": "1014"}, {"desc": "1015", "fix": "1016"}, {"desc": "1017", "fix": "1018"}, {"desc": "1019", "fix": "1020"}, {"desc": "1021", "fix": "1022"}, {"desc": "1023", "fix": "1024"}, {"desc": "1025", "fix": "1026"}, {"desc": "1027", "fix": "1028"}, {"desc": "1027", "fix": "1029"}, {"desc": "1027", "fix": "1030"}, {"desc": "1031", "fix": "1032"}, {"desc": "1033", "fix": "1034"}, {"desc": "1035", "fix": "1036"}, {"desc": "1037", "fix": "1038"}, "Update the dependencies array to be: [loadData, pagination.pageSize, searchKeyword, statusFilter]", {"range": "1039", "text": "1040"}, "Update the dependencies array to be: [dispatch, pagination, searchKeyword]", {"range": "1041", "text": "1042"}, "Update the dependencies array to be: [pagination.pageSize, searchKeyword, statusFilter, customerFilter, loadData]", {"range": "1043", "text": "1044"}, "Update the dependencies array to be: [id, loadMaterialData]", {"range": "1045", "text": "1046"}, "Update the dependencies array to be: [pagination.pageSize, searchKeyword, categoryFilter, supplierFilter, loadData]", {"range": "1047", "text": "1048"}, "Update the dependencies array to be: [loadData, searchKeyword, statusFilter, supplierFilter]", {"range": "1049", "text": "1050"}, "Update the dependencies array to be: [loadData, searchKeyword, statusFilter]", {"range": "1051", "text": "1052"}, "Update the dependencies array to be: [searchKeyword, categoryFilter, warehouseFilter, stockStatusFilter, loadData]", {"range": "1053", "text": "1054"}, {"range": "1055", "text": "1052"}, "Update the dependencies array to be: [searchKeyword, statusFilter, materialTypeFilter, loadData]", {"range": "1056", "text": "1057"}, {"range": "1058", "text": "1052"}, "Update the dependencies array to be: [reportType, dateRange, loadData]", {"range": "1059", "text": "1060"}, "Update the dependencies array to be: [dateRange, loadData, wasteType]", {"range": "1061", "text": "1062"}, "Update the dependencies array to be: [dateRange, loadData, selectedOrder]", {"range": "1063", "text": "1064"}, "Update the dependencies array to be: [id, loadECNData]", {"range": "1065", "text": "1066"}, "Update the dependencies array to be: [form]", {"range": "1067", "text": "1068"}, "Update the dependencies array to be: [pagination.pageSize, searchKeyword, statusFilter, priorityFilter, dateRange]", {"range": "1069", "text": "1070"}, "Update the dependencies array to be: [loadData]", {"range": "1071", "text": "1072"}, {"range": "1073", "text": "1072"}, {"range": "1074", "text": "1072"}, "Update the dependencies array to be: [loadBatchData, loadStatistics]", {"range": "1075", "text": "1076"}, "Update the dependencies array to be: [loadContent, location.pathname]", {"range": "1077", "text": "1078"}, "Update the dependencies array to be: [initializeMermaid]", {"range": "1079", "text": "1080"}, "Update the dependencies array to be: [definition, currentTheme, renderDiagram]", {"range": "1081", "text": "1082"}, [2679, 2749], "[loadData, pagination.pageSize, searchKeyword, statusFilter]", [2934, 3000], "[dispatch, pagination, searchKeyword]", [2020, 2106], "[pagination.pageSize, searchKeyword, statusFilter, customerFilter, loadData]", [1465, 1469], "[id, loadMaterialData]", [2638, 2726], "[pagination.pageSize, searchKeyword, categoryFilter, supplierFilter, loadData]", [1442, 1487], "[loadData, searchKeyword, statusFilter, supplierFilter]", [1423, 1452], "[loadData, searchKeyword, statusFilter]", [1325, 1392], "[searchKeyword, categoryFilter, warehouseFilter, stockStatusFilter, loadData]", [1394, 1423], [1600, 1649], "[searchKeyword, statusFilter, materialTypeFilter, loadData]", [1927, 1956], [1470, 1493], "[reportType, dateRange, loadData]", [1675, 1697], "[dateRange, loadData, wasteType]", [1437, 1463], "[date<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, selected<PERSON>rder]", [1714, 1718], "[id, loadECNData]", [1850, 1852], "[form]", [1650, 1747], "[pagination.pageSize, searchKeyword, statusFilter, priorityFilter, dateRange]", [1252, 1254], "[loadData]", [2429, 2431], [5255, 5257], [6631, 6633], "[loadBatchData, loadStatistics]", [2776, 2795], "[loadContent, location.pathname]", [1180, 1182], "[initializeMer<PERSON>id]", [1262, 1288], "[definition, currentTheme, renderDiagram]"]