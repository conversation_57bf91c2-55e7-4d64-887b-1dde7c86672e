import React, { useState } from 'react';
import { Card, Tabs, Typography, Space, Tag, Button, Table, Modal } from 'antd';
import {
  FileTextOutlined,
  SwapRightOutlined,
  InfoCircleOutlined,
  EyeOutlined
} from '@ant-design/icons';
import MermaidDiagram from './MermaidDiagram';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

interface DocumentInfo {
  code: string;
  name: string;
  description: string;
  creator: string;
  approver: string;
  fields: string[];
}

interface DocumentFlow {
  id: string;
  title: string;
  description: string;
  category: string;
  diagram: string;
  documents: DocumentInfo[];
}

/**
 * 单据流转示意图组件
 */
const DocumentFlowDiagram: React.FC = () => {
  const [selectedFlow, setSelectedFlow] = useState<string>('bom-documents');
  const [showDocumentsModal, setShowDocumentsModal] = useState(false);

  // 单据流转数据
  const documentFlows: DocumentFlow[] = [
    {
      id: 'bom-documents',
      title: 'BOM单据流转',
      description: '从产品设计到BOM发布过程中涉及的各种单据流转关系。',
      category: 'BOM管理',
      diagram: `
        graph TD
          A[产品需求单<br/>PRD-001] --> B[设计任务单<br/>DTK-001]
          B --> C[物料申请单<br/>MAR-001]
          C --> D[BOM草稿<br/>BOM-001-DRAFT]
          D --> E[设计评审单<br/>DRV-001]
          E --> F{评审结果}
          F -->|通过| G[BOM正式版<br/>BOM-001-V1.0]
          F -->|不通过| H[设计修改单<br/>DCR-001]
          H --> D
          G --> I[技术审核单<br/>TAR-001]
          I --> J{技术审核}
          J -->|通过| K[工艺审核单<br/>PAR-001]
          J -->|不通过| L[技术修改单<br/>TCR-001]
          L --> G
          K --> M{工艺审核}
          M -->|通过| N[BOM发布单<br/>BPR-001]
          M -->|不通过| O[工艺修改单<br/>PCR-001]
          O --> G
          N --> P[BOM发布版<br/>BOM-001-RELEASE]
          P --> Q[生产任务单<br/>PTO-001]
          P --> R[采购申请单<br/>PAR-001]
          
          style A fill:#e1f5fe
          style P fill:#e8f5e8
          style Q fill:#e8f5e8
          style R fill:#e8f5e8
          style F fill:#fff3e0
          style J fill:#fff3e0
          style M fill:#fff3e0
      `,
      documents: [
        {
          code: 'PRD',
          name: '产品需求单',
          description: '记录产品功能需求和技术规格',
          creator: '产品经理',
          approver: '技术总监',
          fields: ['产品名称', '功能需求', '技术规格', '交付时间']
        },
        {
          code: 'DTK',
          name: '设计任务单',
          description: '分配设计任务和设计要求',
          creator: '项目经理',
          approver: '设计主管',
          fields: ['任务编号', '设计要求', '完成时间', '设计师']
        },
        {
          code: 'BOM',
          name: 'BOM清单',
          description: '产品物料清单和结构关系',
          creator: '设计工程师',
          approver: '技术经理',
          fields: ['物料编码', '物料名称', '用量', '替代料']
        }
      ]
    },
    {
      id: 'procurement-documents',
      title: '采购单据流转',
      description: '采购业务过程中的单据流转和审批关系。',
      category: '采购管理',
      diagram: `
        graph TD
          A[采购需求单<br/>PRQ-001] --> B[采购申请单<br/>PAP-001]
          B --> C{部门审批}
          C -->|通过| D[询价单<br/>RFQ-001]
          C -->|不通过| E[申请修改单<br/>AMR-001]
          E --> B
          D --> F[供应商报价单<br/>QUO-001]
          F --> G[比价分析单<br/>CPA-001]
          G --> H[供应商选择单<br/>SSR-001]
          H --> I[采购合同<br/>CON-001]
          I --> J[采购订单<br/>PO-001]
          J --> K[订单确认单<br/>POC-001]
          K --> L[发货通知单<br/>SHN-001]
          L --> M[收货单<br/>REC-001]
          M --> N[质检报告<br/>QIR-001]
          N --> O{质检结果}
          O -->|合格| P[入库单<br/>INB-001]
          O -->|不合格| Q[退货单<br/>RET-001]
          Q --> L
          P --> R[发票<br/>INV-001]
          R --> S[付款申请单<br/>PAY-001]
          S --> T[付款单<br/>PMT-001]
          
          style A fill:#e1f5fe
          style T fill:#e8f5e8
          style C fill:#fff3e0
          style O fill:#fff3e0
      `,
      documents: [
        {
          code: 'PRQ',
          name: '采购需求单',
          description: '记录采购需求和规格要求',
          creator: '需求部门',
          approver: '部门主管',
          fields: ['物料名称', '规格型号', '数量', '需求时间']
        },
        {
          code: 'PO',
          name: '采购订单',
          description: '正式的采购订单文件',
          creator: '采购员',
          approver: '采购经理',
          fields: ['供应商', '物料清单', '价格', '交期']
        }
      ]
    },
    {
      id: 'inventory-documents',
      title: '库存单据流转',
      description: '库存管理过程中的单据流转和操作记录。',
      category: '库存管理',
      diagram: `
        graph TD
          A[入库通知单<br/>INN-001] --> B[收货单<br/>REC-001]
          B --> C[质检单<br/>QC-001]
          C --> D{质检结果}
          D -->|合格| E[入库单<br/>INB-001]
          D -->|不合格| F[拒收单<br/>REJ-001]
          E --> G[库存更新<br/>INV-UPD]
          F --> H[退货单<br/>RET-001]
          
          I[出库申请单<br/>OAP-001] --> J[出库审批<br/>OAP-APP]
          J --> K{审批结果}
          K -->|通过| L[出库单<br/>OUT-001]
          K -->|不通过| M[申请修改<br/>OAP-MOD]
          M --> I
          L --> N[库存更新<br/>INV-UPD]
          
          O[盘点计划<br/>STP-001] --> P[盘点单<br/>STK-001]
          P --> Q[盘点差异单<br/>STD-001]
          Q --> R{差异处理}
          R -->|调整| S[库存调整单<br/>ADJ-001]
          R -->|无需调整| T[盘点完成<br/>STC-001]
          S --> U[库存更新<br/>INV-UPD]
          T --> U
          
          G --> U
          N --> U
          U --> V[库存报表<br/>INV-RPT]
          
          style A fill:#e1f5fe
          style I fill:#e1f5fe
          style O fill:#e1f5fe
          style V fill:#e8f5e8
          style D fill:#fff3e0
          style K fill:#fff3e0
          style R fill:#fff3e0
      `,
      documents: [
        {
          code: 'INB',
          name: '入库单',
          description: '记录物料入库的详细信息',
          creator: '仓库管理员',
          approver: '仓库主管',
          fields: ['物料编码', '入库数量', '批次号', '存放位置']
        },
        {
          code: 'OUT',
          name: '出库单',
          description: '记录物料出库的详细信息',
          creator: '仓库管理员',
          approver: '仓库主管',
          fields: ['物料编码', '出库数量', '领用部门', '用途']
        }
      ]
    }
  ];

  // 获取当前选中的流程
  const getCurrentFlow = () => {
    return documentFlows.find(flow => flow.id === selectedFlow) || documentFlows[0];
  };

  // 单据表格列定义
  const documentColumns = [
    {
      title: '单据代码',
      dataIndex: 'code',
      key: 'code',
      width: 100,
    },
    {
      title: '单据名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: '创建者',
      dataIndex: 'creator',
      key: 'creator',
      width: 100,
    },
    {
      title: '审批者',
      dataIndex: 'approver',
      key: 'approver',
      width: 100,
    },
    {
      title: '主要字段',
      dataIndex: 'fields',
      key: 'fields',
      render: (fields: string[]) => (
        <Space wrap>
          {fields.map((field, index) => (
            <Tag key={index} size="small">{field}</Tag>
          ))}
        </Space>
      ),
    },
  ];

  const currentFlow = getCurrentFlow();

  return (
    <div>
      <Card>
        <Tabs
          activeKey={selectedFlow}
          onChange={setSelectedFlow}
          tabBarExtraContent={
            <Space>
              <Button
                icon={<InfoCircleOutlined />}
                onClick={() => setShowDocumentsModal(true)}
              >
                单据说明
              </Button>
              <Button icon={<EyeOutlined />}>
                流程详情
              </Button>
            </Space>
          }
        >
          {documentFlows.map(flow => (
            <TabPane
              tab={
                <Space>
                  <FileTextOutlined />
                  {flow.title}
                </Space>
              }
              key={flow.id}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Title level={4}>
                    <SwapRightOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                    {flow.title}
                  </Title>
                  <Paragraph>{flow.description}</Paragraph>
                  <Tag color="blue">{flow.category}</Tag>
                </div>
                
                <MermaidDiagram
                  definition={flow.diagram}
                  height={600}
                  showControls={true}
                />
                
                <Card size="small" title="流程说明">
                  <ul>
                    <li>蓝色节点表示流程起始单据</li>
                    <li>绿色节点表示流程结束单据</li>
                    <li>橙色节点表示需要人工决策的环节</li>
                    <li>箭头表示单据流转方向</li>
                    <li>虚线表示可选的流转路径</li>
                  </ul>
                </Card>
              </Space>
            </TabPane>
          ))}
        </Tabs>
      </Card>

      {/* 单据说明模态框 */}
      <Modal
        title={`${currentFlow.title} - 单据说明`}
        open={showDocumentsModal}
        onCancel={() => setShowDocumentsModal(false)}
        footer={null}
        width={1000}
      >
        <Table
          columns={documentColumns}
          dataSource={currentFlow.documents}
          rowKey="code"
          pagination={false}
          size="small"
        />
      </Modal>
    </div>
  );
};

export default DocumentFlowDiagram;
