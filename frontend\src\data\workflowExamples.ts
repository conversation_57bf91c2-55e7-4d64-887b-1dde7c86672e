import { WorkflowDiagram } from '../types/help';

/**
 * 工作流程图示例数据
 */
export const workflowExamples: WorkflowDiagram[] = [
  {
    id: 'bom-creation-workflow',
    title: 'BOM创建流程',
    description: '从需求分析到BOM发布的完整流程，包括物料选择、结构设计、审核发布等关键环节。',
    type: 'workflow',
    category: 'BOM管理',
    tags: ['BOM', '产品设计', '工作流程'],
    createdAt: '2024-08-20T10:00:00Z',
    updatedAt: '2024-08-20T10:00:00Z',
    diagramData: `
graph TD
    A[需求分析] --> B[产品设计]
    B --> C[物料选择]
    C --> D[BOM结构设计]
    D --> E[工艺路线设计]
    E --> F[成本估算]
    F --> G{设计评审}
    G -->|通过| H[创建BOM]
    G -->|不通过| I[设计修改]
    I --> D
    H --> J[BOM验证]
    J --> K{验证结果}
    K -->|通过| L[提交审核]
    K -->|不通过| M[BOM修正]
    M --> J
    L --> N[技术审核]
    N --> O{审核结果}
    O -->|通过| P[工艺审核]
    O -->|不通过| Q[技术修改]
    Q --> L
    P --> R{工艺审核结果}
    R -->|通过| S[最终审批]
    R -->|不通过| T[工艺修改]
    T --> L
    S --> U{审批结果}
    U -->|通过| V[BOM发布]
    U -->|不通过| W[重新设计]
    W --> B
    V --> X[生产使用]
    
    style A fill:#e1f5fe
    style X fill:#e8f5e8
    style G fill:#fff3e0
    style K fill:#fff3e0
    style O fill:#fff3e0
    style R fill:#fff3e0
    style U fill:#fff3e0
    `
  },
  
  {
    id: 'procurement-process',
    title: '采购业务流程',
    description: '从采购需求到收货入库的完整采购流程，包括供应商管理、询价比价、合同签订等环节。',
    type: 'business_process',
    category: '采购管理',
    tags: ['采购', '供应商', '业务流程'],
    createdAt: '2024-08-20T10:30:00Z',
    updatedAt: '2024-08-20T10:30:00Z',
    diagramData: `
graph TD
    A[采购需求] --> B[需求分析]
    B --> C[预算确认]
    C --> D[创建采购申请]
    D --> E[部门审批]
    E --> F{审批结果}
    F -->|通过| G[采购部门接收]
    F -->|拒绝| H[申请修改]
    H --> D
    G --> I[供应商筛选]
    I --> J[询价]
    J --> K[报价收集]
    K --> L[比价分析]
    L --> M[供应商选择]
    M --> N[商务谈判]
    N --> O[合同起草]
    O --> P[合同审核]
    P --> Q{合同审核}
    Q -->|通过| R[合同签订]
    Q -->|不通过| S[合同修改]
    S --> O
    R --> T[采购订单]
    T --> U[订单确认]
    U --> V[生产安排]
    V --> W[物流配送]
    W --> X[收货验收]
    X --> Y{质量检验}
    Y -->|合格| Z[入库]
    Y -->|不合格| AA[退货处理]
    AA --> V
    Z --> BB[发票处理]
    BB --> CC[付款]
    CC --> DD[采购完成]
    
    style A fill:#e1f5fe
    style DD fill:#e8f5e8
    style F fill:#fff3e0
    style Q fill:#fff3e0
    style Y fill:#fff3e0
    `
  },
  
  {
    id: 'inventory-management',
    title: '库存管理流程',
    description: '库存监控、补货、调拨、盘点的完整管理流程，确保库存数据的准确性和物料供应的及时性。',
    type: 'system_flow',
    category: '库存管理',
    tags: ['库存', '盘点', '系统流程'],
    createdAt: '2024-08-20T11:00:00Z',
    updatedAt: '2024-08-20T11:00:00Z',
    diagramData: `
graph TD
    A[库存监控] --> B{库存状态}
    B -->|正常| A
    B -->|低库存| C[生成补货建议]
    B -->|高库存| D[生成调拨建议]
    B -->|零库存| E[紧急采购]
    
    C --> F[采购申请]
    F --> G[采购流程]
    G --> H[收货入库]
    
    D --> I[调拨申请]
    I --> J[调拨审批]
    J --> K[库存转移]
    
    E --> L[紧急采购流程]
    L --> M[快速收货]
    
    H --> N[更新库存]
    K --> N
    M --> N
    N --> O[库存盘点]
    
    O --> P{盘点结果}
    P -->|账实相符| A
    P -->|存在差异| Q[差异分析]
    Q --> R[原因调查]
    R --> S{差异处理}
    S -->|调整库存| T[库存调整]
    S -->|追究责任| U[责任认定]
    T --> A
    U --> A
    
    style A fill:#e1f5fe
    style B fill:#fff3e0
    style P fill:#fff3e0
    style S fill:#fff3e0
    `
  },
  
  {
    id: 'quality-control',
    title: '质量控制流程',
    description: '从来料检验到成品出库的全过程质量控制流程，确保产品质量符合标准要求。',
    type: 'business_process',
    category: '质量管理',
    tags: ['质量控制', '检验', '不合格品处理'],
    createdAt: '2024-08-20T11:30:00Z',
    updatedAt: '2024-08-20T11:30:00Z',
    diagramData: `
graph TD
    A[物料到货] --> B[来料检验]
    B --> C{检验结果}
    C -->|合格| D[合格标识]
    C -->|不合格| E[不合格标识]
    
    D --> F[允许入库]
    E --> G{处理方式}
    G -->|退货| H[退货处理]
    G -->|返工| I[返工处理]
    G -->|让步接收| J[特采申请]
    
    H --> K[供应商沟通]
    I --> L[返工作业]
    L --> B
    J --> M{特采审批}
    M -->|批准| N[特采入库]
    M -->|拒绝| E
    
    F --> O[生产领料]
    N --> O
    O --> P[生产过程]
    P --> Q[过程检验]
    Q --> R{过程质量}
    R -->|合格| S[继续生产]
    R -->|不合格| T[过程调整]
    T --> P
    
    S --> U[成品检验]
    U --> V{成品质量}
    V -->|合格| W[成品入库]
    V -->|不合格| X[不合格品处理]
    X --> Y{处理决定}
    Y -->|返工| Z[成品返工]
    Y -->|报废| AA[报废处理]
    Z --> U
    
    W --> BB[出库检验]
    BB --> CC{出库质量}
    CC -->|合格| DD[产品出库]
    CC -->|不合格| EE[出库拒绝]
    EE --> W
    
    style A fill:#e1f5fe
    style DD fill:#e8f5e8
    style C fill:#fff3e0
    style G fill:#fff3e0
    style M fill:#fff3e0
    style R fill:#fff3e0
    style V fill:#fff3e0
    style Y fill:#fff3e0
    style CC fill:#fff3e0
    `
  },
  
  {
    id: 'cost-analysis-process',
    title: '成本分析流程',
    description: '产品成本计算、分析、控制的完整流程，为企业决策提供准确的成本信息。',
    type: 'workflow',
    category: '成本管理',
    tags: ['成本分析', '成本控制', '财务管理'],
    createdAt: '2024-08-20T12:00:00Z',
    updatedAt: '2024-08-20T12:00:00Z',
    diagramData: `
graph TD
    A[成本数据收集] --> B[直接材料成本]
    A --> C[直接人工成本]
    A --> D[制造费用]
    
    B --> E[材料用量统计]
    B --> F[材料价格收集]
    E --> G[材料成本计算]
    F --> G
    
    C --> H[工时统计]
    C --> I[工资率计算]
    H --> J[人工成本计算]
    I --> J
    
    D --> K[设备折旧]
    D --> L[水电费用]
    D --> M[其他费用]
    K --> N[制造费用汇总]
    L --> N
    M --> N
    
    G --> O[成本汇总]
    J --> O
    N --> O
    
    O --> P[标准成本对比]
    P --> Q{成本差异}
    Q -->|正常| R[成本确认]
    Q -->|异常| S[差异分析]
    
    S --> T[原因识别]
    T --> U{差异原因}
    U -->|材料价格| V[价格分析]
    U -->|用量差异| W[用量分析]
    U -->|效率差异| X[效率分析]
    
    V --> Y[成本控制措施]
    W --> Y
    X --> Y
    Y --> Z[措施实施]
    Z --> AA[效果评估]
    AA --> BB{改善效果}
    BB -->|有效| R
    BB -->|无效| S
    
    R --> CC[成本报告]
    CC --> DD[管理决策]
    DD --> EE[成本优化]
    EE --> A
    
    style A fill:#e1f5fe
    style EE fill:#e8f5e8
    style Q fill:#fff3e0
    style U fill:#fff3e0
    style BB fill:#fff3e0
    `
  },
  
  {
    id: 'user-permission-management',
    title: '用户权限管理流程',
    description: '用户账户创建、角色分配、权限管理的完整流程，确保系统安全和数据保护。',
    type: 'system_flow',
    category: '系统管理',
    tags: ['用户管理', '权限控制', '系统安全'],
    createdAt: '2024-08-20T12:30:00Z',
    updatedAt: '2024-08-20T12:30:00Z',
    diagramData: `
graph TD
    A[用户申请] --> B[申请审核]
    B --> C{审核结果}
    C -->|通过| D[创建账户]
    C -->|拒绝| E[申请驳回]
    
    D --> F[基本信息录入]
    F --> G[角色分配]
    G --> H[权限配置]
    H --> I[账户激活]
    
    I --> J[首次登录]
    J --> K[密码修改]
    K --> L[系统培训]
    L --> M[正式使用]
    
    M --> N[权限监控]
    N --> O{权限检查}
    O -->|正常| P[继续使用]
    O -->|异常| Q[权限调整]
    
    Q --> R{调整类型}
    R -->|权限扩大| S[扩权申请]
    R -->|权限缩小| T[权限回收]
    R -->|角色变更| U[角色调整]
    
    S --> V[扩权审批]
    V --> W{审批结果}
    W -->|通过| X[权限更新]
    W -->|拒绝| P
    
    T --> X
    U --> Y[新角色配置]
    Y --> X
    X --> P
    
    P --> Z[定期审查]
    Z --> AA{审查结果}
    AA -->|正常| P
    AA -->|需调整| Q
    AA -->|账户停用| BB[账户禁用]
    
    BB --> CC[数据备份]
    CC --> DD[权限清理]
    DD --> EE[账户归档]
    
    style A fill:#e1f5fe
    style EE fill:#e8f5e8
    style C fill:#fff3e0
    style O fill:#fff3e0
    style R fill:#fff3e0
    style W fill:#fff3e0
    style AA fill:#fff3e0
    `
  }
];

/**
 * 根据类型获取工作流程图
 */
export const getWorkflowsByType = (type: string): WorkflowDiagram[] => {
  return workflowExamples.filter(workflow => workflow.type === type);
};

/**
 * 根据分类获取工作流程图
 */
export const getWorkflowsByCategory = (category: string): WorkflowDiagram[] => {
  return workflowExamples.filter(workflow => workflow.category === category);
};

/**
 * 根据ID获取工作流程图
 */
export const getWorkflowById = (id: string): WorkflowDiagram | undefined => {
  return workflowExamples.find(workflow => workflow.id === id);
};

/**
 * 搜索工作流程图
 */
export const searchWorkflows = (keyword: string): WorkflowDiagram[] => {
  const lowerKeyword = keyword.toLowerCase();
  return workflowExamples.filter(workflow => 
    workflow.title.toLowerCase().includes(lowerKeyword) ||
    workflow.description.toLowerCase().includes(lowerKeyword) ||
    workflow.tags.some(tag => tag.toLowerCase().includes(lowerKeyword))
  );
};

export default workflowExamples;
