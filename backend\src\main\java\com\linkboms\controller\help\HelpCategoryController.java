package com.linkboms.controller.help;

import com.linkboms.entity.help.HelpCategory;
import com.linkboms.service.help.HelpCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 帮助分类控制器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/help/categories")
@RequiredArgsConstructor
@Tag(name = "帮助分类管理", description = "帮助手册分类的增删改查和管理功能")
public class HelpCategoryController {

    private final HelpCategoryService helpCategoryService;

    @PostMapping
    @Operation(summary = "创建帮助分类", description = "创建新的帮助手册分类")
    public ResponseEntity<HelpCategory> createCategory(
            @Valid @RequestBody HelpCategory category) {
        log.info("创建帮助分类请求: {}", category.getName());
        
        HelpCategory createdCategory = helpCategoryService.createCategory(category);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdCategory);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新帮助分类", description = "更新指定ID的帮助分类")
    public ResponseEntity<HelpCategory> updateCategory(
            @Parameter(description = "分类ID") @PathVariable Long id,
            @Valid @RequestBody HelpCategory category) {
        log.info("更新帮助分类请求: {}", id);
        
        HelpCategory updatedCategory = helpCategoryService.updateCategory(id, category);
        return ResponseEntity.ok(updatedCategory);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取帮助分类", description = "根据ID获取帮助分类详情")
    public ResponseEntity<HelpCategory> getCategory(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("获取帮助分类请求: {}", id);
        
        HelpCategory category = helpCategoryService.getCategoryById(id);
        return ResponseEntity.ok(category);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除帮助分类", description = "删除指定ID的帮助分类")
    public ResponseEntity<Void> deleteCategory(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("删除帮助分类请求: {}", id);
        
        helpCategoryService.softDeleteCategory(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping
    @Operation(summary = "获取帮助分类列表", description = "分页查询帮助分类列表")
    public ResponseEntity<Page<HelpCategory>> getCategories(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "sortOrder") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "asc") String sortDir) {
        
        Sort.Direction direction = sortDir.equalsIgnoreCase("desc") ? Sort.Direction.DESC : Sort.Direction.ASC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(direction, sortBy));
        
        Page<HelpCategory> categories = helpCategoryService.getAllCategories(pageable);
        return ResponseEntity.ok(categories);
    }

    @GetMapping("/tree")
    @Operation(summary = "获取分类树", description = "获取完整的分类树结构")
    public ResponseEntity<List<HelpCategory>> getCategoryTree() {
        log.info("获取分类树请求");
        
        List<HelpCategory> categoryTree = helpCategoryService.getCategoryTree();
        return ResponseEntity.ok(categoryTree);
    }

    @GetMapping("/roots")
    @Operation(summary = "获取根分类", description = "获取所有根分类列表")
    public ResponseEntity<List<HelpCategory>> getRootCategories() {
        log.info("获取根分类请求");
        
        List<HelpCategory> rootCategories = helpCategoryService.getRootCategories();
        return ResponseEntity.ok(rootCategories);
    }

    @GetMapping("/{id}/children")
    @Operation(summary = "获取子分类", description = "获取指定分类的子分类列表")
    public ResponseEntity<List<HelpCategory>> getChildCategories(
            @Parameter(description = "父分类ID") @PathVariable Long id) {
        log.info("获取子分类请求: {}", id);
        
        List<HelpCategory> children = helpCategoryService.getChildCategories(id);
        return ResponseEntity.ok(children);
    }

    @GetMapping("/{id}/tree")
    @Operation(summary = "获取分类子树", description = "获取指定分类及其子分类的树结构")
    public ResponseEntity<HelpCategory> getCategoryTreeById(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("获取分类子树请求: {}", id);
        
        HelpCategory categoryTree = helpCategoryService.getCategoryTreeById(id);
        return ResponseEntity.ok(categoryTree);
    }

    @GetMapping("/{id}/path")
    @Operation(summary = "获取分类路径", description = "获取从根分类到指定分类的完整路径")
    public ResponseEntity<List<HelpCategory>> getCategoryPath(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("获取分类路径请求: {}", id);
        
        List<HelpCategory> path = helpCategoryService.getCategoryPath(id);
        return ResponseEntity.ok(path);
    }

    @GetMapping("/search")
    @Operation(summary = "搜索分类", description = "根据关键词搜索分类")
    public ResponseEntity<List<HelpCategory>> searchCategories(
            @Parameter(description = "搜索关键词") @RequestParam String keyword) {
        log.info("搜索分类请求: {}", keyword);
        
        List<HelpCategory> categories = helpCategoryService.searchCategories(keyword);
        return ResponseEntity.ok(categories);
    }

    @GetMapping("/popular")
    @Operation(summary = "获取热门分类", description = "获取热门分类列表")
    public ResponseEntity<List<HelpCategory>> getPopularCategories(
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") int limit) {
        log.info("获取热门分类请求: {}", limit);
        
        List<HelpCategory> categories = helpCategoryService.getPopularCategories(limit);
        return ResponseEntity.ok(categories);
    }

    @GetMapping("/enabled")
    @Operation(summary = "获取启用的分类", description = "获取所有启用的分类列表")
    public ResponseEntity<List<HelpCategory>> getEnabledCategories() {
        log.info("获取启用分类请求");
        
        List<HelpCategory> categories = helpCategoryService.getAllEnabledCategories();
        return ResponseEntity.ok(categories);
    }

    @GetMapping("/visible")
    @Operation(summary = "获取可见的分类", description = "获取所有可见的分类列表")
    public ResponseEntity<List<HelpCategory>> getVisibleCategories() {
        log.info("获取可见分类请求");
        
        List<HelpCategory> categories = helpCategoryService.getAllVisibleCategories();
        return ResponseEntity.ok(categories);
    }

    @PutMapping("/{id}/enable")
    @Operation(summary = "启用分类", description = "启用指定的分类")
    public ResponseEntity<Void> enableCategory(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("启用分类请求: {}", id);
        
        helpCategoryService.enableCategory(id);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/disable")
    @Operation(summary = "禁用分类", description = "禁用指定的分类")
    public ResponseEntity<Void> disableCategory(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("禁用分类请求: {}", id);
        
        helpCategoryService.disableCategory(id);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/show")
    @Operation(summary = "显示分类", description = "显示指定的分类")
    public ResponseEntity<Void> showCategory(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("显示分类请求: {}", id);
        
        helpCategoryService.showCategory(id);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/hide")
    @Operation(summary = "隐藏分类", description = "隐藏指定的分类")
    public ResponseEntity<Void> hideCategory(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("隐藏分类请求: {}", id);
        
        helpCategoryService.hideCategory(id);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/move")
    @Operation(summary = "移动分类", description = "移动分类到新的父分类下")
    public ResponseEntity<Void> moveCategory(
            @Parameter(description = "分类ID") @PathVariable Long id,
            @Parameter(description = "新父分类ID") @RequestParam(required = false) Long newParentId,
            @Parameter(description = "新排序号") @RequestParam(required = false) Integer newSortOrder) {
        log.info("移动分类请求: {} 到父分类: {}", id, newParentId);
        
        helpCategoryService.moveCategory(id, newParentId, newSortOrder);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/sort")
    @Operation(summary = "调整分类排序", description = "调整分类的排序号")
    public ResponseEntity<Void> adjustSortOrder(
            @Parameter(description = "分类ID") @PathVariable Long id,
            @Parameter(description = "新排序号") @RequestParam Integer newSortOrder) {
        log.info("调整分类排序请求: {} 新排序: {}", id, newSortOrder);
        
        helpCategoryService.adjustCategorySortOrder(id, newSortOrder);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/batch/status")
    @Operation(summary = "批量更新分类状态", description = "批量启用或禁用分类")
    public ResponseEntity<Void> batchUpdateStatus(
            @Parameter(description = "分类ID列表") @RequestBody List<Long> ids,
            @Parameter(description = "是否启用") @RequestParam boolean enabled) {
        log.info("批量更新分类状态请求: {} 启用: {}", ids, enabled);
        
        helpCategoryService.batchUpdateCategoryStatus(ids, enabled);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除分类", description = "批量软删除分类")
    public ResponseEntity<Void> batchDeleteCategories(
            @Parameter(description = "分类ID列表") @RequestBody List<Long> ids) {
        log.info("批量删除分类请求: {}", ids);
        
        helpCategoryService.batchDeleteCategories(ids);
        return ResponseEntity.noContent().build();
    }

    @PutMapping("/{id}/restore")
    @Operation(summary = "恢复分类", description = "恢复已删除的分类")
    public ResponseEntity<Void> restoreCategory(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("恢复分类请求: {}", id);
        
        helpCategoryService.restoreCategory(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/rebuild-paths")
    @Operation(summary = "重建分类路径", description = "重建所有分类的路径信息")
    public ResponseEntity<Void> rebuildCategoryPaths() {
        log.info("重建分类路径请求");
        
        helpCategoryService.rebuildCategoryPaths();
        return ResponseEntity.ok().build();
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取分类统计", description = "获取分类的统计信息")
    public ResponseEntity<Object> getCategoryStatistics() {
        log.info("获取分类统计请求");
        
        Object statistics = helpCategoryService.getCategoryStatistics();
        return ResponseEntity.ok(statistics);
    }

    @GetMapping("/{id}/content-count")
    @Operation(summary = "获取分类内容数量", description = "获取指定分类下的内容数量")
    public ResponseEntity<Long> getCategoryContentCount(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("获取分类内容数量请求: {}", id);
        
        Long count = helpCategoryService.getCategoryContentCount(id);
        return ResponseEntity.ok(count);
    }

    @GetMapping("/{id}/tree-content-count")
    @Operation(summary = "获取分类树内容数量", description = "获取指定分类及其子分类下的内容总数量")
    public ResponseEntity<Long> getCategoryTreeContentCount(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("获取分类树内容数量请求: {}", id);
        
        Long count = helpCategoryService.getCategoryTreeContentCount(id);
        return ResponseEntity.ok(count);
    }
}
