package com.linkboms.controller.help;

import com.linkboms.entity.help.HelpCategory;
import com.linkboms.service.help.HelpCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 帮助分类控制器
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/help/category")
@RequiredArgsConstructor
@Tag(name = "帮助分类管理", description = "帮助手册分类的增删改查和管理功能")
public class HelpCategoryController {

    private final HelpCategoryService helpCategoryService;

    @PostMapping
    @Operation(summary = "创建帮助分类", description = "创建新的帮助手册分类")
    public ResponseEntity<HelpCategory> createCategory(
            @Valid @RequestBody HelpCategory category) {
        log.info("创建帮助分类请求: {}", category.getName());
        
        HelpCategory createdCategory = helpCategoryService.createCategory(category);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdCategory);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新帮助分类", description = "更新指定ID的帮助分类")
    public ResponseEntity<HelpCategory> updateCategory(
            @Parameter(description = "分类ID") @PathVariable Long id,
            @Valid @RequestBody HelpCategory category) {
        log.info("更新帮助分类请求: {}", id);
        
        HelpCategory updatedCategory = helpCategoryService.updateCategory(id, category);
        return ResponseEntity.ok(updatedCategory);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取帮助分类", description = "根据ID获取帮助分类详情")
    public ResponseEntity<HelpCategory> getCategory(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("获取帮助分类请求: {}", id);
        
        HelpCategory category = helpCategoryService.getCategoryById(id);
        return ResponseEntity.ok(category);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除帮助分类", description = "软删除指定ID的帮助分类")
    public ResponseEntity<Void> deleteCategory(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("删除帮助分类请求: {}", id);
        
        helpCategoryService.deleteCategory(id);
        return ResponseEntity.noContent().build();
    }

    @DeleteMapping("/{id}/permanent")
    @Operation(summary = "永久删除帮助分类", description = "永久删除指定ID的帮助分类")
    public ResponseEntity<Void> permanentDeleteCategory(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("永久删除帮助分类请求: {}", id);
        
        helpCategoryService.permanentDeleteCategory(id);
        return ResponseEntity.noContent().build();
    }

    @PutMapping("/{id}/restore")
    @Operation(summary = "恢复帮助分类", description = "恢复已删除的帮助分类")
    public ResponseEntity<Void> restoreCategory(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("恢复帮助分类请求: {}", id);
        
        helpCategoryService.restoreCategory(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping
    @Operation(summary = "分页查询帮助分类", description = "分页获取帮助分类列表")
    public ResponseEntity<Page<HelpCategory>> getCategoryList(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "20") int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "sortOrder") String sort,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "asc") String direction) {
        
        Sort.Direction sortDirection = "asc".equalsIgnoreCase(direction) 
            ? Sort.Direction.ASC : Sort.Direction.DESC;
        Pageable pageable = PageRequest.of(page, size, Sort.by(sortDirection, sort));
        
        Page<HelpCategory> categoryPage = helpCategoryService.getCategoryList(pageable);
        return ResponseEntity.ok(categoryPage);
    }

    @GetMapping("/tree")
    @Operation(summary = "获取分类树", description = "获取完整的分类树结构")
    public ResponseEntity<List<HelpCategory>> getCategoryTree() {
        List<HelpCategory> categoryTree = helpCategoryService.getCategoryTree();
        return ResponseEntity.ok(categoryTree);
    }

    @GetMapping("/root")
    @Operation(summary = "获取根分类", description = "获取所有根分类")
    public ResponseEntity<List<HelpCategory>> getRootCategories() {
        List<HelpCategory> rootCategories = helpCategoryService.getRootCategories();
        return ResponseEntity.ok(rootCategories);
    }

    @GetMapping("/{id}/children")
    @Operation(summary = "获取子分类", description = "获取指定分类的直接子分类")
    public ResponseEntity<List<HelpCategory>> getChildCategories(
            @Parameter(description = "父分类ID") @PathVariable Long id) {
        
        List<HelpCategory> childCategories = helpCategoryService.getChildCategories(id);
        return ResponseEntity.ok(childCategories);
    }

    @GetMapping("/{id}/descendants")
    @Operation(summary = "获取所有子孙分类", description = "获取指定分类的所有子孙分类")
    public ResponseEntity<List<HelpCategory>> getDescendantCategories(
            @Parameter(description = "父分类ID") @PathVariable Long id) {
        
        List<HelpCategory> descendants = helpCategoryService.getDescendantCategories(id);
        return ResponseEntity.ok(descendants);
    }

    @GetMapping("/level/{level}")
    @Operation(summary = "按层级查询分类", description = "获取指定层级的所有分类")
    public ResponseEntity<List<HelpCategory>> getCategoriesByLevel(
            @Parameter(description = "层级") @PathVariable Integer level) {
        
        List<HelpCategory> categories = helpCategoryService.getCategoriesByLevel(level);
        return ResponseEntity.ok(categories);
    }

    @GetMapping("/visible")
    @Operation(summary = "获取可见分类", description = "获取所有可见的分类")
    public ResponseEntity<List<HelpCategory>> getVisibleCategories() {
        List<HelpCategory> visibleCategories = helpCategoryService.getVisibleCategories();
        return ResponseEntity.ok(visibleCategories);
    }

    @GetMapping("/enabled")
    @Operation(summary = "获取启用分类", description = "获取所有启用的分类")
    public ResponseEntity<List<HelpCategory>> getEnabledCategories() {
        List<HelpCategory> enabledCategories = helpCategoryService.getEnabledCategories();
        return ResponseEntity.ok(enabledCategories);
    }

    @GetMapping("/with-content")
    @Operation(summary = "获取有内容的分类", description = "获取包含内容的分类")
    public ResponseEntity<List<HelpCategory>> getCategoriesWithContent() {
        List<HelpCategory> categoriesWithContent = helpCategoryService.getCategoriesWithContent();
        return ResponseEntity.ok(categoriesWithContent);
    }

    @GetMapping("/search")
    @Operation(summary = "搜索分类", description = "根据关键词搜索分类")
    public ResponseEntity<List<HelpCategory>> searchCategories(
            @Parameter(description = "搜索关键词") @RequestParam String keyword) {
        
        List<HelpCategory> categories = helpCategoryService.searchCategories(keyword);
        return ResponseEntity.ok(categories);
    }

    @GetMapping("/name/{name}")
    @Operation(summary = "按名称查询分类", description = "根据名称查询分类")
    public ResponseEntity<HelpCategory> getCategoryByName(
            @Parameter(description = "分类名称") @PathVariable String name) {
        
        HelpCategory category = helpCategoryService.getCategoryByName(name);
        return ResponseEntity.ok(category);
    }

    @GetMapping("/path/{path}")
    @Operation(summary = "按路径查询分类", description = "根据路径查询分类")
    public ResponseEntity<HelpCategory> getCategoryByPath(
            @Parameter(description = "分类路径") @PathVariable String path) {
        
        HelpCategory category = helpCategoryService.getCategoryByPath(path);
        return ResponseEntity.ok(category);
    }

    @PutMapping("/{id}/move")
    @Operation(summary = "移动分类", description = "将分类移动到新的父分类下")
    public ResponseEntity<Void> moveCategory(
            @Parameter(description = "分类ID") @PathVariable Long id,
            @Parameter(description = "新父分类ID") @RequestParam(required = false) Long newParentId) {
        log.info("移动帮助分类: {} 到父分类: {}", id, newParentId);
        
        helpCategoryService.moveCategory(id, newParentId);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/sort")
    @Operation(summary = "设置分类排序", description = "设置分类的排序号")
    public ResponseEntity<Void> setCategorySortOrder(
            @Parameter(description = "分类ID") @PathVariable Long id,
            @Parameter(description = "排序号") @RequestParam Integer sortOrder) {
        log.info("设置帮助分类排序: {}, 排序号: {}", id, sortOrder);
        
        helpCategoryService.setCategorySortOrder(id, sortOrder);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/sort")
    @Operation(summary = "批量排序分类", description = "批量设置分类的排序")
    public ResponseEntity<Void> sortCategories(
            @RequestBody List<Map<String, Object>> sortData) {
        log.info("批量排序帮助分类: {}", sortData.size());
        
        helpCategoryService.sortCategories(sortData);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/enable")
    @Operation(summary = "启用分类", description = "启用指定的分类")
    public ResponseEntity<Void> enableCategory(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("启用帮助分类: {}", id);
        
        helpCategoryService.enableCategory(id);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/disable")
    @Operation(summary = "禁用分类", description = "禁用指定的分类")
    public ResponseEntity<Void> disableCategory(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        log.info("禁用帮助分类: {}", id);
        
        helpCategoryService.disableCategory(id);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{id}/visible")
    @Operation(summary = "设置可见性", description = "设置分类的可见性")
    public ResponseEntity<Void> setCategoryVisibility(
            @Parameter(description = "分类ID") @PathVariable Long id,
            @Parameter(description = "是否可见") @RequestParam boolean visible) {
        log.info("设置帮助分类可见性: {}, 可见: {}", id, visible);
        
        helpCategoryService.setCategoryVisibility(id, visible);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{id}/copy")
    @Operation(summary = "复制分类", description = "复制指定的分类")
    public ResponseEntity<HelpCategory> copyCategory(
            @Parameter(description = "分类ID") @PathVariable Long id,
            @Parameter(description = "新名称") @RequestParam String newName,
            @Parameter(description = "是否复制子分类") @RequestParam(defaultValue = "false") boolean includeChildren) {
        log.info("复制帮助分类: {}, 新名称: {}, 包含子分类: {}", id, newName, includeChildren);
        
        HelpCategory copiedCategory = helpCategoryService.copyCategory(id, newName, includeChildren);
        return ResponseEntity.status(HttpStatus.CREATED).body(copiedCategory);
    }

    @GetMapping("/{id}/path")
    @Operation(summary = "获取分类路径", description = "获取分类的完整路径")
    public ResponseEntity<String> getCategoryPath(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        
        String path = helpCategoryService.getCategoryPath(id);
        return ResponseEntity.ok(path);
    }

    @GetMapping("/{id}/breadcrumb")
    @Operation(summary = "获取面包屑导航", description = "获取分类的面包屑导航")
    public ResponseEntity<List<HelpCategory>> getCategoryBreadcrumb(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        
        List<HelpCategory> breadcrumb = helpCategoryService.getCategoryBreadcrumb(id);
        return ResponseEntity.ok(breadcrumb);
    }

    @GetMapping("/{id}/parent-chain")
    @Operation(summary = "获取父级链", description = "获取分类的所有父级分类")
    public ResponseEntity<List<HelpCategory>> getParentChain(
            @Parameter(description = "分类ID") @PathVariable Long id) {
        
        List<HelpCategory> parentChain = helpCategoryService.getParentChain(id);
        return ResponseEntity.ok(parentChain);
    }

    @GetMapping("/{id}/check-parent")
    @Operation(summary = "检查父子关系", description = "检查是否为指定分类的父分类")
    public ResponseEntity<Boolean> isParentOf(
            @Parameter(description = "父分类ID") @PathVariable Long id,
            @Parameter(description = "子分类ID") @RequestParam Long childId) {
        
        boolean isParent = helpCategoryService.isParentOf(id, childId);
        return ResponseEntity.ok(isParent);
    }

    @GetMapping("/{id}/check-ancestor")
    @Operation(summary = "检查祖先关系", description = "检查是否为指定分类的祖先分类")
    public ResponseEntity<Boolean> isAncestorOf(
            @Parameter(description = "祖先分类ID") @PathVariable Long id,
            @Parameter(description = "后代分类ID") @RequestParam Long descendantId) {
        
        boolean isAncestor = helpCategoryService.isAncestorOf(id, descendantId);
        return ResponseEntity.ok(isAncestor);
    }

    @PostMapping("/validate")
    @Operation(summary = "验证分类", description = "验证分类数据的有效性")
    public ResponseEntity<List<String>> validateCategory(
            @Valid @RequestBody HelpCategory category) {
        
        List<String> errors = helpCategoryService.validateCategory(category);
        return ResponseEntity.ok(errors);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取分类统计", description = "获取分类的统计信息")
    public ResponseEntity<Map<String, Object>> getCategoryStatistics() {
        Map<String, Object> statistics = helpCategoryService.getCategoryStatistics();
        return ResponseEntity.ok(statistics);
    }

    @GetMapping("/statistics/level")
    @Operation(summary = "按层级统计", description = "按层级获取分类统计信息")
    public ResponseEntity<Map<Integer, Long>> getCategoryStatisticsByLevel() {
        Map<Integer, Long> statistics = helpCategoryService.getCategoryStatisticsByLevel();
        return ResponseEntity.ok(statistics);
    }

    @GetMapping("/statistics/content-count")
    @Operation(summary = "内容数量统计", description = "获取各分类的内容数量统计")
    public ResponseEntity<Map<String, Long>> getContentCountByCategory() {
        Map<String, Long> statistics = helpCategoryService.getContentCountByCategory();
        return ResponseEntity.ok(statistics);
    }

    @PostMapping("/rebuild-paths")
    @Operation(summary = "重建路径", description = "重建所有分类的路径信息")
    public ResponseEntity<Void> rebuildCategoryPaths() {
        log.info("重建帮助分类路径");
        
        helpCategoryService.rebuildCategoryPaths();
        return ResponseEntity.ok().build();
    }

    @PostMapping("/rebuild-levels")
    @Operation(summary = "重建层级", description = "重建所有分类的层级信息")
    public ResponseEntity<Void> rebuildCategoryLevels() {
        log.info("重建帮助分类层级");
        
        helpCategoryService.rebuildCategoryLevels();
        return ResponseEntity.ok().build();
    }

    @PostMapping("/cleanup")
    @Operation(summary = "清理分类", description = "清理空的和无效的分类")
    public ResponseEntity<Void> cleanupCategories() {
        log.info("清理帮助分类");
        
        helpCategoryService.cleanupCategories();
        return ResponseEntity.ok().build();
    }

    @GetMapping("/export")
    @Operation(summary = "导出分类", description = "导出分类数据")
    public ResponseEntity<byte[]> exportCategories(
            @Parameter(description = "导出格式") @RequestParam(defaultValue = "json") String format) {
        log.info("导出帮助分类，格式: {}", format);
        
        byte[] exportData = helpCategoryService.exportCategories(format);
        return ResponseEntity.ok(exportData);
    }

    @PostMapping("/import")
    @Operation(summary = "导入分类", description = "从文件导入分类数据")
    public ResponseEntity<List<HelpCategory>> importCategories(
            @Parameter(description = "导入文件") @RequestParam("file") org.springframework.web.multipart.MultipartFile file) {
        log.info("导入帮助分类: {}", file.getOriginalFilename());
        
        List<HelpCategory> importedCategories = helpCategoryService.importCategories(file);
        return ResponseEntity.status(HttpStatus.CREATED).body(importedCategories);
    }

    @GetMapping("/next-sort-order")
    @Operation(summary = "获取下一个排序号", description = "获取指定父分类下的下一个排序号")
    public ResponseEntity<Integer> getNextSortOrder(
            @Parameter(description = "父分类ID") @RequestParam(required = false) Long parentId) {
        
        Integer nextSortOrder = helpCategoryService.getNextSortOrder(parentId);
        return ResponseEntity.ok(nextSortOrder);
    }

    @GetMapping("/check-name")
    @Operation(summary = "检查名称是否存在", description = "检查分类名称是否已存在")
    public ResponseEntity<Boolean> checkNameExists(
            @Parameter(description = "分类名称") @RequestParam String name,
            @Parameter(description = "父分类ID") @RequestParam(required = false) Long parentId,
            @Parameter(description = "排除的分类ID") @RequestParam(required = false) Long excludeId) {
        
        boolean exists = helpCategoryService.checkNameExists(name, parentId, excludeId);
        return ResponseEntity.ok(exists);
    }

    @GetMapping("/check-path")
    @Operation(summary = "检查路径是否存在", description = "检查分类路径是否已存在")
    public ResponseEntity<Boolean> checkPathExists(
            @Parameter(description = "分类路径") @RequestParam String path,
            @Parameter(description = "排除的分类ID") @RequestParam(required = false) Long excludeId) {
        
        boolean exists = helpCategoryService.checkPathExists(path, excludeId);
        return ResponseEntity.ok(exists);
    }
}