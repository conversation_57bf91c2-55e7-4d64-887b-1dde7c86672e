package com.linkboms.entity.help;

import jakarta.persistence.*;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 帮助反馈实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Table(name = "help_feedback")
@EntityListeners(AuditingEntityListener.class)
@Data
@EqualsAndHashCode(callSuper = false)
public class HelpFeedback {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "内容ID不能为空")
    @Column(name = "content_id", nullable = false)
    private Long contentId;

    @NotNull(message = "反馈类型不能为空")
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private FeedbackType type;

    @Min(value = 1, message = "评分不能小于1")
    @Max(value = 5, message = "评分不能大于5")
    @Column(name = "rating")
    private Integer rating;

    @Size(max = 1000, message = "反馈内容长度不能超过1000个字符")
    @Column(length = 1000)
    private String content;

    @Size(max = 100, message = "联系方式长度不能超过100个字符")
    @Column(length = 100)
    private String contact;

    @NotNull(message = "状态不能为空")
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private FeedbackStatus status = FeedbackStatus.PENDING;

    @Size(max = 1000, message = "回复内容长度不能超过1000个字符")
    @Column(length = 1000)
    private String reply;

    @Size(max = 100, message = "回复者名称长度不能超过100个字符")
    @Column(length = 100)
    private String replier;

    @Column(name = "replier_id")
    private Long replierId;

    @Column(name = "replied_at")
    private LocalDateTime repliedAt;

    @Column(name = "is_anonymous", nullable = false)
    private Boolean anonymous = false;

    @Column(name = "is_deleted", nullable = false)
    private Boolean deleted = false;

    @Size(max = 100, message = "提交者名称长度不能超过100个字符")
    @Column(length = 100)
    private String submitter;

    @Column(name = "submitter_id")
    private Long submitterId;

    @Size(max = 45, message = "IP地址长度不能超过45个字符")
    @Column(name = "ip_address", length = 45)
    private String ipAddress;

    @Size(max = 500, message = "用户代理长度不能超过500个字符")
    @Column(name = "user_agent", length = 500)
    private String userAgent;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // 反馈类型枚举
    public enum FeedbackType {
        LIKE("点赞"),
        DISLIKE("点踩"),
        COMMENT("评论"),
        SUGGESTION("建议"),
        BUG_REPORT("错误报告"),
        CONTENT_ERROR("内容错误"),
        IMPROVEMENT("改进建议");

        private final String description;

        FeedbackType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 反馈状态枚举
    public enum FeedbackStatus {
        PENDING("待处理"),
        PROCESSING("处理中"),
        RESOLVED("已解决"),
        REJECTED("已拒绝"),
        CLOSED("已关闭");

        private final String description;

        FeedbackStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 回复反馈
     */
    public void reply(String replyContent, String replierName, Long replierId) {
        this.reply = replyContent;
        this.replier = replierName;
        this.replierId = replierId;
        this.repliedAt = LocalDateTime.now();
        this.status = FeedbackStatus.RESOLVED;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 处理反馈
     */
    public void process() {
        this.status = FeedbackStatus.PROCESSING;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 解决反馈
     */
    public void resolve() {
        this.status = FeedbackStatus.RESOLVED;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 拒绝反馈
     */
    public void reject() {
        this.status = FeedbackStatus.REJECTED;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 关闭反馈
     */
    public void close() {
        this.status = FeedbackStatus.CLOSED;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 软删除
     */
    public void softDelete() {
        this.deleted = true;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 恢复删除
     */
    public void restore() {
        this.deleted = false;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 是否为正面反馈
     */
    public boolean isPositive() {
        return type == FeedbackType.LIKE || (rating != null && rating >= 4);
    }

    /**
     * 是否为负面反馈
     */
    public boolean isNegative() {
        return type == FeedbackType.DISLIKE || (rating != null && rating <= 2);
    }
}
