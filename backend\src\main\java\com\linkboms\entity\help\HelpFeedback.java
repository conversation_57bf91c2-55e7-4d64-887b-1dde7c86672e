package com.linkboms.entity.help;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 帮助反馈实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Table(name = "help_feedback")
@EntityListeners(AuditingEntityListener.class)
public class HelpFeedback {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "内容ID不能为空")
    @Column(name = "content_id", nullable = false)
    private Long contentId;

    @NotNull(message = "用户ID不能为空")
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Size(max = 100, message = "用户名长度不能超过100个字符")
    @Column(name = "user_name", length = 100)
    private String userName;

    @NotNull(message = "评分不能为空")
    @Min(value = 1, message = "评分不能小于1")
    @Max(value = 5, message = "评分不能大于5")
    @Column(nullable = false)
    private Integer rating;

    @Size(max = 1000, message = "评论长度不能超过1000个字符")
    @Column(columnDefinition = "TEXT")
    private String comment;

    @Column(name = "is_helpful")
    private Boolean helpful;

    @Size(max = 1000, message = "建议长度不能超过1000个字符")
    @Column(columnDefinition = "TEXT")
    private String suggestions;

    @Enumerated(EnumType.STRING)
    @Column(length = 20)
    private FeedbackType type;

    @Enumerated(EnumType.STRING)
    @Column(length = 20, nullable = false)
    private FeedbackStatus status = FeedbackStatus.PENDING;

    @Size(max = 500, message = "管理员回复长度不能超过500个字符")
    @Column(name = "admin_reply", columnDefinition = "TEXT")
    private String adminReply;

    @Column(name = "admin_id")
    private Long adminId;

    @Size(max = 100, message = "管理员名称长度不能超过100个字符")
    @Column(name = "admin_name", length = 100)
    private String adminName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "replied_at")
    private LocalDateTime repliedAt;

    @Column(name = "ip_address", length = 45)
    private String ipAddress;

    @Column(name = "user_agent", length = 500)
    private String userAgent;

    @Column(name = "is_anonymous", nullable = false)
    private Boolean anonymous = false;

    @Column(name = "deleted", nullable = false)
    private Boolean deleted = false;

    @CreatedDate
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    // 反馈类型枚举
    public enum FeedbackType {
        GENERAL("一般反馈"),
        BUG_REPORT("错误报告"),
        IMPROVEMENT("改进建议"),
        CONTENT_ERROR("内容错误"),
        FEATURE_REQUEST("功能请求"),
        COMPLIMENT("表扬"),
        COMPLAINT("投诉");

        private final String description;

        FeedbackType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 反馈状态枚举
    public enum FeedbackStatus {
        PENDING("待处理"),
        PROCESSING("处理中"),
        RESOLVED("已解决"),
        CLOSED("已关闭"),
        REJECTED("已拒绝");

        private final String description;

        FeedbackStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // Constructors
    public HelpFeedback() {}

    public HelpFeedback(Long contentId, Long userId, Integer rating) {
        this.contentId = contentId;
        this.userId = userId;
        this.rating = rating;
    }

    // Business Methods
    
    /**
     * 是否为正面反馈
     */
    public boolean isPositive() {
        return rating != null && rating >= 4;
    }

    /**
     * 是否为负面反馈
     */
    public boolean isNegative() {
        return rating != null && rating <= 2;
    }

    /**
     * 是否已回复
     */
    public boolean isReplied() {
        return adminReply != null && !adminReply.trim().isEmpty();
    }

    /**
     * 标记为已解决
     */
    public void markAsResolved(Long adminId, String adminName, String reply) {
        this.status = FeedbackStatus.RESOLVED;
        this.adminId = adminId;
        this.adminName = adminName;
        this.adminReply = reply;
        this.repliedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getContentId() {
        return contentId;
    }

    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getRating() {
        return rating;
    }

    public void setRating(Integer rating) {
        this.rating = rating;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public Boolean getHelpful() {
        return helpful;
    }

    public void setHelpful(Boolean helpful) {
        this.helpful = helpful;
    }

    public String getSuggestions() {
        return suggestions;
    }

    public void setSuggestions(String suggestions) {
        this.suggestions = suggestions;
    }

    public FeedbackType getType() {
        return type;
    }

    public void setType(FeedbackType type) {
        this.type = type;
    }

    public FeedbackStatus getStatus() {
        return status;
    }

    public void setStatus(FeedbackStatus status) {
        this.status = status;
    }

    public String getAdminReply() {
        return adminReply;
    }

    public void setAdminReply(String adminReply) {
        this.adminReply = adminReply;
    }

    public Long getAdminId() {
        return adminId;
    }

    public void setAdminId(Long adminId) {
        this.adminId = adminId;
    }

    public String getAdminName() {
        return adminName;
    }

    public void setAdminName(String adminName) {
        this.adminName = adminName;
    }

    public LocalDateTime getRepliedAt() {
        return repliedAt;
    }

    public void setRepliedAt(LocalDateTime repliedAt) {
        this.repliedAt = repliedAt;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public Boolean getAnonymous() {
        return anonymous;
    }

    public void setAnonymous(Boolean anonymous) {
        this.anonymous = anonymous;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    @Override
    public String toString() {
        return "HelpFeedback{" +
                "id=" + id +
                ", contentId=" + contentId +
                ", userId=" + userId +
                ", rating=" + rating +
                ", helpful=" + helpful +
                ", status=" + status +
                ", createdAt=" + createdAt +
                '}';
    }
}