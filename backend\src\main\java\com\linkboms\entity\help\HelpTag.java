package com.linkboms.entity.help;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 帮助标签实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Table(name = "help_tag", indexes = {
    @Index(name = "idx_help_tag_name", columnList = "name"),
    @Index(name = "idx_help_tag_type", columnList = "type"),
    @Index(name = "idx_help_tag_enabled", columnList = "enabled")
})
@EntityListeners(AuditingEntityListener.class)
public class HelpTag {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "标签名称不能为空")
    @Size(max = 50, message = "标签名称长度不能超过50个字符")
    @Column(nullable = false, unique = true, length = 50)
    private String name;

    @Size(max = 200, message = "标签描述长度不能超过200个字符")
    @Column(length = 200)
    private String description;

    @Size(max = 20, message = "标签颜色长度不能超过20个字符")
    @Column(length = 20)
    private String color;

    @Size(max = 50, message = "标签图标长度不能超过50个字符")
    @Column(length = 50)
    private String icon;

    @Enumerated(EnumType.STRING)
    @Column(length = 20, nullable = false)
    private TagType type = TagType.GENERAL;

    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;

    @Column(name = "usage_count", nullable = false)
    private Integer usageCount = 0;

    @Column(nullable = false)
    private Boolean enabled = true;

    @Column(nullable = false)
    private Boolean deleted = false;

    @Size(max = 100, message = "创建者长度不能超过100个字符")
    @Column(name = "created_by", length = 100)
    private String createdBy;

    @Size(max = 100, message = "更新者长度不能超过100个字符")
    @Column(name = "updated_by", length = 100)
    private String updatedBy;

    @CreatedDate
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // 多对多关系：标签与内容
    @ManyToMany(mappedBy = "tags", fetch = FetchType.LAZY)
    private Set<HelpContent> contents = new HashSet<>();

    // 标签类型枚举
    public enum TagType {
        GENERAL("通用标签"),
        FUNCTION("功能标签"),
        ROLE("角色标签"),
        KNOWLEDGE("知识标签"),
        WORKFLOW("工作流标签"),
        BUSINESS("业务标签"),
        DIFFICULTY("难度标签"),
        PRIORITY("优先级标签"),
        STATUS("状态标签"),
        CATEGORY("分类标签");

        private final String description;

        TagType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // Constructors
    public HelpTag() {}

    public HelpTag(String name) {
        this.name = name;
    }

    public HelpTag(String name, String description, TagType type) {
        this.name = name;
        this.description = description;
        this.type = type;
    }

    // Business Methods
    
    /**
     * 增加使用次数
     */
    public void incrementUsage() {
        this.usageCount++;
    }

    /**
     * 减少使用次数
     */
    public void decrementUsage() {
        if (this.usageCount > 0) {
            this.usageCount--;
        }
    }

    /**
     * 是否为热门标签
     */
    public boolean isPopular() {
        return usageCount != null && usageCount >= 10;
    }

    /**
     * 是否可用
     */
    public boolean isAvailable() {
        return enabled != null && enabled && (deleted == null || !deleted);
    }

    /**
     * 获取标签的完整显示名称
     */
    public String getDisplayName() {
        if (description != null && !description.trim().isEmpty()) {
            return name + " (" + description + ")";
        }
        return name;
    }

    /**
     * 获取标签样式类名
     */
    public String getStyleClass() {
        StringBuilder styleClass = new StringBuilder("tag");
        if (type != null) {
            styleClass.append(" tag-").append(type.name().toLowerCase());
        }
        if (color != null && !color.trim().isEmpty()) {
            styleClass.append(" tag-color-").append(color);
        }
        return styleClass.toString();
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public TagType getType() {
        return type;
    }

    public void setType(TagType type) {
        this.type = type;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Integer getUsageCount() {
        return usageCount;
    }

    public void setUsageCount(Integer usageCount) {
        this.usageCount = usageCount;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Set<HelpContent> getContents() {
        return contents;
    }

    public void setContents(Set<HelpContent> contents) {
        this.contents = contents;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof HelpTag)) return false;
        HelpTag helpTag = (HelpTag) o;
        return name != null && name.equals(helpTag.name);
    }

    @Override
    public int hashCode() {
        return name != null ? name.hashCode() : 0;
    }

    @Override
    public String toString() {
        return "HelpTag{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", type=" + type +
                ", usageCount=" + usageCount +
                ", enabled=" + enabled +
                '}';
    }
}