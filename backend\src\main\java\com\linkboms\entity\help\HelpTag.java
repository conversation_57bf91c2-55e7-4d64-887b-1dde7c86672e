package com.linkboms.entity.help;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 帮助标签实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Table(name = "help_tag")
@EntityListeners(AuditingEntityListener.class)
@Data
@EqualsAndHashCode(callSuper = false)
public class HelpTag {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "标签名称不能为空")
    @Size(max = 50, message = "标签名称长度不能超过50个字符")
    @Column(nullable = false, length = 50, unique = true)
    private String name;

    @Size(max = 200, message = "标签描述长度不能超过200个字符")
    @Column(length = 200)
    private String description;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private TagType type = TagType.CONTENT;

    @Size(max = 20, message = "标签颜色长度不能超过20个字符")
    @Column(length = 20)
    private String color;

    @Column(name = "usage_count", nullable = false)
    private Long usageCount = 0L;

    @Column(name = "is_enabled", nullable = false)
    private Boolean enabled = true;

    @Column(name = "is_deleted", nullable = false)
    private Boolean deleted = false;

    @Size(max = 100, message = "创建者名称长度不能超过100个字符")
    @Column(length = 100)
    private String creator;

    @Column(name = "creator_id")
    private Long creatorId;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // 关联的内容
    @ManyToMany(mappedBy = "tags", fetch = FetchType.LAZY)
    private Set<HelpContent> contents = new HashSet<>();

    // 标签类型枚举
    public enum TagType {
        CONTENT("内容标签"),
        CATEGORY("分类标签"),
        SYSTEM("系统标签"),
        CUSTOM("自定义标签");

        private final String description;

        TagType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 增加使用次数
     */
    public void incrementUsageCount() {
        this.usageCount++;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 减少使用次数
     */
    public void decrementUsageCount() {
        if (this.usageCount > 0) {
            this.usageCount--;
            this.updatedAt = LocalDateTime.now();
        }
    }

    /**
     * 启用标签
     */
    public void enable() {
        this.enabled = true;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 禁用标签
     */
    public void disable() {
        this.enabled = false;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 软删除
     */
    public void softDelete() {
        this.deleted = true;
        this.enabled = false;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 恢复删除
     */
    public void restore() {
        this.deleted = false;
        this.enabled = true;
        this.updatedAt = LocalDateTime.now();
    }
}
