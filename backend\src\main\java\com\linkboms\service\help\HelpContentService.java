package com.linkboms.service.help;

import com.linkboms.entity.help.HelpContent;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 帮助内容服务接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface HelpContentService {

    /**
     * 创建帮助内容
     */
    HelpContent createContent(HelpContent content);

    /**
     * 更新帮助内容
     */
    HelpContent updateContent(Long id, HelpContent content);

    /**
     * 根据ID获取帮助内容
     */
    HelpContent getContentById(Long id);

    /**
     * 根据ID获取帮助内容（包含已删除）
     */
    HelpContent getContentByIdIncludeDeleted(Long id);

    /**
     * 删除帮助内容（软删除）
     */
    void deleteContent(Long id);

    /**
     * 批量删除帮助内容
     */
    void batchDeleteContent(List<Long> ids);

    /**
     * 恢复已删除的帮助内容
     */
    void restoreContent(Long id);

    /**
     * 永久删除帮助内容
     */
    void permanentDeleteContent(Long id);

    /**
     * 分页查询帮助内容
     */
    Page<HelpContent> getContentList(Pageable pageable);

    /**
     * 根据类型分页查询帮助内容
     */
    Page<HelpContent> getContentByType(HelpContent.ContentType type, Pageable pageable);

    /**
     * 根据分类分页查询帮助内容
     */
    Page<HelpContent> getContentByCategory(Long categoryId, Pageable pageable);

    /**
     * 根据状态分页查询帮助内容
     */
    Page<HelpContent> getContentByStatus(HelpContent.ContentStatus status, Pageable pageable);

    /**
     * 根据作者分页查询帮助内容
     */
    Page<HelpContent> getContentByAuthor(String author, Pageable pageable);

    /**
     * 搜索帮助内容
     */
    Page<HelpContent> searchContent(String keyword, Pageable pageable);

    /**
     * 高级搜索帮助内容
     */
    Page<HelpContent> advancedSearchContent(
        String keyword,
        HelpContent.ContentType type,
        Long categoryId,
        HelpContent.ContentStatus status,
        String author,
        LocalDateTime startDate,
        LocalDateTime endDate,
        List<String> tags,
        Pageable pageable
    );

    /**
     * 全文搜索帮助内容
     */
    Page<HelpContent> fullTextSearch(String keyword, Pageable pageable);

    /**
     * 获取热门内容
     */
    List<HelpContent> getPopularContent(int limit);

    /**
     * 获取最新内容
     */
    List<HelpContent> getLatestContent(int limit);

    /**
     * 获取推荐内容
     */
    List<HelpContent> getRecommendedContent(int limit);

    /**
     * 获取特色内容
     */
    List<HelpContent> getFeaturedContent(int limit);

    /**
     * 获取置顶内容
     */
    List<HelpContent> getPinnedContent();

    /**
     * 获取相关内容
     */
    List<HelpContent> getRelatedContent(Long contentId, int limit);

    /**
     * 增加浏览量
     */
    void incrementViewCount(Long id);

    /**
     * 增加点赞量
     */
    void incrementLikeCount(Long id);

    /**
     * 减少点赞量
     */
    void decrementLikeCount(Long id);

    /**
     * 发布内容
     */
    void publishContent(Long id, String reviewer);

    /**
     * 撤回发布
     */
    void unpublishContent(Long id);

    /**
     * 审核通过
     */
    void approveContent(Long id, String reviewer);

    /**
     * 审核拒绝
     */
    void rejectContent(Long id, String reviewer, String reason);

    /**
     * 设置为特色内容
     */
    void setFeatured(Long id, boolean featured);

    /**
     * 设置为置顶内容
     */
    void setPinned(Long id, boolean pinned);

    /**
     * 批量更新状态
     */
    void batchUpdateStatus(List<Long> ids, HelpContent.ContentStatus status);

    /**
     * 添加标签
     */
    void addTag(Long contentId, String tagName);

    /**
     * 移除标签
     */
    void removeTag(Long contentId, String tagName);

    /**
     * 批量添加标签
     */
    void batchAddTags(Long contentId, List<String> tagNames);

    /**
     * 上传附件
     */
    void uploadAttachment(Long contentId, MultipartFile file, String description);

    /**
     * 批量上传附件
     */
    void batchUploadAttachments(Long contentId, List<MultipartFile> files, List<String> descriptions);

    /**
     * 删除附件
     */
    void deleteAttachment(Long contentId, Long attachmentId);

    /**
     * 复制内容
     */
    HelpContent copyContent(Long id, String newTitle);

    /**
     * 导出内容
     */
    byte[] exportContent(Long id, String format);

    /**
     * 批量导出内容
     */
    byte[] batchExportContent(List<Long> ids, String format);

    /**
     * 导入内容
     */
    List<HelpContent> importContent(MultipartFile file);

    /**
     * 获取内容统计信息
     */
    Map<String, Object> getContentStatistics();

    /**
     * 获取内容统计信息（按类型）
     */
    Map<HelpContent.ContentType, Long> getContentStatisticsByType();

    /**
     * 获取内容统计信息（按状态）
     */
    Map<HelpContent.ContentStatus, Long> getContentStatisticsByStatus();

    /**
     * 获取内容统计信息（按分类）
     */
    Map<String, Long> getContentStatisticsByCategory();

    /**
     * 获取内容统计信息（按作者）
     */
    Map<String, Long> getContentStatisticsByAuthor();

    /**
     * 获取内容统计信息（按时间）
     */
    Map<String, Long> getContentStatisticsByDate(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 清理过期内容
     */
    void cleanupExpiredContent(int days);

    /**
     * 重建搜索索引
     */
    void rebuildSearchIndex();

    /**
     * 验证内容
     */
    List<String> validateContent(HelpContent content);

    /**
     * 预览内容
     */
    String previewContent(Long id);

    /**
     * 生成内容摘要
     */
    String generateSummary(String content, int maxLength);

    /**
     * 检查内容是否存在
     */
    boolean existsById(Long id);

    /**
     * 检查标题是否重复
     */
    boolean existsByTitle(String title);

    /**
     * 检查标题是否重复（排除指定ID）
     */
    boolean existsByTitleAndIdNot(String title, Long id);

    /**
     * 获取内容版本历史
     */
    List<HelpContent> getContentVersionHistory(Long id);

    /**
     * 创建内容版本
     */
    HelpContent createContentVersion(Long id, String versionNote);

    /**
     * 恢复到指定版本
     */
    HelpContent restoreToVersion(Long id, String version);

    /**
     * 比较内容版本
     */
    Map<String, Object> compareVersions(Long id, String version1, String version2);
}