package com.linkboms.service.help;

import com.linkboms.entity.help.HelpContent;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 帮助内容服务接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface HelpContentService {

    /**
     * 创建内容
     */
    HelpContent createContent(HelpContent content);

    /**
     * 更新内容
     */
    HelpContent updateContent(Long id, HelpContent content);

    /**
     * 根据ID获取内容
     */
    HelpContent getContentById(Long id);

    /**
     * 根据ID获取内容（可选）
     */
    Optional<HelpContent> findContentById(Long id);

    /**
     * 删除内容
     */
    void deleteContent(Long id);

    /**
     * 软删除内容
     */
    void softDeleteContent(Long id);

    /**
     * 恢复内容
     */
    void restoreContent(Long id);

    /**
     * 获取所有内容（分页）
     */
    Page<HelpContent> getAllContent(Pageable pageable);

    /**
     * 根据分类获取内容
     */
    Page<HelpContent> getContentByCategory(Long categoryId, Pageable pageable);

    /**
     * 根据类型获取内容
     */
    Page<HelpContent> getContentByType(HelpContent.ContentType type, Pageable pageable);

    /**
     * 根据状态获取内容
     */
    Page<HelpContent> getContentByStatus(HelpContent.ContentStatus status, Pageable pageable);

    /**
     * 根据作者获取内容
     */
    Page<HelpContent> getContentByAuthor(Long authorId, Pageable pageable);

    /**
     * 获取已发布的内容
     */
    Page<HelpContent> getPublishedContent(Pageable pageable);

    /**
     * 获取特色内容
     */
    Page<HelpContent> getFeaturedContent(Pageable pageable);

    /**
     * 获取最新内容
     */
    Page<HelpContent> getLatestContent(Pageable pageable);

    /**
     * 获取热门内容
     */
    Page<HelpContent> getPopularContent(Pageable pageable);

    /**
     * 获取最受欢迎的内容
     */
    Page<HelpContent> getMostLikedContent(Pageable pageable);

    /**
     * 获取待审核内容
     */
    Page<HelpContent> getPendingReviewContent(Pageable pageable);

    /**
     * 全文搜索
     */
    Page<HelpContent> searchContent(String keyword, HelpContent.ContentType type, 
                                   Long categoryId, Pageable pageable);

    /**
     * 高级搜索
     */
    Page<HelpContent> advancedSearchContent(String keyword, HelpContent.ContentType type,
                                           Long categoryId, Long authorId, HelpContent.ContentStatus status,
                                           Boolean featured, LocalDateTime startDate, LocalDateTime endDate,
                                           Pageable pageable);

    /**
     * 根据标签搜索内容
     */
    Page<HelpContent> searchContentByTags(List<String> tagNames, Pageable pageable);

    /**
     * 获取相关内容
     */
    List<HelpContent> getRelatedContent(Long contentId, int limit);

    /**
     * 增加浏览量
     */
    void incrementViewCount(Long id);

    /**
     * 增加点赞数
     */
    void incrementLikeCount(Long id);

    /**
     * 减少点赞数
     */
    void decrementLikeCount(Long id);

    /**
     * 增加点踩数
     */
    void incrementDislikeCount(Long id);

    /**
     * 发布内容
     */
    void publishContent(Long id, String reviewer, Long reviewerId);

    /**
     * 撤回发布
     */
    void unpublishContent(Long id);

    /**
     * 审核通过
     */
    void approveContent(Long id, String reviewer, Long reviewerId);

    /**
     * 审核拒绝
     */
    void rejectContent(Long id, String reviewer, Long reviewerId, String reason);

    /**
     * 设置特色内容
     */
    void setFeaturedContent(Long id, boolean featured);

    /**
     * 添加标签
     */
    void addTag(Long contentId, String tagName);

    /**
     * 移除标签
     */
    void removeTag(Long contentId, String tagName);

    /**
     * 批量更新状态
     */
    void batchUpdateStatus(List<Long> ids, HelpContent.ContentStatus status);

    /**
     * 批量删除内容
     */
    void batchDeleteContent(List<Long> ids);

    /**
     * 批量发布内容
     */
    void batchPublishContent(List<Long> ids, String reviewer, Long reviewerId);

    /**
     * 批量设置特色
     */
    void batchSetFeatured(List<Long> ids, boolean featured);

    /**
     * 复制内容
     */
    HelpContent copyContent(Long id, String newTitle);

    /**
     * 移动内容到其他分类
     */
    void moveContentToCategory(Long contentId, Long newCategoryId);

    /**
     * 调整内容排序
     */
    void adjustContentSortOrder(Long id, Integer newSortOrder);

    /**
     * 验证内容数据
     */
    void validateContent(HelpContent content);

    /**
     * 检查标题是否存在
     */
    boolean isContentTitleExists(String title);

    /**
     * 检查标题是否存在（排除指定ID）
     */
    boolean isContentTitleExists(String title, Long excludeId);

    /**
     * 获取内容统计信息
     */
    Object getContentStatistics();

    /**
     * 获取内容状态统计
     */
    Object getContentStatusStatistics();

    /**
     * 获取内容类型统计
     */
    Object getContentTypeStatistics();

    /**
     * 获取内容分类统计
     */
    Object getContentCategoryStatistics();

    /**
     * 查找即将过期的内容
     */
    List<HelpContent> findExpiringContent(LocalDateTime expireDate);

    /**
     * 查找指定时间段内的热门内容
     */
    List<HelpContent> findPopularContentInPeriod(LocalDateTime startDate, LocalDateTime endDate, int limit);

    /**
     * 导出内容数据
     */
    byte[] exportContent(List<Long> contentIds);

    /**
     * 导入内容数据
     */
    void importContent(byte[] data);

    /**
     * 生成内容摘要
     */
    String generateContentSummary(String content, int maxLength);

    /**
     * 提取内容关键词
     */
    List<String> extractContentKeywords(String content, int maxCount);

    /**
     * 内容质量评分
     */
    Double calculateContentQualityScore(Long contentId);

    /**
     * 推荐内容
     */
    List<HelpContent> recommendContent(Long userId, int limit);
}
