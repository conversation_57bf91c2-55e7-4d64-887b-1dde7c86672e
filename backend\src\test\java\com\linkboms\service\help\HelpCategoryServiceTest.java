package com.linkboms.service.help;

import com.linkboms.entity.help.HelpCategory;
import com.linkboms.repository.help.HelpCategoryRepository;
import com.linkboms.service.help.impl.HelpCategoryServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 帮助分类服务测试类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class HelpCategoryServiceTest {

    @Mock
    private HelpCategoryRepository categoryRepository;

    @InjectMocks
    private HelpCategoryServiceImpl categoryService;

    private HelpCategory testCategory;
    private HelpCategory parentCategory;

    @BeforeEach
    void setUp() {
        // 创建测试用的父分类
        parentCategory = new HelpCategory();
        parentCategory.setId(1L);
        parentCategory.setName("功能指南");
        parentCategory.setDescription("系统功能指南");
        parentCategory.setLevel(0);
        parentCategory.setSortOrder(1);
        parentCategory.setPath("/功能指南");
        parentCategory.setVisible(true);
        parentCategory.setEnabled(true);
        parentCategory.setDeleted(false);
        parentCategory.setCreatedAt(LocalDateTime.now());
        parentCategory.setUpdatedAt(LocalDateTime.now());

        // 创建测试用的子分类
        testCategory = new HelpCategory();
        testCategory.setId(2L);
        testCategory.setName("BOM管理");
        testCategory.setDescription("BOM创建和管理功能");
        testCategory.setParentId(1L);
        testCategory.setLevel(1);
        testCategory.setSortOrder(1);
        testCategory.setPath("/功能指南/BOM管理");
        testCategory.setVisible(true);
        testCategory.setEnabled(true);
        testCategory.setDeleted(false);
        testCategory.setCreatedAt(LocalDateTime.now());
        testCategory.setUpdatedAt(LocalDateTime.now());
    }

    @Test
    void testCreateCategory() {
        // 准备测试数据
        HelpCategory newCategory = new HelpCategory();
        newCategory.setName("物料管理");
        newCategory.setDescription("物料信息管理");
        newCategory.setParentId(1L);
        newCategory.setVisible(true);
        newCategory.setEnabled(true);

        // Mock 行为
        when(categoryRepository.existsByNameAndDeletedFalse(anyString())).thenReturn(false);
        when(categoryRepository.existsById(1L)).thenReturn(true);
        when(categoryRepository.findById(1L)).thenReturn(Optional.of(parentCategory));
        when(categoryRepository.findMaxSortOrderByParentId(1L)).thenReturn(0);
        when(categoryRepository.save(any(HelpCategory.class))).thenReturn(testCategory);

        // 执行测试
        HelpCategory result = categoryService.createCategory(newCategory);

        // 验证结果
        assertNotNull(result);
        assertEquals("BOM管理", result.getName());
        assertEquals(1, result.getLevel());
        assertEquals("/功能指南/BOM管理", result.getPath());
        
        // 验证方法调用
        verify(categoryRepository).save(any(HelpCategory.class));
    }

    @Test
    void testCreateCategoryWithDuplicateName() {
        // 准备测试数据
        HelpCategory newCategory = new HelpCategory();
        newCategory.setName("BOM管理");

        // Mock 行为 - 名称已存在
        when(categoryRepository.existsByNameAndDeletedFalse("BOM管理")).thenReturn(true);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            categoryService.createCategory(newCategory);
        });

        assertEquals("分类名称已存在: BOM管理", exception.getMessage());
    }

    @Test
    void testGetCategoryById() {
        // Mock 行为
        when(categoryRepository.findById(2L)).thenReturn(Optional.of(testCategory));

        // 执行测试
        HelpCategory result = categoryService.getCategoryById(2L);

        // 验证结果
        assertNotNull(result);
        assertEquals(2L, result.getId());
        assertEquals("BOM管理", result.getName());
    }

    @Test
    void testGetCategoryByIdNotFound() {
        // Mock 行为
        when(categoryRepository.findById(999L)).thenReturn(Optional.empty());

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            categoryService.getCategoryById(999L);
        });

        assertEquals("帮助分类不存在: 999", exception.getMessage());
    }

    @Test
    void testUpdateCategory() {
        // 准备测试数据
        HelpCategory updateData = new HelpCategory();
        updateData.setName("BOM管理更新");
        updateData.setDescription("更新后的描述");
        updateData.setVisible(false);
        updateData.setEnabled(false);

        // Mock 行为
        when(categoryRepository.findById(2L)).thenReturn(Optional.of(testCategory));
        when(categoryRepository.existsByNameAndIdNotAndDeletedFalse("BOM管理更新", 2L)).thenReturn(false);
        when(categoryRepository.save(any(HelpCategory.class))).thenReturn(testCategory);

        // 执行测试
        HelpCategory result = categoryService.updateCategory(2L, updateData);

        // 验证结果
        assertNotNull(result);
        verify(categoryRepository).save(any(HelpCategory.class));
    }

    @Test
    void testDeleteCategory() {
        // Mock 行为
        when(categoryRepository.findById(2L)).thenReturn(Optional.of(testCategory));
        when(categoryRepository.findByParentIdAndDeletedFalseOrderBySortOrderAsc(2L)).thenReturn(Arrays.asList());
        when(categoryRepository.countContentsByCategoryId(2L)).thenReturn(0L);

        // 执行测试
        categoryService.deleteCategory(2L);

        // 验证方法调用
        verify(categoryRepository).delete(testCategory);
    }

    @Test
    void testDeleteCategoryWithChildren() {
        // 准备子分类
        HelpCategory childCategory = new HelpCategory();
        childCategory.setId(3L);
        childCategory.setParentId(2L);

        // Mock 行为
        when(categoryRepository.findById(2L)).thenReturn(Optional.of(testCategory));
        when(categoryRepository.findByParentIdAndDeletedFalseOrderBySortOrderAsc(2L))
            .thenReturn(Arrays.asList(childCategory));

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            categoryService.deleteCategory(2L);
        });

        assertEquals("存在子分类，无法删除", exception.getMessage());
    }

    @Test
    void testSoftDeleteCategory() {
        // Mock 行为
        when(categoryRepository.findById(2L)).thenReturn(Optional.of(testCategory));
        when(categoryRepository.findAllDescendants(anyString())).thenReturn(Arrays.asList());
        when(categoryRepository.save(any(HelpCategory.class))).thenReturn(testCategory);

        // 执行测试
        categoryService.softDeleteCategory(2L);

        // 验证方法调用
        verify(categoryRepository, atLeastOnce()).save(any(HelpCategory.class));
    }

    @Test
    void testGetAllCategories() {
        // 准备测试数据
        List<HelpCategory> categories = Arrays.asList(parentCategory, testCategory);
        Page<HelpCategory> page = new PageImpl<>(categories);
        Pageable pageable = PageRequest.of(0, 10);

        // Mock 行为
        when(categoryRepository.findAll(pageable)).thenReturn(page);

        // 执行测试
        Page<HelpCategory> result = categoryService.getAllCategories(pageable);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getContent().size());
    }

    @Test
    void testGetRootCategories() {
        // Mock 行为
        when(categoryRepository.findByParentIdIsNullAndEnabledTrueAndDeletedFalseOrderBySortOrderAsc())
            .thenReturn(Arrays.asList(parentCategory));

        // 执行测试
        List<HelpCategory> result = categoryService.getRootCategories();

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("功能指南", result.get(0).getName());
    }

    @Test
    void testGetChildCategories() {
        // Mock 行为
        when(categoryRepository.findByParentIdAndEnabledTrueAndDeletedFalseOrderBySortOrderAsc(1L))
            .thenReturn(Arrays.asList(testCategory));

        // 执行测试
        List<HelpCategory> result = categoryService.getChildCategories(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("BOM管理", result.get(0).getName());
    }

    @Test
    void testSearchCategories() {
        // Mock 行为
        when(categoryRepository.searchCategories("BOM")).thenReturn(Arrays.asList(testCategory));

        // 执行测试
        List<HelpCategory> result = categoryService.searchCategories("BOM");

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("BOM管理", result.get(0).getName());
    }

    @Test
    void testEnableCategory() {
        // Mock 行为
        when(categoryRepository.findById(2L)).thenReturn(Optional.of(testCategory));
        when(categoryRepository.save(any(HelpCategory.class))).thenReturn(testCategory);

        // 执行测试
        categoryService.enableCategory(2L);

        // 验证方法调用
        verify(categoryRepository).save(any(HelpCategory.class));
    }

    @Test
    void testDisableCategory() {
        // Mock 行为
        when(categoryRepository.findById(2L)).thenReturn(Optional.of(testCategory));
        when(categoryRepository.save(any(HelpCategory.class))).thenReturn(testCategory);

        // 执行测试
        categoryService.disableCategory(2L);

        // 验证方法调用
        verify(categoryRepository).save(any(HelpCategory.class));
    }

    @Test
    void testIsCategoryNameExists() {
        // Mock 行为
        when(categoryRepository.existsByNameAndDeletedFalse("BOM管理")).thenReturn(true);
        when(categoryRepository.existsByNameAndDeletedFalse("新分类")).thenReturn(false);

        // 执行测试
        assertTrue(categoryService.isCategoryNameExists("BOM管理"));
        assertFalse(categoryService.isCategoryNameExists("新分类"));
    }

    @Test
    void testGetCategoryContentCount() {
        // Mock 行为
        when(categoryRepository.countContentsByCategoryId(2L)).thenReturn(5L);

        // 执行测试
        Long count = categoryService.getCategoryContentCount(2L);

        // 验证结果
        assertEquals(5L, count);
    }
}
