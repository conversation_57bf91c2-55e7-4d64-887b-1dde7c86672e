import React, { useState, useEffect } from 'react';
import {
  Layout,
  Card,
  Row,
  Col,
  Typography,
  Input,
  Select,
  Space,
  Tag,
  Button,
  List,
  Avatar,
  Divider,
  Empty,
  Tabs
} from 'antd';
import {
  SearchOutlined,
  ApartmentOutlined,
  FilterOutlined,
  EyeOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import WorkflowDiagram from '../../components/help/WorkflowDiagram';
import BusinessFlowDiagram from '../../components/help/BusinessFlowDiagram';
import DocumentFlowDiagram from '../../components/help/DocumentFlowDiagram';
import { 
  workflowExamples, 
  getWorkflowById, 
  getWorkflowsByType, 
  getWorkflowsByCategory,
  searchWorkflows 
} from '../../data/workflowExamples';
import { WorkflowDiagram as WorkflowType } from '../../types/help';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;
const { Option } = Select;
const { Content, Sider } = Layout;
const { TabPane } = Tabs;

/**
 * 工作流程图页面
 */
const WorkflowPage: React.FC = () => {
  const { workflowId } = useParams<{ workflowId?: string }>();
  const navigate = useNavigate();
  
  const [selectedWorkflow, setSelectedWorkflow] = useState<WorkflowType | null>(null);
  const [filteredWorkflows, setFilteredWorkflows] = useState<WorkflowType[]>(workflowExamples);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [activeTab, setActiveTab] = useState<string>('workflows');

  useEffect(() => {
    if (workflowId) {
      const workflow = getWorkflowById(workflowId);
      setSelectedWorkflow(workflow || null);
    } else {
      setSelectedWorkflow(null);
    }
  }, [workflowId]);

  useEffect(() => {
    let results = workflowExamples;

    // 关键词搜索
    if (searchKeyword) {
      results = searchWorkflows(searchKeyword);
    }

    // 类型筛选
    if (selectedType) {
      results = results.filter(w => w.type === selectedType);
    }

    // 分类筛选
    if (selectedCategory) {
      results = results.filter(w => w.category === selectedCategory);
    }

    setFilteredWorkflows(results);
  }, [searchKeyword, selectedType, selectedCategory]);

  // 处理工作流程选择
  const handleWorkflowSelect = (workflow: WorkflowType) => {
    setSelectedWorkflow(workflow);
    navigate(`/help/workflows/${workflow.id}`);
  };

  // 清除筛选
  const handleClearFilters = () => {
    setSearchKeyword('');
    setSelectedType('');
    setSelectedCategory('');
  };

  // 获取所有类型
  const getAllTypes = () => {
    const types = Array.from(new Set(workflowExamples.map(w => w.type)));
    return types.map(type => ({
      value: type,
      label: getTypeLabel(type)
    }));
  };

  // 获取所有分类
  const getAllCategories = () => {
    const categories = Array.from(new Set(workflowExamples.map(w => w.category)));
    return categories.map(category => ({
      value: category,
      label: category
    }));
  };

  // 获取类型标签
  const getTypeLabel = (type: string) => {
    const typeLabels = {
      'workflow': '工作流程',
      'business_process': '业务流程',
      'system_flow': '系统流程'
    };
    return typeLabels[type as keyof typeof typeLabels] || type;
  };

  // 获取类型颜色
  const getTypeColor = (type: string) => {
    const typeColors = {
      'workflow': 'blue',
      'business_process': 'green',
      'system_flow': 'orange'
    };
    return typeColors[type as keyof typeof typeColors] || 'default';
  };

  // 渲染工作流程列表
  const renderWorkflowList = () => {
    if (filteredWorkflows.length === 0) {
      return (
        <Empty
          description="没有找到匹配的工作流程"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button type="primary" onClick={handleClearFilters}>
            清除筛选条件
          </Button>
        </Empty>
      );
    }

    return (
      <List
        itemLayout="vertical"
        dataSource={filteredWorkflows}
        renderItem={(workflow) => (
          <List.Item
            key={workflow.id}
            style={{ 
              cursor: 'pointer',
              backgroundColor: selectedWorkflow?.id === workflow.id ? '#f0f8ff' : 'transparent'
            }}
            onClick={() => handleWorkflowSelect(workflow)}
            actions={[
              <Space key="info">
                <EyeOutlined />
                <Text type="secondary">查看详情</Text>
              </Space>
            ]}
          >
            <List.Item.Meta
              avatar={
                <Avatar
                  icon={<ApartmentOutlined />}
                  style={{ backgroundColor: '#1890ff' }}
                />
              }
              title={
                <Space>
                  <span>{workflow.title}</span>
                  <Tag color={getTypeColor(workflow.type)}>
                    {getTypeLabel(workflow.type)}
                  </Tag>
                </Space>
              }
              description={
                <div>
                  <Paragraph ellipsis={{ rows: 2 }} style={{ marginBottom: '8px' }}>
                    {workflow.description}
                  </Paragraph>
                  <Space wrap>
                    <Tag>{workflow.category}</Tag>
                    {workflow.tags.map((tag, index) => (
                      <Tag key={index} color="blue" size="small">
                        {tag}
                      </Tag>
                    ))}
                  </Space>
                </div>
              }
            />
          </List.Item>
        )}
      />
    );
  };

  return (
    <Layout style={{ minHeight: '100vh', background: '#f5f5f5' }}>
      {/* 左侧工作流程列表 */}
      <Sider
        width={400}
        style={{
          background: '#fff',
          borderRight: '1px solid #f0f0f0',
          height: '100vh',
          overflow: 'auto'
        }}
      >
        <div style={{ padding: '24px' }}>
          {/* 标题 */}
          <Title level={4} style={{ marginBottom: '24px' }}>
            <ApartmentOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
            工作流程图
          </Title>

          {/* 搜索和筛选 */}
          <Space direction="vertical" style={{ width: '100%', marginBottom: '24px' }}>
            <Search
              placeholder="搜索工作流程..."
              allowClear
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              style={{ width: '100%' }}
            />
            
            <Select
              placeholder="选择类型"
              allowClear
              value={selectedType}
              onChange={setSelectedType}
              style={{ width: '100%' }}
            >
              {getAllTypes().map(type => (
                <Option key={type.value} value={type.value}>
                  {type.label}
                </Option>
              ))}
            </Select>
            
            <Select
              placeholder="选择分类"
              allowClear
              value={selectedCategory}
              onChange={setSelectedCategory}
              style={{ width: '100%' }}
            >
              {getAllCategories().map(category => (
                <Option key={category.value} value={category.value}>
                  {category.label}
                </Option>
              ))}
            </Select>

            {(searchKeyword || selectedType || selectedCategory) && (
              <Button
                icon={<FilterOutlined />}
                onClick={handleClearFilters}
                style={{ width: '100%' }}
              >
                清除筛选
              </Button>
            )}
          </Space>

          <Divider />

          {/* 工作流程列表 */}
          {renderWorkflowList()}
        </div>
      </Sider>

      {/* 右侧内容区域 */}
      <Content style={{ padding: '24px', background: '#f5f5f5' }}>
        <Card>
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="工作流程图" key="workflows">
              {selectedWorkflow ? (
                <WorkflowDiagram workflow={selectedWorkflow} showDetails={true} />
              ) : (
                <div style={{ textAlign: 'center', padding: '60px 0' }}>
                  <ApartmentOutlined style={{ fontSize: '64px', color: '#d9d9d9', marginBottom: '16px' }} />
                  <Title level={4} type="secondary">
                    选择一个工作流程查看详情
                  </Title>
                  <Text type="secondary">
                    从左侧列表中选择一个工作流程图来查看详细的流程说明和图表
                  </Text>
                </div>
              )}
            </TabPane>
            <TabPane tab="业务流程图" key="business">
              <BusinessFlowDiagram />
            </TabPane>
            <TabPane tab="单据流转图" key="documents">
              <DocumentFlowDiagram />
            </TabPane>
          </Tabs>
        </Card>
      </Content>
    </Layout>
  );
};

export default WorkflowPage;
