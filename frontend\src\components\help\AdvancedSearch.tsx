import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Input, 
  Select, 
  DatePicker, 
  Button, 
  Space, 
  Form, 
  Row, 
  Col,
  Collapse,
  Tag,
  Typography,
  Divider,
  Checkbox,
  Slider,
  AutoComplete
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
  HistoryOutlined,
  BookOutlined,
  TagsOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Panel } = Collapse;
const { Text } = Typography;
const CheckboxGroup = Checkbox.Group;

interface SearchFilters {
  query: string;
  type: string[];
  category: string[];
  author: string[];
  dateRange: [dayjs.Dayjs, dayjs.Dayjs] | null;
  tags: string[];
  difficulty: string[];
  hasImages: boolean;
  hasVideos: boolean;
  hasAttachments: boolean;
}

interface AdvancedSearchProps {
  onSearch: (filters: SearchFilters) => void;
  onSaveSearch?: (name: string, filters: SearchFilters) => void;
  savedSearches?: Array<{ name: string; filters: SearchFilters }>;
  searchHistory?: string[];
  availableCategories?: string[];
  availableAuthors?: string[];
  availableTags?: string[];
}

const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  onSearch,
  onSaveSearch,
  savedSearches = [],
  searchHistory = [],
  availableCategories = [],
  availableAuthors = [],
  availableTags = [],
}) => {
  const [form] = Form.useForm();
  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    type: [],
    category: [],
    author: [],
    dateRange: null,
    tags: [],
    difficulty: [],
    hasImages: false,
    hasVideos: false,
    hasAttachments: false,
  });
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);

  // 搜索建议
  const generateSuggestions = (value: string) => {
    if (!value) {
      setSearchSuggestions(searchHistory.slice(0, 5));
      return;
    }

    const suggestions = [
      ...searchHistory.filter(h => h.toLowerCase().includes(value.toLowerCase())),
      ...availableTags.filter(t => t.toLowerCase().includes(value.toLowerCase())),
    ].slice(0, 8);

    setSearchSuggestions(suggestions);
  };

  const handleSearch = () => {
    onSearch(filters);
  };

  const handleQuickSearch = (query: string) => {
    const newFilters = { ...filters, query };
    setFilters(newFilters);
    onSearch(newFilters);
  };

  const handleClearFilters = () => {
    const clearedFilters: SearchFilters = {
      query: '',
      type: [],
      category: [],
      author: [],
      dateRange: null,
      tags: [],
      difficulty: [],
      hasImages: false,
      hasVideos: false,
      hasAttachments: false,
    };
    setFilters(clearedFilters);
    form.resetFields();
  };

  const handleSaveSearch = () => {
    const name = prompt('请输入搜索名称:');
    if (name && onSaveSearch) {
      onSaveSearch(name, filters);
    }
  };

  const handleLoadSavedSearch = (savedFilters: SearchFilters) => {
    setFilters(savedFilters);
    form.setFieldsValue({
      query: savedFilters.query,
      type: savedFilters.type,
      category: savedFilters.category,
      author: savedFilters.author,
      dateRange: savedFilters.dateRange,
      tags: savedFilters.tags,
      difficulty: savedFilters.difficulty,
      hasImages: savedFilters.hasImages,
      hasVideos: savedFilters.hasVideos,
      hasAttachments: savedFilters.hasAttachments,
    });
    onSearch(savedFilters);
  };

  const typeOptions = [
    { label: '功能指南', value: 'function' },
    { label: '角色权限', value: 'role' },
    { label: '知识库', value: 'knowledge' },
    { label: '工作流程', value: 'workflow' },
    { label: '业务流程', value: 'process' },
  ];

  const difficultyOptions = [
    { label: '初级', value: 'beginner' },
    { label: '中级', value: 'intermediate' },
    { label: '高级', value: 'advanced' },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card title="高级搜索" extra={
        <Space>
          <Button
            icon={<BookOutlined />}
            onClick={handleSaveSearch}
            disabled={!filters.query && filters.type.length === 0}
          >
            保存搜索
          </Button>
          <Button icon={<ClearOutlined />} onClick={handleClearFilters}>
            清空
          </Button>
        </Space>
      }>
        <Form
          form={form}
          layout="vertical"
          onValuesChange={(_, allValues) => {
            setFilters({ ...filters, ...allValues });
          }}
        >
          {/* 基础搜索 */}
          <Form.Item label="搜索关键词" name="query">
            <AutoComplete
              options={searchSuggestions.map(s => ({ value: s }))}
              onSearch={generateSuggestions}
              onSelect={handleQuickSearch}
            >
              <Search
                placeholder="输入关键词搜索..."
                allowClear
                enterButton={<SearchOutlined />}
                onSearch={handleSearch}
                size="large"
              />
            </AutoComplete>
          </Form.Item>

          {/* 快速标签 */}
          {availableTags.length > 0 && (
            <div style={{ marginBottom: '16px' }}>
              <Text type="secondary">热门标签:</Text>
              <div style={{ marginTop: '8px' }}>
                <Space wrap>
                  {availableTags.slice(0, 10).map(tag => (
                    <Tag
                      key={tag}
                      style={{ cursor: 'pointer' }}
                      onClick={() => handleQuickSearch(tag)}
                    >
                      {tag}
                    </Tag>
                  ))}
                </Space>
              </div>
            </div>
          )}

          {/* 高级筛选 */}
          <Collapse 
            ghost 
            activeKey={showAdvanced ? ['advanced'] : []}
            onChange={(keys) => setShowAdvanced(keys.includes('advanced'))}
          >
            <Panel 
              header={
                <Space>
                  <FilterOutlined />
                  <Text>高级筛选</Text>
                </Space>
              } 
              key="advanced"
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="内容类型" name="type">
                    <CheckboxGroup options={typeOptions} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="难度等级" name="difficulty">
                    <CheckboxGroup options={difficultyOptions} />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="分类" name="category">
                    <Select
                      mode="multiple"
                      placeholder="选择分类"
                      allowClear
                    >
                      {availableCategories.map(cat => (
                        <Option key={cat} value={cat}>{cat}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="作者" name="author">
                    <Select
                      mode="multiple"
                      placeholder="选择作者"
                      allowClear
                    >
                      {availableAuthors.map(author => (
                        <Option key={author} value={author}>{author}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="更新时间" name="dateRange">
                    <RangePicker style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="标签" name="tags">
                    <Select
                      mode="tags"
                      placeholder="输入或选择标签"
                      allowClear
                    >
                      {availableTags.map(tag => (
                        <Option key={tag} value={tag}>{tag}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item label="媒体内容">
                <Space direction="vertical">
                  <Form.Item name="hasImages" valuePropName="checked" style={{ margin: 0 }}>
                    <Checkbox>包含图片</Checkbox>
                  </Form.Item>
                  <Form.Item name="hasVideos" valuePropName="checked" style={{ margin: 0 }}>
                    <Checkbox>包含视频</Checkbox>
                  </Form.Item>
                  <Form.Item name="hasAttachments" valuePropName="checked" style={{ margin: 0 }}>
                    <Checkbox>包含附件</Checkbox>
                  </Form.Item>
                </Space>
              </Form.Item>
            </Panel>
          </Collapse>

          {/* 搜索按钮 */}
          <div style={{ textAlign: 'center', marginTop: '24px' }}>
            <Button 
              type="primary" 
              icon={<SearchOutlined />} 
              size="large"
              onClick={handleSearch}
            >
              搜索
            </Button>
          </div>
        </Form>
      </Card>

      {/* 保存的搜索 */}
      {savedSearches.length > 0 && (
        <Card title="保存的搜索" style={{ marginTop: '16px' }}>
          <Space wrap>
            {savedSearches.map((saved, index) => (
              <Tag
                key={index}
                color="blue"
                style={{ cursor: 'pointer', padding: '4px 8px' }}
                onClick={() => handleLoadSavedSearch(saved.filters)}
              >
                <BookOutlined /> {saved.name}
              </Tag>
            ))}
          </Space>
        </Card>
      )}

      {/* 搜索历史 */}
      {searchHistory.length > 0 && (
        <Card title="搜索历史" style={{ marginTop: '16px' }}>
          <Space wrap>
            {searchHistory.slice(0, 10).map((query, index) => (
              <Tag
                key={index}
                style={{ cursor: 'pointer' }}
                onClick={() => handleQuickSearch(query)}
              >
                <HistoryOutlined /> {query}
              </Tag>
            ))}
          </Space>
        </Card>
      )}
    </div>
  );
};

export default AdvancedSearch;
