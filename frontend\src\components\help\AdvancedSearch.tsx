import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Button,
  Space,
  Card,
  Typography,
  Tag,
  Divider,
  Row,
  Col,
  Checkbox,
  Slider,
  AutoComplete
} from 'antd';
import {
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
  SaveOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { ContentType, ContentStatus, SearchParams } from '../../types/help';
import { useAppSelector } from '../../hooks/redux';
import { debounce } from '../../utils/helpUtils';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

interface AdvancedSearchProps {
  visible: boolean;
  onClose: () => void;
  onSearch: (params: SearchParams) => void;
  initialParams?: SearchParams;
}

interface SearchHistory {
  id: string;
  name: string;
  params: SearchParams;
  timestamp: string;
}

/**
 * 高级搜索组件
 */
const AdvancedSearch: React.FC<AdvancedSearchProps> = ({
  visible,
  onClose,
  onSearch,
  initialParams = {}
}) => {
  const [form] = Form.useForm();
  const [searchHistory, setSearchHistory] = useState<SearchHistory[]>([]);
  const [savedSearches, setSavedSearches] = useState<SearchHistory[]>([]);
  const [keywordSuggestions, setKeywordSuggestions] = useState<string[]>([]);
  const [tagSuggestions, setTagSuggestions] = useState<string[]>([]);

  const { categories, popularTags } = useAppSelector(state => state.help);

  useEffect(() => {
    // 加载搜索历史
    const history = localStorage.getItem('help_search_history');
    if (history) {
      setSearchHistory(JSON.parse(history));
    }

    // 加载保存的搜索
    const saved = localStorage.getItem('help_saved_searches');
    if (saved) {
      setSavedSearches(JSON.parse(saved));
    }
  }, []);

  useEffect(() => {
    if (visible && initialParams) {
      form.setFieldsValue(initialParams);
    }
  }, [visible, initialParams, form]);

  // 关键词建议
  const handleKeywordSearch = debounce((value: string) => {
    if (value.length >= 2) {
      // 模拟关键词建议API调用
      const suggestions = [
        'BOM创建',
        'BOM管理',
        'BOM审核',
        '物料管理',
        '物料编码',
        '库存盘点',
        '库存管理',
        '采购申请',
        '采购流程',
        '成本分析',
        '成本计算',
        '用户权限',
        '角色管理',
        '系统设置'
      ].filter(item => item.toLowerCase().includes(value.toLowerCase()));
      
      setKeywordSuggestions(suggestions);
    }
  }, 300);

  // 标签建议
  const handleTagSearch = (value: string) => {
    if (value.length >= 1) {
      const suggestions = popularTags
        .filter(tag => tag.name.toLowerCase().includes(value.toLowerCase()))
        .map(tag => tag.name);
      setTagSuggestions(suggestions);
    }
  };

  // 执行搜索
  const handleSearch = () => {
    form.validateFields().then(values => {
      const searchParams: SearchParams = {
        keyword: values.keyword,
        type: values.type,
        categoryId: values.categoryId,
        status: values.status,
        authorId: values.authorId,
        featured: values.featured,
        startDate: values.dateRange?.[0]?.format('YYYY-MM-DD'),
        endDate: values.dateRange?.[1]?.format('YYYY-MM-DD'),
        tags: values.tags,
        sortBy: values.sortBy || 'relevance',
        sortDir: values.sortDir || 'desc'
      };

      // 过滤空值
      const filteredParams = Object.fromEntries(
        Object.entries(searchParams).filter(([_, value]) => 
          value !== undefined && value !== null && value !== ''
        )
      );

      // 保存到搜索历史
      saveToHistory(filteredParams);

      onSearch(filteredParams);
      onClose();
    });
  };

  // 保存到搜索历史
  const saveToHistory = (params: SearchParams) => {
    const historyItem: SearchHistory = {
      id: Date.now().toString(),
      name: params.keyword || '高级搜索',
      params,
      timestamp: new Date().toISOString()
    };

    const newHistory = [historyItem, ...searchHistory.slice(0, 9)]; // 保留最近10条
    setSearchHistory(newHistory);
    localStorage.setItem('help_search_history', JSON.stringify(newHistory));
  };

  // 保存搜索条件
  const saveSearch = () => {
    Modal.confirm({
      title: '保存搜索条件',
      content: (
        <Input
          placeholder="请输入搜索名称"
          onPressEnter={(e) => {
            const name = (e.target as HTMLInputElement).value;
            if (name) {
              const values = form.getFieldsValue();
              const searchItem: SearchHistory = {
                id: Date.now().toString(),
                name,
                params: values,
                timestamp: new Date().toISOString()
              };

              const newSaved = [searchItem, ...savedSearches];
              setSavedSearches(newSaved);
              localStorage.setItem('help_saved_searches', JSON.stringify(newSaved));
              Modal.destroyAll();
            }
          }}
        />
      ),
      okText: '保存',
      cancelText: '取消'
    });
  };

  // 加载历史搜索
  const loadHistorySearch = (item: SearchHistory) => {
    form.setFieldsValue(item.params);
  };

  // 清除表单
  const clearForm = () => {
    form.resetFields();
  };

  return (
    <Modal
      title={
        <Space>
          <SearchOutlined />
          高级搜索
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={
        <Space>
          <Button onClick={clearForm} icon={<ClearOutlined />}>
            清除
          </Button>
          <Button onClick={saveSearch} icon={<SaveOutlined />}>
            保存搜索
          </Button>
          <Button type="primary" onClick={handleSearch} icon={<SearchOutlined />}>
            搜索
          </Button>
        </Space>
      }
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          sortBy: 'relevance',
          sortDir: 'desc'
        }}
      >
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              label="关键词"
              name="keyword"
              help="支持多个关键词，用空格分隔"
            >
              <AutoComplete
                options={keywordSuggestions.map(item => ({ value: item }))}
                onSearch={handleKeywordSearch}
                placeholder="请输入搜索关键词..."
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="内容类型" name="type">
              <Select placeholder="选择内容类型" allowClear>
                <Option value={ContentType.FUNCTION_GUIDE}>功能指南</Option>
                <Option value={ContentType.ROLE_PERMISSION}>角色权限</Option>
                <Option value={ContentType.INDUSTRY_KNOWLEDGE}>行业知识</Option>
                <Option value={ContentType.WORKFLOW}>工作流程</Option>
                <Option value={ContentType.BUSINESS_PROCESS}>业务流程</Option>
                <Option value={ContentType.FAQ}>常见问题</Option>
                <Option value={ContentType.TUTORIAL}>教程</Option>
                <Option value={ContentType.ANNOUNCEMENT}>公告</Option>
                <Option value={ContentType.DIAGRAM}>流程图</Option>
                <Option value={ContentType.VIDEO}>视频教程</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="分类" name="categoryId">
              <Select placeholder="选择分类" allowClear>
                {categories.map(category => (
                  <Option key={category.id} value={category.id}>
                    {category.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="状态" name="status">
              <Select placeholder="选择状态" allowClear>
                <Option value={ContentStatus.DRAFT}>草稿</Option>
                <Option value={ContentStatus.PENDING_REVIEW}>待审核</Option>
                <Option value={ContentStatus.APPROVED}>已审核</Option>
                <Option value={ContentStatus.PUBLISHED}>已发布</Option>
                <Option value={ContentStatus.ARCHIVED}>已归档</Option>
                <Option value={ContentStatus.REJECTED}>已拒绝</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="时间范围" name="dateRange">
              <RangePicker style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="标签" name="tags">
              <Select
                mode="tags"
                placeholder="选择或输入标签"
                onSearch={handleTagSearch}
                style={{ width: '100%' }}
              >
                {tagSuggestions.map(tag => (
                  <Option key={tag} value={tag}>
                    {tag}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="排序方式" name="sortBy">
              <Select>
                <Option value="relevance">相关性</Option>
                <Option value="createdAt">创建时间</Option>
                <Option value="updatedAt">更新时间</Option>
                <Option value="viewCount">浏览量</Option>
                <Option value="likeCount">点赞数</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="排序方向" name="sortDir">
              <Select>
                <Option value="desc">降序</Option>
                <Option value="asc">升序</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Form.Item name="featured" valuePropName="checked">
          <Checkbox>仅显示特色内容</Checkbox>
        </Form.Item>

        <Divider />

        {/* 搜索历史 */}
        {searchHistory.length > 0 && (
          <Card size="small" title={<><HistoryOutlined /> 搜索历史</>}>
            <Space wrap>
              {searchHistory.slice(0, 5).map(item => (
                <Tag
                  key={item.id}
                  style={{ cursor: 'pointer' }}
                  onClick={() => loadHistorySearch(item)}
                >
                  {item.name}
                </Tag>
              ))}
            </Space>
          </Card>
        )}

        {/* 保存的搜索 */}
        {savedSearches.length > 0 && (
          <Card size="small" title={<><SaveOutlined /> 保存的搜索</>} style={{ marginTop: '16px' }}>
            <Space wrap>
              {savedSearches.map(item => (
                <Tag
                  key={item.id}
                  color="blue"
                  style={{ cursor: 'pointer' }}
                  onClick={() => loadHistorySearch(item)}
                >
                  {item.name}
                </Tag>
              ))}
            </Space>
          </Card>
        )}
      </Form>
    </Modal>
  );
};

export default AdvancedSearch;
