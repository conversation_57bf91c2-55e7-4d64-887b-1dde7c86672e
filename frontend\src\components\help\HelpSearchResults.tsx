import React, { useState } from 'react';
import {
  Typography,
  Space,
  Card,
  List,
  Avatar,
  Tag,
  Button,
  Input,
  Select,
  Row,
  Col,
  Pagination,
  Empty,
  Divider,
  Statistic,
  Modal
} from 'antd';
import AdvancedSearch from './AdvancedSearch';
import SearchSuggestions from './SearchSuggestions';
import {
  SearchOutlined,
  FileTextOutlined,
  EyeOutlined,
  LikeOutlined,
  ClockCircleOutlined,
  FilterOutlined,
  ClearOutlined
} from '@ant-design/icons';
import { SearchResult, ContentType, SearchParams } from '../../types/help';
import { useAppDispatch } from '../../hooks/redux';
import { searchContent } from '../../store/slices/helpSlice';

const { Title, Text } = Typography;
const { Search } = Input;
const { Option } = Select;

interface HelpSearchResultsProps {
  results: SearchResult;
  onContentSelect: (contentId: number) => void;
  onCategorySelect: (categoryId: number) => void;
  onSearch: (keyword: string, filters?: any) => void;
}

/**
 * 帮助搜索结果组件
 */
const HelpSearchResults: React.FC<HelpSearchResultsProps> = ({
  results,
  onContentSelect,
  onCategorySelect,
  onSearch
}) => {
  const dispatch = useAppDispatch();
  const [searchKeyword, setSearchKeyword] = useState('');
  const [filters, setFilters] = useState<Partial<SearchParams>>({});
  const [showFilters, setShowFilters] = useState(false);
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);

  // 处理搜索
  const handleSearch = (keyword: string) => {
    setSearchKeyword(keyword);
    onSearch(keyword, filters);
  };

  // 处理筛选
  const handleFilterChange = (key: string, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    
    if (searchKeyword) {
      onSearch(searchKeyword, newFilters);
    }
  };

  // 清除筛选
  const handleClearFilters = () => {
    setFilters({});
    if (searchKeyword) {
      onSearch(searchKeyword, {});
    }
  };

  // 处理分页
  const handlePageChange = (page: number, pageSize: number) => {
    const searchParams: SearchParams = {
      keyword: searchKeyword,
      ...filters,
      page: page - 1,
      size: pageSize
    };
    
    dispatch(searchContent(searchParams));
  };

  // 渲染筛选器
  const renderFilters = () => {
    if (!showFilters) return null;

    return (
      <Card size="small" style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text strong>内容类型</Text>
              <Select
                style={{ width: '100%' }}
                placeholder="选择内容类型"
                allowClear
                value={filters.type}
                onChange={(value) => handleFilterChange('type', value)}
              >
                <Option value={ContentType.FUNCTION_GUIDE}>功能指南</Option>
                <Option value={ContentType.ROLE_PERMISSION}>角色权限</Option>
                <Option value={ContentType.INDUSTRY_KNOWLEDGE}>行业知识</Option>
                <Option value={ContentType.WORKFLOW}>工作流程</Option>
                <Option value={ContentType.BUSINESS_PROCESS}>业务流程</Option>
                <Option value={ContentType.FAQ}>常见问题</Option>
                <Option value={ContentType.TUTORIAL}>教程</Option>
                <Option value={ContentType.ANNOUNCEMENT}>公告</Option>
                <Option value={ContentType.DIAGRAM}>流程图</Option>
                <Option value={ContentType.VIDEO}>视频教程</Option>
              </Select>
            </Space>
          </Col>
          
          <Col xs={24} sm={12} md={8}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text strong>排序方式</Text>
              <Select
                style={{ width: '100%' }}
                placeholder="选择排序方式"
                value={filters.sortBy}
                onChange={(value) => handleFilterChange('sortBy', value)}
              >
                <Option value="relevance">相关性</Option>
                <Option value="createdAt">创建时间</Option>
                <Option value="updatedAt">更新时间</Option>
                <Option value="viewCount">浏览量</Option>
                <Option value="likeCount">点赞数</Option>
              </Select>
            </Space>
          </Col>
          
          <Col xs={24} sm={12} md={8}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Text strong>操作</Text>
              <Button 
                icon={<ClearOutlined />} 
                onClick={handleClearFilters}
                style={{ width: '100%' }}
              >
                清除筛选
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>
    );
  };

  // 渲染搜索统计
  const renderSearchStats = () => {
    return (
      <Row gutter={16} style={{ marginBottom: '16px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="搜索结果"
              value={results.totalElements}
              suffix="条"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总页数"
              value={results.totalPages}
              suffix="页"
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="当前页"
              value={results.currentPage + 1}
              suffix={`/ ${results.totalPages}`}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="每页显示"
              value={results.size}
              suffix="条"
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 高亮搜索关键词
  const highlightKeyword = (text: string, keyword: string) => {
    if (!keyword) return text;
    
    const regex = new RegExp(`(${keyword})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  };

  return (
    <div>
      {/* 搜索头部 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={3}>
          <SearchOutlined style={{ marginRight: '8px' }} />
          搜索结果
        </Title>
        
        {/* 搜索框 */}
        <div style={{ marginBottom: '16px' }}>
          <Search
            placeholder="搜索帮助内容..."
            allowClear
            enterButton="搜索"
            size="large"
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            onSearch={handleSearch}
            style={{ maxWidth: '600px' }}
          />
        </div>
        
        {/* 筛选按钮 */}
        <Space>
          <Button
            icon={<FilterOutlined />}
            onClick={() => setShowFilters(!showFilters)}
            type={showFilters ? 'primary' : 'default'}
          >
            筛选
          </Button>
          
          {Object.keys(filters).length > 0 && (
            <Button
              icon={<ClearOutlined />}
              onClick={handleClearFilters}
            >
              清除筛选
            </Button>
          )}
        </Space>
      </div>

      {/* 筛选器 */}
      {renderFilters()}

      {/* 搜索统计 */}
      {renderSearchStats()}

      {/* 搜索结果 */}
      {results.content.length === 0 ? (
        <Card>
          <Empty
            description="没有找到相关内容"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button type="primary" onClick={() => handleSearch('')}>
              查看所有内容
            </Button>
          </Empty>
        </Card>
      ) : (
        <Card title={`找到 ${results.totalElements} 条相关内容`}>
          <List
            itemLayout="vertical"
            dataSource={results.content}
            renderItem={(item) => (
              <List.Item
                key={item.id}
                style={{ cursor: 'pointer' }}
                onClick={() => onContentSelect(item.id)}
                actions={[
                  <Space key="stats">
                    <EyeOutlined /> {item.viewCount}
                    <LikeOutlined /> {item.likeCount}
                    <ClockCircleOutlined /> {item.updatedAt}
                  </Space>
                ]}
                extra={
                  <Space direction="vertical">
                    {item.featured && <Tag color="gold">特色</Tag>}
                    <Tag color="blue">{item.type}</Tag>
                  </Space>
                }
              >
                <List.Item.Meta
                  avatar={
                    <Avatar
                      icon={<FileTextOutlined />}
                      style={{ backgroundColor: '#1890ff' }}
                    />
                  }
                  title={
                    <div
                      dangerouslySetInnerHTML={{
                        __html: highlightKeyword(item.title, searchKeyword)
                      }}
                    />
                  }
                  description={
                    <div>
                      {item.summary && (
                        <div
                          style={{ marginBottom: '8px' }}
                          dangerouslySetInnerHTML={{
                            __html: highlightKeyword(
                              item.summary.length > 200 
                                ? item.summary.substring(0, 200) + '...'
                                : item.summary,
                              searchKeyword
                            )
                          }}
                        />
                      )}
                      <Space wrap>
                        {item.tags?.slice(0, 5).map(tag => (
                          <Tag key={tag.id} size="small" color={tag.color}>
                            {tag.name}
                          </Tag>
                        ))}
                        {item.tags && item.tags.length > 5 && (
                          <Tag size="small">+{item.tags.length - 5}</Tag>
                        )}
                      </Space>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
          
          {/* 分页 */}
          {results.totalPages > 1 && (
            <div style={{ textAlign: 'center', marginTop: '24px' }}>
              <Pagination
                current={results.currentPage + 1}
                total={results.totalElements}
                pageSize={results.size}
                showSizeChanger
                showQuickJumper
                showTotal={(total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }
                onChange={handlePageChange}
              />
            </div>
          )}
        </Card>
      )}
    </div>
  );
};

export default HelpSearchResults;
