import React, { useEffect } from 'react';
import {
  Typography,
  Space,
  Card,
  List,
  Avatar,
  Tag,
  Button,
  Row,
  Col,
  Statistic,
  Empty,
  Pagination
} from 'antd';
import {
  FileTextOutlined,
  EyeOutlined,
  LikeOutlined,
  ClockCircleOutlined,
  FolderOutlined,
  RightOutlined
} from '@ant-design/icons';
import { HelpCategory, HelpContent } from '../../types/help';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { fetchContentByCategory } from '../../store/slices/helpSlice';

const { Title, Text, Paragraph } = Typography;

interface HelpCategoryViewProps {
  category?: HelpCategory | null;
  onContentSelect: (contentId: number) => void;
  onCategorySelect: (categoryId: number) => void;
}

/**
 * 帮助分类视图组件
 */
const HelpCategoryView: React.FC<HelpCategoryViewProps> = ({
  category,
  onContentSelect,
  onCategorySelect
}) => {
  const dispatch = useAppDispatch();
  const { contentList, pagination, loading } = useAppSelector(state => state.help);

  useEffect(() => {
    if (category?.id) {
      dispatch(fetchContentByCategory({ 
        categoryId: category.id,
        page: 0,
        size: 20
      }));
    }
  }, [category?.id, dispatch]);

  // 处理分页
  const handlePageChange = (page: number, pageSize: number) => {
    if (category?.id) {
      dispatch(fetchContentByCategory({
        categoryId: category.id,
        page: page - 1,
        size: pageSize
      }));
    }
  };

  // 渲染子分类
  const renderSubCategories = () => {
    if (!category?.children || category.children.length === 0) {
      return null;
    }

    return (
      <Card title="子分类" style={{ marginBottom: '24px' }}>
        <Row gutter={[16, 16]}>
          {category.children.map((subCategory) => (
            <Col xs={24} sm={12} md={8} lg={6} key={subCategory.id}>
              <Card
                hoverable
                size="small"
                onClick={() => onCategorySelect(subCategory.id)}
                style={{ height: '100px' }}
                bodyStyle={{ padding: '16px' }}
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Space>
                    <FolderOutlined style={{ color: '#1890ff' }} />
                    <Text strong ellipsis style={{ maxWidth: '120px' }}>
                      {subCategory.name}
                    </Text>
                  </Space>
                  {subCategory.description && (
                    <Text 
                      type="secondary" 
                      style={{ fontSize: '12px' }}
                      ellipsis
                    >
                      {subCategory.description}
                    </Text>
                  )}
                  {subCategory.contentCount !== undefined && (
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {subCategory.contentCount} 篇内容
                    </Text>
                  )}
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>
    );
  };

  // 渲染统计信息
  const renderStatistics = () => {
    return (
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总内容数"
              value={pagination.totalElements}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="子分类数"
              value={category?.children?.length || 0}
              prefix={<FolderOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总浏览量"
              value={contentList.reduce((sum, item) => sum + item.viewCount, 0)}
              prefix={<EyeOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总点赞数"
              value={contentList.reduce((sum, item) => sum + item.likeCount, 0)}
              prefix={<LikeOutlined />}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染内容列表
  const renderContentList = () => {
    if (contentList.length === 0) {
      return (
        <Card>
          <Empty
            description="暂无内容"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </Card>
      );
    }

    return (
      <Card title="内容列表">
        <List
          itemLayout="vertical"
          loading={loading.content}
          dataSource={contentList}
          renderItem={(item) => (
            <List.Item
              key={item.id}
              style={{ cursor: 'pointer' }}
              onClick={() => onContentSelect(item.id)}
              actions={[
                <Space key="stats">
                  <EyeOutlined /> {item.viewCount}
                  <LikeOutlined /> {item.likeCount}
                  <ClockCircleOutlined /> {item.updatedAt}
                </Space>
              ]}
              extra={
                item.featured && (
                  <Tag color="gold">特色</Tag>
                )
              }
            >
              <List.Item.Meta
                avatar={
                  <Avatar
                    icon={<FileTextOutlined />}
                    style={{ backgroundColor: '#1890ff' }}
                  />
                }
                title={
                  <Space>
                    <span>{item.title}</span>
                    <Tag color="blue">{item.type}</Tag>
                  </Space>
                }
                description={
                  <div>
                    {item.summary && (
                      <Paragraph 
                        ellipsis={{ rows: 2 }}
                        style={{ marginBottom: '8px' }}
                      >
                        {item.summary}
                      </Paragraph>
                    )}
                    <Space wrap>
                      {item.tags?.slice(0, 3).map(tag => (
                        <Tag key={tag.id} size="small" color={tag.color}>
                          {tag.name}
                        </Tag>
                      ))}
                      {item.tags && item.tags.length > 3 && (
                        <Tag size="small">+{item.tags.length - 3}</Tag>
                      )}
                    </Space>
                  </div>
                }
              />
            </List.Item>
          )}
        />
        
        {/* 分页 */}
        {pagination.totalPages > 1 && (
          <div style={{ textAlign: 'center', marginTop: '24px' }}>
            <Pagination
              current={pagination.currentPage + 1}
              total={pagination.totalElements}
              pageSize={pagination.size}
              showSizeChanger
              showQuickJumper
              showTotal={(total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }
              onChange={handlePageChange}
            />
          </div>
        )}
      </Card>
    );
  };

  if (!category) {
    return (
      <Card>
        <Empty description="分类不存在" />
      </Card>
    );
  }

  return (
    <div>
      {/* 分类头部 */}
      <div style={{ marginBottom: '24px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Title level={2}>
            <FolderOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
            {category.name}
          </Title>
          
          {category.description && (
            <Paragraph style={{ fontSize: '16px', color: '#666' }}>
              {category.description}
            </Paragraph>
          )}
        </Space>
      </div>

      {/* 统计信息 */}
      {renderStatistics()}

      {/* 子分类 */}
      {renderSubCategories()}

      {/* 内容列表 */}
      {renderContentList()}
    </div>
  );
};

export default HelpCategoryView;
