package com.linkboms.service.help;

import com.linkboms.entity.help.HelpFeedback;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 帮助反馈服务接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface HelpFeedbackService {

    // ==================== 基础CRUD操作 ====================
    
    /**
     * 创建反馈
     * 
     * @param feedback 反馈信息
     * @return 创建的反馈
     */
    HelpFeedback createFeedback(HelpFeedback feedback);
    
    /**
     * 更新反馈
     * 
     * @param id 反馈ID
     * @param feedback 反馈信息
     * @return 更新后的反馈
     */
    HelpFeedback updateFeedback(Long id, HelpFeedback feedback);
    
    /**
     * 删除反馈（软删除）
     * 
     * @param id 反馈ID
     */
    void deleteFeedback(Long id);
    
    /**
     * 永久删除反馈
     * 
     * @param id 反馈ID
     */
    void permanentDeleteFeedback(Long id);
    
    /**
     * 恢复已删除的反馈
     * 
     * @param id 反馈ID
     */
    void restoreFeedback(Long id);
    
    // ==================== 查询操作 ====================
    
    /**
     * 根据ID获取反馈
     * 
     * @param id 反馈ID
     * @return 反馈信息
     */
    HelpFeedback getFeedbackById(Long id);
    
    /**
     * 分页获取反馈列表
     * 
     * @param pageable 分页参数
     * @return 反馈分页列表
     */
    Page<HelpFeedback> getFeedbackList(Pageable pageable);
    
    /**
     * 根据内容ID获取反馈
     * 
     * @param contentId 内容ID
     * @param pageable 分页参数
     * @return 反馈分页列表
     */
    Page<HelpFeedback> getFeedbackByContentId(Long contentId, Pageable pageable);
    
    /**
     * 根据用户ID获取反馈
     * 
     * @param userId 用户ID
     * @param pageable 分页参数
     * @return 反馈分页列表
     */
    Page<HelpFeedback> getFeedbackByUserId(Long userId, Pageable pageable);
    
    /**
     * 根据状态获取反馈
     * 
     * @param status 反馈状态
     * @param pageable 分页参数
     * @return 反馈分页列表
     */
    Page<HelpFeedback> getFeedbackByStatus(HelpFeedback.FeedbackStatus status, Pageable pageable);
    
    /**
     * 根据类型获取反馈
     * 
     * @param type 反馈类型
     * @param pageable 分页参数
     * @return 反馈分页列表
     */
    Page<HelpFeedback> getFeedbackByType(HelpFeedback.FeedbackType type, Pageable pageable);
    
    /**
     * 获取正面反馈
     * 
     * @param contentId 内容ID
     * @param pageable 分页参数
     * @return 正面反馈列表
     */
    Page<HelpFeedback> getPositiveFeedback(Long contentId, Pageable pageable);
    
    /**
     * 获取负面反馈
     * 
     * @param contentId 内容ID
     * @param pageable 分页参数
     * @return 负面反馈列表
     */
    Page<HelpFeedback> getNegativeFeedback(Long contentId, Pageable pageable);
    
    /**
     * 获取有用的反馈
     * 
     * @param contentId 内容ID
     * @param pageable 分页参数
     * @return 有用反馈列表
     */
    Page<HelpFeedback> getHelpfulFeedback(Long contentId, Pageable pageable);
    
    /**
     * 获取包含建议的反馈
     * 
     * @param contentId 内容ID
     * @param pageable 分页参数
     * @return 包含建议的反馈列表
     */
    Page<HelpFeedback> getFeedbackWithSuggestions(Long contentId, Pageable pageable);
    
    /**
     * 获取已回复的反馈
     * 
     * @param pageable 分页参数
     * @return 已回复反馈列表
     */
    Page<HelpFeedback> getRepliedFeedback(Pageable pageable);
    
    /**
     * 获取未回复的反馈
     * 
     * @param pageable 分页参数
     * @return 未回复反馈列表
     */
    Page<HelpFeedback> getUnrepliedFeedback(Pageable pageable);
    
    /**
     * 根据时间范围获取反馈
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 反馈列表
     */
    Page<HelpFeedback> getFeedbackByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 根据IP地址获取反馈
     * 
     * @param ipAddress IP地址
     * @param pageable 分页参数
     * @return 反馈列表
     */
    Page<HelpFeedback> getFeedbackByIpAddress(String ipAddress, Pageable pageable);
    
    /**
     * 获取匿名反馈
     * 
     * @param pageable 分页参数
     * @return 匿名反馈列表
     */
    Page<HelpFeedback> getAnonymousFeedback(Pageable pageable);
    
    /**
     * 搜索反馈
     * 
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 反馈列表
     */
    Page<HelpFeedback> searchFeedback(String keyword, Pageable pageable);
    
    /**
     * 获取最近反馈
     * 
     * @param contentId 内容ID
     * @param limit 数量限制
     * @return 最近反馈列表
     */
    List<HelpFeedback> getRecentFeedback(Long contentId, int limit);
    
    // ==================== 状态管理 ====================
    
    /**
     * 更新反馈状态
     * 
     * @param id 反馈ID
     * @param status 新状态
     */
    void updateFeedbackStatus(Long id, HelpFeedback.FeedbackStatus status);
    
    /**
     * 批量更新反馈状态
     * 
     * @param ids 反馈ID列表
     * @param status 新状态
     */
    void batchUpdateFeedbackStatus(List<Long> ids, HelpFeedback.FeedbackStatus status);
    
    /**
     * 审核反馈
     * 
     * @param id 反馈ID
     * @param approved 是否通过
     * @param reason 审核原因
     */
    void reviewFeedback(Long id, boolean approved, String reason);
    
    /**
     * 标记为已处理
     * 
     * @param id 反馈ID
     */
    void markAsProcessed(Long id);
    
    /**
     * 标记为已解决
     * 
     * @param id 反馈ID
     */
    void markAsResolved(Long id);
    
    /**
     * 标记为已关闭
     * 
     * @param id 反馈ID
     */
    void markAsClosed(Long id);
    
    // ==================== 管理员回复 ====================
    
    /**
     * 添加管理员回复
     * 
     * @param id 反馈ID
     * @param reply 回复内容
     * @param adminId 管理员ID
     */
    void addAdminReply(Long id, String reply, Long adminId);
    
    /**
     * 更新管理员回复
     * 
     * @param id 反馈ID
     * @param reply 回复内容
     */
    void updateAdminReply(Long id, String reply);
    
    /**
     * 删除管理员回复
     * 
     * @param id 反馈ID
     */
    void deleteAdminReply(Long id);
    
    // ==================== 批量操作 ====================
    
    /**
     * 批量删除反馈
     * 
     * @param ids 反馈ID列表
     */
    void batchDeleteFeedback(List<Long> ids);
    
    /**
     * 批量恢复反馈
     * 
     * @param ids 反馈ID列表
     */
    void batchRestoreFeedback(List<Long> ids);
    
    /**
     * 批量审核反馈
     * 
     * @param ids 反馈ID列表
     * @param approved 是否通过
     * @param reason 审核原因
     */
    void batchReviewFeedback(List<Long> ids, boolean approved, String reason);
    
    // ==================== 统计信息 ====================
    
    /**
     * 获取内容的平均评分
     * 
     * @param contentId 内容ID
     * @return 平均评分
     */
    Double getAverageRating(Long contentId);
    
    /**
     * 获取内容的评分分布
     * 
     * @param contentId 内容ID
     * @return 评分分布
     */
    Map<Integer, Long> getRatingDistribution(Long contentId);
    
    /**
     * 统计反馈数量
     * 
     * @param contentId 内容ID
     * @return 反馈数量
     */
    Long countFeedback(Long contentId);
    
    /**
     * 按状态统计反馈数量
     * 
     * @param contentId 内容ID
     * @return 状态统计
     */
    Map<HelpFeedback.FeedbackStatus, Long> countFeedbackByStatus(Long contentId);
    
    /**
     * 按类型统计反馈数量
     * 
     * @param contentId 内容ID
     * @return 类型统计
     */
    Map<HelpFeedback.FeedbackType, Long> countFeedbackByType(Long contentId);
    
    /**
     * 按评分统计反馈数量
     * 
     * @param contentId 内容ID
     * @return 评分统计
     */
    Map<Integer, Long> countFeedbackByRating(Long contentId);
    
    /**
     * 统计指定时间范围内的反馈数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 反馈数量
     */
    Long countFeedbackByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计指定时间范围内的平均评分
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 平均评分
     */
    Double getAverageRatingByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取反馈统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getFeedbackStatistics();
    
    /**
     * 获取内容反馈统计
     * 
     * @param contentId 内容ID
     * @return 内容反馈统计
     */
    Map<String, Object> getContentFeedbackStatistics(Long contentId);
    
    /**
     * 获取用户反馈统计
     * 
     * @param userId 用户ID
     * @return 用户反馈统计
     */
    Map<String, Object> getUserFeedbackStatistics(Long userId);
    
    // ==================== 验证与检查 ====================
    
    /**
     * 验证反馈数据
     * 
     * @param feedback 反馈信息
     * @return 验证错误列表
     */
    List<String> validateFeedback(HelpFeedback feedback);
    
    /**
     * 检查用户是否已对内容提交反馈
     * 
     * @param contentId 内容ID
     * @param userId 用户ID
     * @return 是否已提交
     */
    boolean hasUserSubmittedFeedback(Long contentId, Long userId);
    
    /**
     * 检查IP是否已对内容提交反馈
     * 
     * @param contentId 内容ID
     * @param ipAddress IP地址
     * @return 是否已提交
     */
    boolean hasIpSubmittedFeedback(Long contentId, String ipAddress);
    
    // ==================== 导入导出 ====================
    
    /**
     * 导出反馈
     * 
     * @param format 导出格式（json, csv, excel）
     * @param contentId 内容ID（可选）
     * @return 导出数据
     */
    byte[] exportFeedback(String format, Long contentId);
    
    /**
     * 导入反馈
     * 
     * @param file 导入文件
     * @return 导入的反馈列表
     */
    List<HelpFeedback> importFeedback(MultipartFile file);
    
    // ==================== 清理与维护 ====================
    
    /**
     * 清理过期反馈
     * 
     * @param days 保留天数
     */
    void cleanupExpiredFeedback(int days);
    
    /**
     * 清理垃圾反馈
     */
    void cleanupSpamFeedback();
    
    /**
     * 清理重复反馈
     */
    void cleanupDuplicateFeedback();
    
    // ==================== 通知与提醒 ====================
    
    /**
     * 发送反馈通知
     * 
     * @param feedbackId 反馈ID
     * @param notificationType 通知类型
     */
    void sendFeedbackNotification(Long feedbackId, String notificationType);
    
    /**
     * 发送反馈摘要报告
     * 
     * @param contentId 内容ID
     * @param recipientEmail 接收邮箱
     */
    void sendFeedbackSummaryReport(Long contentId, String recipientEmail);
    
    // ==================== 分析与洞察 ====================
    
    /**
     * 分析反馈趋势
     * 
     * @param contentId 内容ID
     * @param days 分析天数
     * @return 趋势分析结果
     */
    Map<String, Object> analyzeFeedbackTrends(Long contentId, int days);
    
    /**
     * 分析反馈情感
     * 
     * @param contentId 内容ID
     * @return 情感分析结果
     */
    Map<String, Object> analyzeFeedbackSentiment(Long contentId);
    
    /**
     * 提取反馈关键词
     * 
     * @param contentId 内容ID
     * @param limit 关键词数量
     * @return 关键词列表
     */
    List<Map<String, Object>> extractFeedbackKeywords(Long contentId, int limit);
    
    /**
     * 生成反馈摘要
     * 
     * @param contentId 内容ID
     * @return 反馈摘要
     */
    String generateFeedbackSummary(Long contentId);
}