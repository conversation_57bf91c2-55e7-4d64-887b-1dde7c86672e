package com.linkboms.service.help;

import com.linkboms.entity.help.HelpFeedback;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 帮助反馈服务接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface HelpFeedbackService {

    /**
     * 提交反馈
     */
    HelpFeedback submitFeedback(HelpFeedback feedback);

    /**
     * 更新反馈
     */
    HelpFeedback updateFeedback(Long id, HelpFeedback feedback);

    /**
     * 根据ID获取反馈
     */
    HelpFeedback getFeedbackById(Long id);

    /**
     * 根据ID获取反馈（可选）
     */
    Optional<HelpFeedback> findFeedbackById(Long id);

    /**
     * 删除反馈
     */
    void deleteFeedback(Long id);

    /**
     * 软删除反馈
     */
    void softDeleteFeedback(Long id);

    /**
     * 恢复反馈
     */
    void restoreFeedback(Long id);

    /**
     * 获取所有反馈（分页）
     */
    Page<HelpFeedback> getAllFeedback(Pageable pageable);

    /**
     * 根据内容ID获取反馈
     */
    Page<HelpFeedback> getFeedbackByContent(Long contentId, Pageable pageable);

    /**
     * 根据类型获取反馈
     */
    Page<HelpFeedback> getFeedbackByType(HelpFeedback.FeedbackType type, Pageable pageable);

    /**
     * 根据状态获取反馈
     */
    Page<HelpFeedback> getFeedbackByStatus(HelpFeedback.FeedbackStatus status, Pageable pageable);

    /**
     * 根据提交者获取反馈
     */
    Page<HelpFeedback> getFeedbackBySubmitter(Long submitterId, Pageable pageable);

    /**
     * 根据回复者获取反馈
     */
    Page<HelpFeedback> getFeedbackByReplier(Long replierId, Pageable pageable);

    /**
     * 获取匿名反馈
     */
    Page<HelpFeedback> getAnonymousFeedback(Pageable pageable);

    /**
     * 获取已回复的反馈
     */
    Page<HelpFeedback> getRepliedFeedback(Pageable pageable);

    /**
     * 获取未回复的反馈
     */
    Page<HelpFeedback> getUnrepliedFeedback(Pageable pageable);

    /**
     * 获取待处理的反馈
     */
    Page<HelpFeedback> getPendingFeedback(Pageable pageable);

    /**
     * 搜索反馈
     */
    Page<HelpFeedback> searchFeedback(String keyword, HelpFeedback.FeedbackType type, 
                                     HelpFeedback.FeedbackStatus status, Pageable pageable);

    /**
     * 高级搜索反馈
     */
    Page<HelpFeedback> advancedSearchFeedback(String keyword, HelpFeedback.FeedbackType type,
                                             HelpFeedback.FeedbackStatus status, Long contentId,
                                             Long submitterId, Long replierId, LocalDateTime startDate,
                                             LocalDateTime endDate, Integer minRating, Integer maxRating,
                                             Pageable pageable);

    /**
     * 根据评分范围获取反馈
     */
    Page<HelpFeedback> getFeedbackByRatingRange(Integer minRating, Integer maxRating, Pageable pageable);

    /**
     * 获取高评分反馈
     */
    List<HelpFeedback> getHighRatingFeedback(Integer minRating, int limit);

    /**
     * 获取低评分反馈
     */
    List<HelpFeedback> getLowRatingFeedback(Integer maxRating, int limit);

    /**
     * 根据时间范围获取反馈
     */
    List<HelpFeedback> getFeedbackByDateRange(LocalDateTime startDate, LocalDateTime endDate, int limit);

    /**
     * 回复反馈
     */
    void replyFeedback(Long id, String replyContent, String replierName, Long replierId);

    /**
     * 处理反馈
     */
    void processFeedback(Long id);

    /**
     * 解决反馈
     */
    void resolveFeedback(Long id);

    /**
     * 拒绝反馈
     */
    void rejectFeedback(Long id);

    /**
     * 关闭反馈
     */
    void closeFeedback(Long id);

    /**
     * 批量更新状态
     */
    void batchUpdateStatus(List<Long> ids, HelpFeedback.FeedbackStatus status);

    /**
     * 批量删除反馈
     */
    void batchDeleteFeedback(List<Long> ids);

    /**
     * 根据内容ID批量删除反馈
     */
    void deleteFeedbackByContent(Long contentId);

    /**
     * 批量回复反馈
     */
    void batchReplyFeedback(List<Long> ids, String replyTemplate, String replierName, Long replierId);

    /**
     * 转发反馈
     */
    void forwardFeedback(Long id, Long targetUserId, String message);

    /**
     * 标记为重要反馈
     */
    void markAsImportant(Long id);

    /**
     * 取消重要标记
     */
    void unmarkAsImportant(Long id);

    /**
     * 验证反馈数据
     */
    void validateFeedback(HelpFeedback feedback);

    /**
     * 获取反馈统计信息
     */
    Object getFeedbackStatistics();

    /**
     * 获取反馈状态统计
     */
    Object getFeedbackStatusStatistics();

    /**
     * 获取反馈类型统计
     */
    Object getFeedbackTypeStatistics();

    /**
     * 获取反馈评分统计
     */
    Object getFeedbackRatingStatistics();

    /**
     * 计算平均评分
     */
    Double calculateAverageRating();

    /**
     * 计算指定内容的平均评分
     */
    Double calculateAverageRatingByContent(Long contentId);

    /**
     * 获取需要关注的反馈
     */
    List<HelpFeedback> getCriticalFeedback(int limit);

    /**
     * 获取活跃用户的反馈
     */
    List<HelpFeedback> getFeedbackFromActiveUsers(Long minFeedbackCount, int limit);

    /**
     * 获取超时未回复的反馈
     */
    List<HelpFeedback> getOverdueFeedback(LocalDateTime timeoutDate);

    /**
     * 获取反馈趋势
     */
    List<Object[]> getFeedbackTrend(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 自动分类反馈
     */
    HelpFeedback.FeedbackType autoClassifyFeedback(String content);

    /**
     * 情感分析
     */
    String analyzeFeedbackSentiment(String content);

    /**
     * 关键词提取
     */
    List<String> extractFeedbackKeywords(String content, int maxCount);

    /**
     * 相似反馈检测
     */
    List<HelpFeedback> findSimilarFeedback(String content, int limit);

    /**
     * 生成反馈报告
     */
    byte[] generateFeedbackReport(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 导出反馈数据
     */
    byte[] exportFeedback(List<Long> feedbackIds);

    /**
     * 发送反馈通知
     */
    void sendFeedbackNotification(Long feedbackId, String notificationType);

    /**
     * 设置反馈提醒
     */
    void setFeedbackReminder(Long feedbackId, LocalDateTime reminderTime);

    /**
     * 反馈质量评估
     */
    Double assessFeedbackQuality(Long feedbackId);

    /**
     * 推荐相关内容
     */
    List<Object> recommendRelatedContent(Long feedbackId, int limit);
}
