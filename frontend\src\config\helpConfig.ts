import { ContentType, FeedbackType, TagType, AttachmentType } from '../types/help';

/**
 * 帮助手册配置文件
 */
export const HELP_CONFIG = {
  // 基本配置
  ITEMS_PER_PAGE: 20,
  MAX_SEARCH_RESULTS: 100,
  ENABLE_COMMENTS: true,
  ENABLE_RATING: true,
  ENABLE_ANONYMOUS_FEEDBACK: true,
  AUTO_PUBLISH: false,
  REQUIRE_REVIEW: true,
  
  // 文件上传配置
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_FILE_TYPES: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'text/plain',
    'video/mp4',
    'video/webm'
  ],
  
  // 搜索配置
  SEARCH_DEBOUNCE_DELAY: 300,
  MIN_SEARCH_LENGTH: 2,
  MAX_SEARCH_SUGGESTIONS: 10,
  
  // 缓存配置
  CACHE_DURATION: 5 * 60 * 1000, // 5分钟
  
  // 主题配置
  THEME: {
    PRIMARY_COLOR: '#1890ff',
    SECONDARY_COLOR: '#52c41a',
    BACKGROUND_COLOR: '#f5f5f5',
    TEXT_COLOR: '#262626',
    LINK_COLOR: '#1890ff',
    BORDER_COLOR: '#d9d9d9',
    FONT_FAMILY: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    FONT_SIZE: '14px',
    BORDER_RADIUS: '6px'
  }
};

/**
 * 内容类型配置
 */
export const CONTENT_TYPE_CONFIG = {
  [ContentType.FUNCTION_GUIDE]: {
    label: '功能指南',
    description: '系统功能的详细操作指南',
    icon: 'BookOutlined',
    color: '#1890ff'
  },
  [ContentType.ROLE_PERMISSION]: {
    label: '角色权限',
    description: '用户角色和权限说明',
    icon: 'UserOutlined',
    color: '#52c41a'
  },
  [ContentType.INDUSTRY_KNOWLEDGE]: {
    label: '行业知识',
    description: '相关行业的专业知识',
    icon: 'DatabaseOutlined',
    color: '#722ed1'
  },
  [ContentType.WORKFLOW]: {
    label: '工作流程',
    description: '业务工作流程说明',
    icon: 'ToolOutlined',
    color: '#fa8c16'
  },
  [ContentType.BUSINESS_PROCESS]: {
    label: '业务流程',
    description: '完整的业务操作流程',
    icon: 'ApartmentOutlined',
    color: '#13c2c2'
  },
  [ContentType.FAQ]: {
    label: '常见问题',
    description: '用户常遇问题解答',
    icon: 'QuestionCircleOutlined',
    color: '#eb2f96'
  },
  [ContentType.TUTORIAL]: {
    label: '教程',
    description: '详细的操作教程',
    icon: 'FileTextOutlined',
    color: '#f5222d'
  },
  [ContentType.ANNOUNCEMENT]: {
    label: '公告',
    description: '系统公告和通知',
    icon: 'NotificationOutlined',
    color: '#fa541c'
  },
  [ContentType.DIAGRAM]: {
    label: '流程图',
    description: '可视化流程图表',
    icon: 'ApartmentOutlined',
    color: '#2f54eb'
  },
  [ContentType.VIDEO]: {
    label: '视频教程',
    description: '视频形式的教程',
    icon: 'PlayCircleOutlined',
    color: '#722ed1'
  }
};

/**
 * 反馈类型配置
 */
export const FEEDBACK_TYPE_CONFIG = {
  [FeedbackType.LIKE]: {
    label: '点赞',
    description: '对内容表示赞同',
    icon: 'LikeOutlined',
    color: '#52c41a'
  },
  [FeedbackType.DISLIKE]: {
    label: '点踩',
    description: '对内容表示不满',
    icon: 'DislikeOutlined',
    color: '#f5222d'
  },
  [FeedbackType.COMMENT]: {
    label: '评论',
    description: '对内容进行评论',
    icon: 'MessageOutlined',
    color: '#1890ff'
  },
  [FeedbackType.SUGGESTION]: {
    label: '建议',
    description: '提出改进建议',
    icon: 'BulbOutlined',
    color: '#fa8c16'
  },
  [FeedbackType.BUG_REPORT]: {
    label: '错误报告',
    description: '报告系统错误',
    icon: 'BugOutlined',
    color: '#f5222d'
  },
  [FeedbackType.CONTENT_ERROR]: {
    label: '内容错误',
    description: '报告内容错误',
    icon: 'ExclamationCircleOutlined',
    color: '#fa541c'
  },
  [FeedbackType.IMPROVEMENT]: {
    label: '改进建议',
    description: '提出改进意见',
    icon: 'RiseOutlined',
    color: '#13c2c2'
  }
};

/**
 * 标签类型配置
 */
export const TAG_TYPE_CONFIG = {
  [TagType.CONTENT]: {
    label: '内容标签',
    description: '用于标记内容特征',
    color: 'blue'
  },
  [TagType.CATEGORY]: {
    label: '分类标签',
    description: '用于分类管理',
    color: 'green'
  },
  [TagType.SYSTEM]: {
    label: '系统标签',
    description: '系统预定义标签',
    color: 'orange'
  },
  [TagType.CUSTOM]: {
    label: '自定义标签',
    description: '用户自定义标签',
    color: 'purple'
  }
};

/**
 * 附件类型配置
 */
export const ATTACHMENT_TYPE_CONFIG = {
  [AttachmentType.IMAGE]: {
    label: '图片',
    description: '图片文件',
    icon: 'FileImageOutlined',
    extensions: ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'],
    maxSize: 5 * 1024 * 1024 // 5MB
  },
  [AttachmentType.DOCUMENT]: {
    label: '文档',
    description: '文档文件',
    icon: 'FileTextOutlined',
    extensions: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'],
    maxSize: 10 * 1024 * 1024 // 10MB
  },
  [AttachmentType.VIDEO]: {
    label: '视频',
    description: '视频文件',
    icon: 'VideoCameraOutlined',
    extensions: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'],
    maxSize: 100 * 1024 * 1024 // 100MB
  },
  [AttachmentType.AUDIO]: {
    label: '音频',
    description: '音频文件',
    icon: 'AudioOutlined',
    extensions: ['mp3', 'wav', 'flac', 'aac', 'ogg'],
    maxSize: 20 * 1024 * 1024 // 20MB
  },
  [AttachmentType.ARCHIVE]: {
    label: '压缩包',
    description: '压缩文件',
    icon: 'FileZipOutlined',
    extensions: ['zip', 'rar', '7z', 'tar', 'gz'],
    maxSize: 50 * 1024 * 1024 // 50MB
  },
  [AttachmentType.OTHER]: {
    label: '其他',
    description: '其他类型文件',
    icon: 'FileOutlined',
    extensions: [],
    maxSize: 10 * 1024 * 1024 // 10MB
  }
};

/**
 * 快速链接配置
 */
export const QUICK_LINKS = [
  {
    id: 'getting-started',
    title: '快速入门',
    description: '新用户快速上手指南',
    url: '/help/category/1',
    icon: 'RocketOutlined',
    category: '入门指南'
  },
  {
    id: 'bom-management',
    title: 'BOM管理',
    description: 'BOM创建和管理功能',
    url: '/help/category/7',
    icon: 'BookOutlined',
    category: '核心功能'
  },
  {
    id: 'material-management',
    title: '物料管理',
    description: '物料信息维护和管理',
    url: '/help/category/8',
    icon: 'DatabaseOutlined',
    category: '核心功能'
  },
  {
    id: 'inventory-management',
    title: '库存管理',
    description: '库存查询和管理功能',
    url: '/help/category/9',
    icon: 'InboxOutlined',
    category: '核心功能'
  },
  {
    id: 'procurement',
    title: '采购管理',
    description: '采购流程和订单管理',
    url: '/help/category/10',
    icon: 'ShoppingCartOutlined',
    category: '业务流程'
  },
  {
    id: 'cost-analysis',
    title: '成本分析',
    description: '成本计算和分析功能',
    url: '/help/category/11',
    icon: 'BarChartOutlined',
    category: '分析报表'
  },
  {
    id: 'user-roles',
    title: '用户角色',
    description: '角色权限说明',
    url: '/help/category/2',
    icon: 'UserOutlined',
    category: '系统管理'
  },
  {
    id: 'faq',
    title: '常见问题',
    description: '用户常遇问题解答',
    url: '/help/category/5',
    icon: 'QuestionCircleOutlined',
    category: '帮助支持'
  }
];

/**
 * 搜索建议配置
 */
export const SEARCH_SUGGESTIONS = [
  '如何创建BOM',
  '物料编码规则',
  '库存盘点流程',
  '采购申请审批',
  '成本分析报表',
  '用户权限设置',
  '密码重置',
  '系统登录问题',
  '数据导入导出',
  '报表生成'
];

/**
 * 帮助手册版本信息
 */
export const HELP_VERSION = {
  VERSION: '1.0.0',
  BUILD_DATE: '2024-08-20',
  LAST_UPDATED: '2024-08-20',
  CHANGELOG_URL: '/help/changelog'
};

/**
 * 联系信息配置
 */
export const CONTACT_INFO = {
  SUPPORT_EMAIL: '<EMAIL>',
  SUPPORT_PHONE: '************',
  SUPPORT_HOURS: '工作日 9:00-18:00',
  FEEDBACK_URL: '/help/feedback',
  DOCUMENTATION_URL: '/help/docs'
};

/**
 * 获取内容类型标签
 */
export const getContentTypeLabel = (type: ContentType): string => {
  return CONTENT_TYPE_CONFIG[type]?.label || type;
};

/**
 * 获取内容类型颜色
 */
export const getContentTypeColor = (type: ContentType): string => {
  return CONTENT_TYPE_CONFIG[type]?.color || '#1890ff';
};

/**
 * 获取反馈类型标签
 */
export const getFeedbackTypeLabel = (type: FeedbackType): string => {
  return FEEDBACK_TYPE_CONFIG[type]?.label || type;
};

/**
 * 获取附件类型配置
 */
export const getAttachmentTypeConfig = (type: AttachmentType) => {
  return ATTACHMENT_TYPE_CONFIG[type];
};

/**
 * 验证文件类型
 */
export const validateFileType = (file: File): boolean => {
  return HELP_CONFIG.ALLOWED_FILE_TYPES.includes(file.type);
};

/**
 * 验证文件大小
 */
export const validateFileSize = (file: File): boolean => {
  return file.size <= HELP_CONFIG.MAX_FILE_SIZE;
};

/**
 * 格式化文件大小
 */
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export default HELP_CONFIG;
