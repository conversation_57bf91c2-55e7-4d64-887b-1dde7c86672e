package com.linkboms.repository.help;

import com.linkboms.entity.help.HelpCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 帮助分类Repository接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface HelpCategoryRepository extends JpaRepository<HelpCategory, Long> {

    /**
     * 查找所有启用的根分类
     */
    List<HelpCategory> findByParentIdIsNullAndEnabledTrueAndDeletedFalseOrderBySortOrderAsc();

    /**
     * 查找指定父分类下的子分类
     */
    List<HelpCategory> findByParentIdAndEnabledTrueAndDeletedFalseOrderBySortOrderAsc(Long parentId);

    /**
     * 查找所有启用的分类（树形结构）
     */
    List<HelpCategory> findByEnabledTrueAndDeletedFalseOrderByLevelAscSortOrderAsc();

    /**
     * 查找所有可见的分类
     */
    List<HelpCategory> findByVisibleTrueAndEnabledTrueAndDeletedFalseOrderBySortOrderAsc();

    /**
     * 根据名称查找分类
     */
    Optional<HelpCategory> findByNameAndDeletedFalse(String name);

    /**
     * 根据路径查找分类
     */
    Optional<HelpCategory> findByPathAndDeletedFalse(String path);

    /**
     * 检查名称是否已存在
     */
    boolean existsByNameAndDeletedFalse(String name);

    /**
     * 检查名称是否已存在（排除指定ID）
     */
    boolean existsByNameAndIdNotAndDeletedFalse(String name, Long id);

    /**
     * 检查路径是否已存在
     */
    boolean existsByPathAndDeletedFalse(String path);

    /**
     * 检查路径是否已存在（排除指定ID）
     */
    boolean existsByPathAndIdNotAndDeletedFalse(String path, Long id);

    /**
     * 查找指定分类的所有子分类（包括子子分类）
     */
    @Query("SELECT c FROM HelpCategory c WHERE c.path LIKE CONCAT(:parentPath, '%') AND c.deleted = false")
    List<HelpCategory> findAllDescendants(@Param("parentPath") String parentPath);

    /**
     * 查找指定分类的直接子分类
     */
    List<HelpCategory> findByParentIdAndDeletedFalseOrderBySortOrderAsc(Long parentId);

    /**
     * 统计指定分类下的内容数量
     */
    @Query("SELECT COUNT(h) FROM HelpContent h WHERE h.categoryId = :categoryId AND h.deleted = false")
    Long countContentsByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 更新分类的内容数量
     */
    @Modifying
    @Query("UPDATE HelpCategory c SET c.contentCount = :count WHERE c.id = :id")
    void updateContentCount(@Param("id") Long id, @Param("count") Integer count);

    /**
     * 增加分类的内容数量
     */
    @Modifying
    @Query("UPDATE HelpCategory c SET c.contentCount = c.contentCount + 1 WHERE c.id = :id")
    void incrementContentCount(@Param("id") Long id);

    /**
     * 减少分类的内容数量
     */
    @Modifying
    @Query("UPDATE HelpCategory c SET c.contentCount = c.contentCount - 1 WHERE c.id = :id AND c.contentCount > 0")
    void decrementContentCount(@Param("id") Long id);

    /**
     * 软删除分类
     */
    @Modifying
    @Query("UPDATE HelpCategory c SET c.deleted = true WHERE c.id = :id")
    void softDelete(@Param("id") Long id);

    /**
     * 批量软删除
     */
    @Modifying
    @Query("UPDATE HelpCategory c SET c.deleted = true WHERE c.id IN :ids")
    void batchSoftDelete(@Param("ids") List<Long> ids);

    /**
     * 恢复已删除的分类
     */
    @Modifying
    @Query("UPDATE HelpCategory c SET c.deleted = false WHERE c.id = :id")
    void restore(@Param("id") Long id);

    /**
     * 更新分类状态
     */
    @Modifying
    @Query("UPDATE HelpCategory c SET c.enabled = :enabled WHERE c.id = :id")
    void updateStatus(@Param("id") Long id, @Param("enabled") Boolean enabled);

    /**
     * 批量更新分类状态
     */
    @Modifying
    @Query("UPDATE HelpCategory c SET c.enabled = :enabled WHERE c.id IN :ids")
    void batchUpdateStatus(@Param("ids") List<Long> ids, @Param("enabled") Boolean enabled);

    /**
     * 更新分类可见性
     */
    @Modifying
    @Query("UPDATE HelpCategory c SET c.visible = :visible WHERE c.id = :id")
    void updateVisibility(@Param("id") Long id, @Param("visible") Boolean visible);

    /**
     * 查找最大排序号
     */
    @Query("SELECT COALESCE(MAX(c.sortOrder), 0) FROM HelpCategory c WHERE c.parentId = :parentId AND c.deleted = false")
    Integer findMaxSortOrderByParentId(@Param("parentId") Long parentId);

    /**
     * 查找根分类的最大排序号
     */
    @Query("SELECT COALESCE(MAX(c.sortOrder), 0) FROM HelpCategory c WHERE c.parentId IS NULL AND c.deleted = false")
    Integer findMaxSortOrderForRoot();

    /**
     * 根据关键词搜索分类
     */
    @Query("SELECT c FROM HelpCategory c WHERE " +
           "(LOWER(c.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(c.description) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "c.enabled = true AND c.deleted = false " +
           "ORDER BY c.level ASC, c.sortOrder ASC")
    List<HelpCategory> searchByKeyword(@Param("keyword") String keyword);

    /**
     * 查找指定层级的分类
     */
    List<HelpCategory> findByLevelAndEnabledTrueAndDeletedFalseOrderBySortOrderAsc(Integer level);

    /**
     * 查找叶子分类（没有子分类的分类）
     */
    @Query("SELECT c FROM HelpCategory c WHERE c.id NOT IN " +
           "(SELECT DISTINCT c2.parentId FROM HelpCategory c2 WHERE c2.parentId IS NOT NULL AND c2.deleted = false) " +
           "AND c.enabled = true AND c.deleted = false " +
           "ORDER BY c.level ASC, c.sortOrder ASC")
    List<HelpCategory> findLeafCategories();

    /**
     * 查找有内容的分类
     */
    @Query("SELECT c FROM HelpCategory c WHERE c.contentCount > 0 AND c.enabled = true AND c.deleted = false " +
           "ORDER BY c.contentCount DESC")
    List<HelpCategory> findCategoriesWithContent();

    /**
     * 查找热门分类（按内容数量排序）
     */
    @Query("SELECT c FROM HelpCategory c WHERE c.contentCount >= :minCount AND c.enabled = true AND c.deleted = false " +
           "ORDER BY c.contentCount DESC")
    List<HelpCategory> findPopularCategories(@Param("minCount") Integer minCount);

    /**
     * 统计各层级的分类数量
     */
    @Query("SELECT c.level, COUNT(c) FROM HelpCategory c WHERE c.deleted = false GROUP BY c.level ORDER BY c.level")
    List<Object[]> countByLevel();

    /**
     * 统计启用和禁用的分类数量
     */
    @Query("SELECT c.enabled, COUNT(c) FROM HelpCategory c WHERE c.deleted = false GROUP BY c.enabled")
    List<Object[]> countByStatus();

    /**
     * 查找需要更新路径的分类
     */
    @Query("SELECT c FROM HelpCategory c WHERE c.path IS NULL OR c.path = '' AND c.deleted = false")
    List<HelpCategory> findCategoriesNeedingPathUpdate();

    /**
     * 批量更新分类路径
     */
    @Modifying
    @Query("UPDATE HelpCategory c SET c.path = :path WHERE c.id = :id")
    void updatePath(@Param("id") Long id, @Param("path") String path);

    /**
     * 查找指定分类的父分类链
     */
    @Query(value = "WITH RECURSIVE category_tree AS (" +
                   "  SELECT id, name, parent_id, level, path FROM help_category WHERE id = :categoryId " +
                   "  UNION ALL " +
                   "  SELECT c.id, c.name, c.parent_id, c.level, c.path " +
                   "  FROM help_category c " +
                   "  INNER JOIN category_tree ct ON c.id = ct.parent_id" +
                   ") " +
                   "SELECT * FROM category_tree ORDER BY level", nativeQuery = true)
    List<Object[]> findParentChain(@Param("categoryId") Long categoryId);
}