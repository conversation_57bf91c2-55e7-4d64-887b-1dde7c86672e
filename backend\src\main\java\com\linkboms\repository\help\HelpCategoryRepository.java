package com.linkboms.repository.help;

import com.linkboms.entity.help.HelpCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 帮助分类Repository接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface HelpCategoryRepository extends JpaRepository<HelpCategory, Long> {

    /**
     * 查找所有启用的根分类
     */
    List<HelpCategory> findByParentIdIsNullAndEnabledTrueAndDeletedFalseOrderBySortOrderAsc();

    /**
     * 查找指定父分类下的子分类
     */
    List<HelpCategory> findByParentIdAndEnabledTrueAndDeletedFalseOrderBySortOrderAsc(Long parentId);

    /**
     * 查找所有启用的分类（树形结构）
     */
    List<HelpCategory> findByEnabledTrueAndDeletedFalseOrderByLevelAscSortOrderAsc();

    /**
     * 查找所有可见的分类
     */
    List<HelpCategory> findByVisibleTrueAndEnabledTrueAndDeletedFalseOrderBySortOrderAsc();

    /**
     * 根据名称查找分类
     */
    Optional<HelpCategory> findByNameAndDeletedFalse(String name);

    /**
     * 根据路径查找分类
     */
    Optional<HelpCategory> findByPathAndDeletedFalse(String path);

    /**
     * 检查名称是否已存在
     */
    boolean existsByNameAndDeletedFalse(String name);

    /**
     * 检查名称是否已存在（排除指定ID）
     */
    boolean existsByNameAndIdNotAndDeletedFalse(String name, Long id);

    /**
     * 检查路径是否已存在
     */
    boolean existsByPathAndDeletedFalse(String path);

    /**
     * 检查路径是否已存在（排除指定ID）
     */
    boolean existsByPathAndIdNotAndDeletedFalse(String path, Long id);

    /**
     * 查找指定分类的所有子分类（包括子子分类）
     */
    @Query("SELECT c FROM HelpCategory c WHERE c.path LIKE CONCAT(:parentPath, '%') AND c.deleted = false")
    List<HelpCategory> findAllDescendants(@Param("parentPath") String parentPath);

    /**
     * 查找指定分类的直接子分类
     */
    List<HelpCategory> findByParentIdAndDeletedFalseOrderBySortOrderAsc(Long parentId);

    /**
     * 查找指定层级的分类
     */
    List<HelpCategory> findByLevelAndEnabledTrueAndDeletedFalseOrderBySortOrderAsc(Integer level);

    /**
     * 查找指定父分类下的最大排序号
     */
    @Query("SELECT COALESCE(MAX(c.sortOrder), 0) FROM HelpCategory c WHERE " +
           "(:parentId IS NULL AND c.parentId IS NULL OR c.parentId = :parentId) AND c.deleted = false")
    Integer findMaxSortOrderByParentId(@Param("parentId") Long parentId);

    /**
     * 统计指定分类下的内容数量
     */
    @Query("SELECT COUNT(hc) FROM HelpContent hc WHERE hc.categoryId = :categoryId AND hc.deleted = false")
    Long countContentsByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 统计指定分类及其子分类下的内容数量
     */
    @Query("SELECT COUNT(hc) FROM HelpContent hc " +
           "JOIN HelpCategory c ON hc.categoryId = c.id " +
           "WHERE (c.id = :categoryId OR c.path LIKE CONCAT(:categoryPath, '%')) " +
           "AND hc.deleted = false AND c.deleted = false")
    Long countContentsInCategoryTree(@Param("categoryId") Long categoryId, @Param("categoryPath") String categoryPath);

    /**
     * 查找热门分类（按内容数量排序）
     */
    @Query("SELECT c, COUNT(hc) as contentCount FROM HelpCategory c " +
           "LEFT JOIN HelpContent hc ON c.id = hc.categoryId AND hc.deleted = false " +
           "WHERE c.enabled = true AND c.deleted = false " +
           "GROUP BY c.id ORDER BY contentCount DESC")
    List<Object[]> findPopularCategories();

    /**
     * 搜索分类（按名称或描述）
     */
    @Query("SELECT c FROM HelpCategory c WHERE " +
           "(LOWER(c.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(c.description) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "c.enabled = true AND c.deleted = false " +
           "ORDER BY c.level ASC, c.sortOrder ASC")
    List<HelpCategory> searchCategories(@Param("keyword") String keyword);

    /**
     * 查找需要重建路径的分类
     */
    @Query("SELECT c FROM HelpCategory c WHERE c.path IS NULL OR c.path = '' AND c.deleted = false")
    List<HelpCategory> findCategoriesNeedingPathRebuild();

    /**
     * 批量更新分类状态
     */
    @Query("UPDATE HelpCategory c SET c.enabled = :enabled, c.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE c.id IN :ids")
    void batchUpdateEnabled(@Param("ids") List<Long> ids, @Param("enabled") Boolean enabled);

    /**
     * 批量软删除分类
     */
    @Query("UPDATE HelpCategory c SET c.deleted = true, c.enabled = false, c.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE c.id IN :ids")
    void batchSoftDelete(@Param("ids") List<Long> ids);
}
