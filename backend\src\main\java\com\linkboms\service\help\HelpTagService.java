package com.linkboms.service.help;

import com.linkboms.entity.help.HelpTag;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 帮助标签服务接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface HelpTagService {

    /**
     * 创建标签
     */
    HelpTag createTag(HelpTag tag);

    /**
     * 更新标签
     */
    HelpTag updateTag(Long id, HelpTag tag);

    /**
     * 根据ID获取标签
     */
    HelpTag getTagById(Long id);

    /**
     * 根据ID获取标签（可选）
     */
    Optional<HelpTag> findTagById(Long id);

    /**
     * 根据名称获取标签
     */
    Optional<HelpTag> findTagByName(String name);

    /**
     * 删除标签
     */
    void deleteTag(Long id);

    /**
     * 软删除标签
     */
    void softDeleteTag(Long id);

    /**
     * 恢复标签
     */
    void restoreTag(Long id);

    /**
     * 启用标签
     */
    void enableTag(Long id);

    /**
     * 禁用标签
     */
    void disableTag(Long id);

    /**
     * 获取所有标签（分页）
     */
    Page<HelpTag> getAllTags(Pageable pageable);

    /**
     * 根据类型获取标签
     */
    Page<HelpTag> getTagsByType(HelpTag.TagType type, Pageable pageable);

    /**
     * 获取所有启用的标签
     */
    List<HelpTag> getAllEnabledTags();

    /**
     * 根据类型获取启用的标签
     */
    List<HelpTag> getEnabledTagsByType(HelpTag.TagType type);

    /**
     * 搜索标签
     */
    List<HelpTag> searchTags(String keyword);

    /**
     * 获取热门标签
     */
    List<HelpTag> getPopularTags(int limit);

    /**
     * 获取未使用的标签
     */
    List<HelpTag> getUnusedTags();

    /**
     * 获取使用次数超过指定值的标签
     */
    List<HelpTag> getTagsWithMinUsage(Long minUsage);

    /**
     * 根据颜色获取标签
     */
    List<HelpTag> getTagsByColor(String color);

    /**
     * 增加使用次数
     */
    void incrementUsageCount(Long id);

    /**
     * 减少使用次数
     */
    void decrementUsageCount(Long id);

    /**
     * 重置使用次数
     */
    void resetUsageCount(Long id);

    /**
     * 批量重置使用次数
     */
    void batchResetUsageCount();

    /**
     * 批量更新标签状态
     */
    void batchUpdateTagStatus(List<Long> ids, boolean enabled);

    /**
     * 批量删除标签
     */
    void batchDeleteTags(List<Long> ids);

    /**
     * 合并标签
     */
    void mergeTags(Long sourceTagId, Long targetTagId);

    /**
     * 拆分标签
     */
    HelpTag splitTag(Long tagId, String newTagName, List<Long> contentIds);

    /**
     * 验证标签数据
     */
    void validateTag(HelpTag tag);

    /**
     * 检查标签名称是否存在
     */
    boolean isTagNameExists(String name);

    /**
     * 检查标签名称是否存在（排除指定ID）
     */
    boolean isTagNameExists(String name, Long excludeId);

    /**
     * 获取标签统计信息
     */
    Object getTagStatistics();

    /**
     * 获取标签类型统计
     */
    Object getTagTypeStatistics();

    /**
     * 获取标签云数据
     */
    List<Object[]> getTagCloudData(int limit);

    /**
     * 根据分类推荐标签
     */
    List<HelpTag> getRecommendedTagsByCategory(Long categoryId, int limit);

    /**
     * 查找相似标签
     */
    List<HelpTag> findSimilarTags(String tagName, Long excludeId);

    /**
     * 根据内容获取标签
     */
    List<HelpTag> getTagsByContent(Long contentId);

    /**
     * 自动生成标签
     */
    List<String> generateTagsFromContent(String content, int maxCount);

    /**
     * 标签推荐算法
     */
    List<HelpTag> recommendTags(String content, Long categoryId, int limit);

    /**
     * 清理无用标签
     */
    void cleanupUnusedTags();

    /**
     * 导出标签数据
     */
    byte[] exportTags();

    /**
     * 导入标签数据
     */
    void importTags(byte[] data);

    /**
     * 标签使用趋势分析
     */
    Object getTagUsageTrend(Long tagId, int days);

    /**
     * 获取标签关联度
     */
    Object getTagCorrelation(Long tagId);
}
