package com.linkboms.service.help;

import com.linkboms.entity.help.HelpTag;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 帮助标签服务接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface HelpTagService {

    // ==================== 基础CRUD操作 ====================
    
    /**
     * 创建标签
     * 
     * @param tag 标签信息
     * @return 创建的标签
     */
    HelpTag createTag(HelpTag tag);
    
    /**
     * 更新标签
     * 
     * @param id 标签ID
     * @param tag 标签信息
     * @return 更新后的标签
     */
    HelpTag updateTag(Long id, HelpTag tag);
    
    /**
     * 删除标签（软删除）
     * 
     * @param id 标签ID
     */
    void deleteTag(Long id);
    
    /**
     * 永久删除标签
     * 
     * @param id 标签ID
     */
    void permanentDeleteTag(Long id);
    
    /**
     * 恢复已删除的标签
     * 
     * @param id 标签ID
     */
    void restoreTag(Long id);
    
    // ==================== 查询操作 ====================
    
    /**
     * 根据ID获取标签
     * 
     * @param id 标签ID
     * @return 标签信息
     */
    HelpTag getTagById(Long id);
    
    /**
     * 分页获取标签列表
     * 
     * @param pageable 分页参数
     * @return 标签分页列表
     */
    Page<HelpTag> getTagList(Pageable pageable);
    
    /**
     * 获取所有标签
     * 
     * @return 标签列表
     */
    List<HelpTag> getAllTags();
    
    /**
     * 根据类型获取标签
     * 
     * @param type 标签类型
     * @return 标签列表
     */
    List<HelpTag> getTagsByType(HelpTag.TagType type);
    
    /**
     * 根据名称获取标签
     * 
     * @param name 标签名称
     * @return 标签信息
     */
    HelpTag getTagByName(String name);
    
    /**
     * 搜索标签
     * 
     * @param keyword 关键词
     * @return 标签列表
     */
    List<HelpTag> searchTags(String keyword);
    
    /**
     * 获取热门标签
     * 
     * @param limit 数量限制
     * @return 热门标签列表
     */
    List<HelpTag> getPopularTags(int limit);
    
    /**
     * 获取常用标签
     * 
     * @param limit 数量限制
     * @return 常用标签列表
     */
    List<HelpTag> getFrequentTags(int limit);
    
    /**
     * 获取未使用的标签
     * 
     * @return 未使用标签列表
     */
    List<HelpTag> getUnusedTags();
    
    /**
     * 获取启用的标签
     * 
     * @return 启用标签列表
     */
    List<HelpTag> getEnabledTags();
    
    /**
     * 获取指定内容的标签
     * 
     * @param contentId 内容ID
     * @return 标签列表
     */
    List<HelpTag> getTagsByContentId(Long contentId);
    
    /**
     * 获取相关标签
     * 
     * @param tagId 标签ID
     * @param limit 数量限制
     * @return 相关标签列表
     */
    List<HelpTag> getRelatedTags(Long tagId, int limit);
    
    /**
     * 获取标签云数据
     * 
     * @param limit 数量限制
     * @return 标签云数据
     */
    List<Map<String, Object>> getTagCloudData(int limit);
    
    /**
     * 获取建议标签
     * 
     * @param keyword 关键词
     * @param limit 数量限制
     * @return 建议标签列表
     */
    List<HelpTag> getSuggestedTags(String keyword, int limit);
    
    // ==================== 状态管理 ====================
    
    /**
     * 启用标签
     * 
     * @param id 标签ID
     */
    void enableTag(Long id);
    
    /**
     * 禁用标签
     * 
     * @param id 标签ID
     */
    void disableTag(Long id);
    
    /**
     * 设置标签排序
     * 
     * @param id 标签ID
     * @param sortOrder 排序号
     */
    void setTagSortOrder(Long id, Integer sortOrder);
    
    /**
     * 批量排序标签
     * 
     * @param sortData 排序数据
     */
    void sortTags(List<Map<String, Object>> sortData);
    
    // ==================== 使用次数管理 ====================
    
    /**
     * 增加标签使用次数
     * 
     * @param id 标签ID
     */
    void incrementUsageCount(Long id);
    
    /**
     * 减少标签使用次数
     * 
     * @param id 标签ID
     */
    void decrementUsageCount(Long id);
    
    /**
     * 更新标签使用次数
     * 
     * @param id 标签ID
     * @param count 使用次数
     */
    void updateUsageCount(Long id, Long count);
    
    /**
     * 批量增加标签使用次数
     * 
     * @param tagIds 标签ID列表
     */
    void batchIncrementUsageCount(List<Long> tagIds);
    
    /**
     * 批量减少标签使用次数
     * 
     * @param tagIds 标签ID列表
     */
    void batchDecrementUsageCount(List<Long> tagIds);
    
    /**
     * 重新计算标签使用次数
     * 
     * @param id 标签ID
     */
    void recalculateUsageCount(Long id);
    
    /**
     * 重新计算所有标签使用次数
     */
    void recalculateAllUsageCounts();
    
    // ==================== 批量操作 ====================
    
    /**
     * 批量创建标签
     * 
     * @param tags 标签列表
     * @return 创建的标签列表
     */
    List<HelpTag> batchCreateTags(List<HelpTag> tags);
    
    /**
     * 批量删除标签
     * 
     * @param ids 标签ID列表
     */
    void batchDeleteTags(List<Long> ids);
    
    /**
     * 批量恢复标签
     * 
     * @param ids 标签ID列表
     */
    void batchRestoreTags(List<Long> ids);
    
    /**
     * 批量启用标签
     * 
     * @param ids 标签ID列表
     */
    void batchEnableTags(List<Long> ids);
    
    /**
     * 批量禁用标签
     * 
     * @param ids 标签ID列表
     */
    void batchDisableTags(List<Long> ids);
    
    // ==================== 标签合并与拆分 ====================
    
    /**
     * 合并标签
     * 
     * @param sourceTagIds 源标签ID列表
     * @param targetTagId 目标标签ID
     */
    void mergeTags(List<Long> sourceTagIds, Long targetTagId);
    
    /**
     * 拆分标签
     * 
     * @param sourceTagId 源标签ID
     * @param newTags 新标签列表
     */
    void splitTag(Long sourceTagId, List<HelpTag> newTags);
    
    // ==================== 清理与维护 ====================
    
    /**
     * 清理未使用的标签
     */
    void cleanupUnusedTags();
    
    /**
     * 清理重复标签
     */
    void cleanupDuplicateTags();
    
    // ==================== 导入导出 ====================
    
    /**
     * 导出标签
     * 
     * @param format 导出格式（json, csv, excel）
     * @return 导出数据
     */
    byte[] exportTags(String format);
    
    /**
     * 导入标签
     * 
     * @param file 导入文件
     * @return 导入的标签列表
     */
    List<HelpTag> importTags(MultipartFile file);
    
    // ==================== 统计信息 ====================
    
    /**
     * 获取标签统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getTagStatistics();
    
    /**
     * 按类型统计标签数量
     * 
     * @return 类型统计
     */
    Map<HelpTag.TagType, Long> getTagStatisticsByType();
    
    /**
     * 获取使用次数分布
     * 
     * @return 使用次数分布
     */
    Map<String, Long> getUsageCountDistribution();
    
    // ==================== 验证与检查 ====================
    
    /**
     * 验证标签数据
     * 
     * @param tag 标签信息
     * @return 验证错误列表
     */
    List<String> validateTag(HelpTag tag);
    
    /**
     * 检查标签名称是否存在
     * 
     * @param name 标签名称
     * @param excludeId 排除的标签ID
     * @return 是否存在
     */
    boolean checkNameExists(String name, Long excludeId);
    
    /**
     * 获取下一个排序号
     * 
     * @param type 标签类型
     * @return 下一个排序号
     */
    Integer getNextSortOrder(HelpTag.TagType type);
    
    // ==================== 标签推荐 ====================
    
    /**
     * 根据内容推荐标签
     * 
     * @param content 内容文本
     * @param limit 推荐数量
     * @return 推荐标签列表
     */
    List<HelpTag> recommendTagsForContent(String content, int limit);
    
    /**
     * 根据用户行为推荐标签
     * 
     * @param userId 用户ID
     * @param limit 推荐数量
     * @return 推荐标签列表
     */
    List<HelpTag> recommendTagsForUser(Long userId, int limit);
}