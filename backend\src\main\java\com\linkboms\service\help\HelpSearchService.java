package com.linkboms.service.help;

import com.linkboms.dto.help.SearchResultDto;
import com.linkboms.dto.help.SearchSuggestionDto;
import com.linkboms.entity.help.HelpContent;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * 帮助搜索服务接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface HelpSearchService {

    /**
     * 全文搜索帮助内容
     * 
     * @param keyword 搜索关键词
     * @param filters 搜索过滤条件
     * @param pageable 分页参数
     * @return 搜索结果
     */
    Page<SearchResultDto> searchContent(String keyword, Map<String, Object> filters, Pageable pageable);

    /**
     * 高级搜索
     * 
     * @param searchParams 搜索参数
     * @param pageable 分页参数
     * @return 搜索结果
     */
    Page<SearchResultDto> advancedSearch(Map<String, Object> searchParams, Pageable pageable);

    /**
     * 获取搜索建议
     * 
     * @param keyword 关键词
     * @param limit 建议数量限制
     * @return 搜索建议列表
     */
    List<SearchSuggestionDto> getSearchSuggestions(String keyword, int limit);

    /**
     * 获取热门搜索关键词
     * 
     * @param limit 数量限制
     * @return 热门关键词列表
     */
    List<String> getHotKeywords(int limit);

    /**
     * 获取相关内容
     * 
     * @param contentId 内容ID
     * @param limit 数量限制
     * @return 相关内容列表
     */
    List<HelpContent> getRelatedContent(Long contentId, int limit);

    /**
     * 记录搜索日志
     * 
     * @param keyword 搜索关键词
     * @param userId 用户ID
     * @param resultCount 搜索结果数量
     */
    void logSearch(String keyword, Long userId, int resultCount);

    /**
     * 重建搜索索引
     */
    void rebuildSearchIndex();

    /**
     * 更新内容索引
     * 
     * @param contentId 内容ID
     */
    void updateContentIndex(Long contentId);

    /**
     * 删除内容索引
     * 
     * @param contentId 内容ID
     */
    void deleteContentIndex(Long contentId);

    /**
     * 获取搜索统计信息
     * 
     * @return 搜索统计
     */
    Map<String, Object> getSearchStatistics();

    /**
     * 智能搜索推荐
     * 
     * @param userId 用户ID
     * @param limit 推荐数量
     * @return 推荐内容列表
     */
    List<HelpContent> getRecommendedContent(Long userId, int limit);

    /**
     * 搜索自动完成
     * 
     * @param prefix 前缀
     * @param limit 建议数量
     * @return 自动完成建议
     */
    List<String> getAutoCompleteSuggestions(String prefix, int limit);

    /**
     * 按标签搜索
     * 
     * @param tagNames 标签名称列表
     * @param pageable 分页参数
     * @return 搜索结果
     */
    Page<HelpContent> searchByTags(List<String> tagNames, Pageable pageable);

    /**
     * 按分类搜索
     * 
     * @param categoryId 分类ID
     * @param keyword 关键词（可选）
     * @param pageable 分页参数
     * @return 搜索结果
     */
    Page<HelpContent> searchByCategory(Long categoryId, String keyword, Pageable pageable);

    /**
     * 模糊搜索
     * 
     * @param keyword 关键词
     * @param threshold 相似度阈值
     * @param pageable 分页参数
     * @return 搜索结果
     */
    Page<SearchResultDto> fuzzySearch(String keyword, double threshold, Pageable pageable);

    /**
     * 获取搜索历史
     * 
     * @param userId 用户ID
     * @param limit 数量限制
     * @return 搜索历史
     */
    List<String> getSearchHistory(Long userId, int limit);

    /**
     * 清除搜索历史
     * 
     * @param userId 用户ID
     */
    void clearSearchHistory(Long userId);

    /**
     * 保存搜索条件
     * 
     * @param userId 用户ID
     * @param name 搜索名称
     * @param searchParams 搜索参数
     */
    void saveSearchCondition(Long userId, String name, Map<String, Object> searchParams);

    /**
     * 获取保存的搜索条件
     * 
     * @param userId 用户ID
     * @return 保存的搜索条件列表
     */
    List<Map<String, Object>> getSavedSearchConditions(Long userId);

    /**
     * 删除保存的搜索条件
     * 
     * @param userId 用户ID
     * @param searchId 搜索ID
     */
    void deleteSavedSearchCondition(Long userId, Long searchId);
}
