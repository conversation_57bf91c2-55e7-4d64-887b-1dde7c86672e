import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { 
  HelpCategory,
  HelpContent,
  HelpTag,
  HelpAttachment,
  HelpFeedback,
  HelpNavigation,
  HelpStatistics,
  SearchParams,
  SearchResult,
  CategoryForm,
  ContentForm,
  FeedbackForm,
  PageResponse,
  ContentType,
  ContentStatus
} from '../../types/help';
import { helpService } from '../../services/helpService';

// 异步操作
export const fetchCategoryTree = createAsyncThunk(
  'help/fetchCategoryTree',
  async () => {
    return await helpService.getCategoryTree();
  }
);

export const fetchContentList = createAsyncThunk(
  'help/fetchContentList',
  async (params: SearchParams) => {
    return await helpService.getContentList(params);
  }
);

export const fetchContentById = createAsyncThunk(
  'help/fetchContentById',
  async (id: number) => {
    return await helpService.getContentById(id);
  }
);

export const fetchContentByCategory = createAsyncThunk(
  'help/fetchContentByCategory',
  async ({ categoryId, page, size }: { categoryId: number; page?: number; size?: number }) => {
    return await helpService.getContentByCategory(categoryId, page, size);
  }
);

export const searchContent = createAsyncThunk(
  'help/searchContent',
  async (params: SearchParams) => {
    return await helpService.searchContent(params);
  }
);

export const fetchFeaturedContent = createAsyncThunk(
  'help/fetchFeaturedContent',
  async ({ page, size }: { page?: number; size?: number } = {}) => {
    return await helpService.getFeaturedContent(page, size);
  }
);

export const fetchPopularContent = createAsyncThunk(
  'help/fetchPopularContent',
  async ({ page, size }: { page?: number; size?: number } = {}) => {
    return await helpService.getPopularContent(page, size);
  }
);

export const fetchLatestContent = createAsyncThunk(
  'help/fetchLatestContent',
  async ({ page, size }: { page?: number; size?: number } = {}) => {
    return await helpService.getLatestContent(page, size);
  }
);

export const fetchPopularTags = createAsyncThunk(
  'help/fetchPopularTags',
  async (limit: number = 20) => {
    return await helpService.getPopularTags(limit);
  }
);

export const fetchHelpStatistics = createAsyncThunk(
  'help/fetchHelpStatistics',
  async () => {
    return await helpService.getHelpStatistics();
  }
);

export const fetchHelpNavigation = createAsyncThunk(
  'help/fetchHelpNavigation',
  async () => {
    return await helpService.getHelpNavigation();
  }
);

export const createContent = createAsyncThunk(
  'help/createContent',
  async (content: ContentForm) => {
    return await helpService.createContent(content);
  }
);

export const updateContent = createAsyncThunk(
  'help/updateContent',
  async ({ id, content }: { id: number; content: ContentForm }) => {
    return await helpService.updateContent(id, content);
  }
);

export const deleteContent = createAsyncThunk(
  'help/deleteContent',
  async (id: number) => {
    await helpService.deleteContent(id);
    return id;
  }
);

export const submitFeedback = createAsyncThunk(
  'help/submitFeedback',
  async (feedback: FeedbackForm) => {
    return await helpService.submitFeedback(feedback);
  }
);

export const incrementViewCount = createAsyncThunk(
  'help/incrementViewCount',
  async (id: number) => {
    await helpService.incrementViewCount(id);
    return id;
  }
);

export const likeContent = createAsyncThunk(
  'help/likeContent',
  async (id: number) => {
    await helpService.likeContent(id);
    return id;
  }
);

// 状态接口
interface HelpState {
  // 分类相关
  categories: HelpCategory[];
  categoryTree: HelpCategory[];
  currentCategory: HelpCategory | null;
  
  // 内容相关
  contentList: HelpContent[];
  currentContent: HelpContent | null;
  featuredContent: HelpContent[];
  popularContent: HelpContent[];
  latestContent: HelpContent[];
  relatedContent: HelpContent[];
  
  // 搜索相关
  searchResults: SearchResult | null;
  searchParams: SearchParams;
  
  // 标签相关
  tags: HelpTag[];
  popularTags: HelpTag[];
  
  // 附件相关
  attachments: HelpAttachment[];
  
  // 反馈相关
  feedback: HelpFeedback[];
  
  // 导航和统计
  navigation: HelpNavigation | null;
  statistics: HelpStatistics | null;
  
  // 分页信息
  pagination: {
    currentPage: number;
    totalPages: number;
    totalElements: number;
    size: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
  
  // 加载状态
  loading: {
    categories: boolean;
    content: boolean;
    search: boolean;
    statistics: boolean;
    navigation: boolean;
    feedback: boolean;
    tags: boolean;
  };
  
  // 错误信息
  error: string | null;
  
  // UI 状态
  ui: {
    sidebarCollapsed: boolean;
    currentView: 'home' | 'search' | 'content' | 'category';
    selectedCategoryId: number | null;
    selectedContentType: ContentType | null;
    showFeedbackModal: boolean;
    showContentModal: boolean;
  };
}

// 初始状态
const initialState: HelpState = {
  categories: [],
  categoryTree: [],
  currentCategory: null,
  contentList: [],
  currentContent: null,
  featuredContent: [],
  popularContent: [],
  latestContent: [],
  relatedContent: [],
  searchResults: null,
  searchParams: {},
  tags: [],
  popularTags: [],
  attachments: [],
  feedback: [],
  navigation: null,
  statistics: null,
  pagination: {
    currentPage: 0,
    totalPages: 0,
    totalElements: 0,
    size: 20,
    hasNext: false,
    hasPrevious: false,
  },
  loading: {
    categories: false,
    content: false,
    search: false,
    statistics: false,
    navigation: false,
    feedback: false,
    tags: false,
  },
  error: null,
  ui: {
    sidebarCollapsed: false,
    currentView: 'home',
    selectedCategoryId: null,
    selectedContentType: null,
    showFeedbackModal: false,
    showContentModal: false,
  },
};

// Slice
const helpSlice = createSlice({
  name: 'help',
  initialState,
  reducers: {
    // UI 状态管理
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.ui.sidebarCollapsed = action.payload;
    },
    setCurrentView: (state, action: PayloadAction<'home' | 'search' | 'content' | 'category'>) => {
      state.ui.currentView = action.payload;
    },
    setSelectedCategory: (state, action: PayloadAction<number | null>) => {
      state.ui.selectedCategoryId = action.payload;
    },
    setSelectedContentType: (state, action: PayloadAction<ContentType | null>) => {
      state.ui.selectedContentType = action.payload;
    },
    setShowFeedbackModal: (state, action: PayloadAction<boolean>) => {
      state.ui.showFeedbackModal = action.payload;
    },
    setShowContentModal: (state, action: PayloadAction<boolean>) => {
      state.ui.showContentModal = action.payload;
    },
    
    // 搜索参数管理
    setSearchParams: (state, action: PayloadAction<SearchParams>) => {
      state.searchParams = { ...state.searchParams, ...action.payload };
    },
    clearSearchParams: (state) => {
      state.searchParams = {};
      state.searchResults = null;
    },
    
    // 当前内容管理
    setCurrentContent: (state, action: PayloadAction<HelpContent | null>) => {
      state.currentContent = action.payload;
    },
    setCurrentCategory: (state, action: PayloadAction<HelpCategory | null>) => {
      state.currentCategory = action.payload;
    },
    
    // 清除状态
    clearError: (state) => {
      state.error = null;
    },
    clearCurrentContent: (state) => {
      state.currentContent = null;
      state.relatedContent = [];
    },
    clearSearchResults: (state) => {
      state.searchResults = null;
      state.searchParams = {};
    },
    
    // 内容操作
    updateContentInList: (state, action: PayloadAction<HelpContent>) => {
      const index = state.contentList.findIndex(item => item.id === action.payload.id);
      if (index !== -1) {
        state.contentList[index] = action.payload;
      }
      if (state.currentContent?.id === action.payload.id) {
        state.currentContent = action.payload;
      }
    },
    removeContentFromList: (state, action: PayloadAction<number>) => {
      state.contentList = state.contentList.filter(item => item.id !== action.payload);
      if (state.currentContent?.id === action.payload) {
        state.currentContent = null;
      }
    },
  },
  extraReducers: (builder) => {
    // 分类树
    builder
      .addCase(fetchCategoryTree.pending, (state) => {
        state.loading.categories = true;
        state.error = null;
      })
      .addCase(fetchCategoryTree.fulfilled, (state, action) => {
        state.loading.categories = false;
        state.categoryTree = action.payload;
        state.categories = flattenCategories(action.payload);
      })
      .addCase(fetchCategoryTree.rejected, (state, action) => {
        state.loading.categories = false;
        state.error = action.error.message || '获取分类失败';
      });

    // 内容列表
    builder
      .addCase(fetchContentList.pending, (state) => {
        state.loading.content = true;
        state.error = null;
      })
      .addCase(fetchContentList.fulfilled, (state, action) => {
        state.loading.content = false;
        state.contentList = action.payload.content;
        updatePagination(state, action.payload);
      })
      .addCase(fetchContentList.rejected, (state, action) => {
        state.loading.content = false;
        state.error = action.error.message || '获取内容列表失败';
      });

    // 内容详情
    builder
      .addCase(fetchContentById.pending, (state) => {
        state.loading.content = true;
        state.error = null;
      })
      .addCase(fetchContentById.fulfilled, (state, action) => {
        state.loading.content = false;
        state.currentContent = action.payload;
      })
      .addCase(fetchContentById.rejected, (state, action) => {
        state.loading.content = false;
        state.error = action.error.message || '获取内容详情失败';
      });

    // 分类内容
    builder
      .addCase(fetchContentByCategory.fulfilled, (state, action) => {
        state.contentList = action.payload.content;
        updatePagination(state, action.payload);
      });

    // 搜索内容
    builder
      .addCase(searchContent.pending, (state) => {
        state.loading.search = true;
        state.error = null;
      })
      .addCase(searchContent.fulfilled, (state, action) => {
        state.loading.search = false;
        state.searchResults = action.payload;
      })
      .addCase(searchContent.rejected, (state, action) => {
        state.loading.search = false;
        state.error = action.error.message || '搜索失败';
      });

    // 特色内容
    builder
      .addCase(fetchFeaturedContent.fulfilled, (state, action) => {
        state.featuredContent = action.payload.content;
      });

    // 热门内容
    builder
      .addCase(fetchPopularContent.fulfilled, (state, action) => {
        state.popularContent = action.payload.content;
      });

    // 最新内容
    builder
      .addCase(fetchLatestContent.fulfilled, (state, action) => {
        state.latestContent = action.payload.content;
      });

    // 热门标签
    builder
      .addCase(fetchPopularTags.pending, (state) => {
        state.loading.tags = true;
      })
      .addCase(fetchPopularTags.fulfilled, (state, action) => {
        state.loading.tags = false;
        state.popularTags = action.payload;
      })
      .addCase(fetchPopularTags.rejected, (state, action) => {
        state.loading.tags = false;
        state.error = action.error.message || '获取标签失败';
      });

    // 统计信息
    builder
      .addCase(fetchHelpStatistics.pending, (state) => {
        state.loading.statistics = true;
      })
      .addCase(fetchHelpStatistics.fulfilled, (state, action) => {
        state.loading.statistics = false;
        state.statistics = action.payload;
      })
      .addCase(fetchHelpStatistics.rejected, (state, action) => {
        state.loading.statistics = false;
        state.error = action.error.message || '获取统计信息失败';
      });

    // 导航信息
    builder
      .addCase(fetchHelpNavigation.pending, (state) => {
        state.loading.navigation = true;
      })
      .addCase(fetchHelpNavigation.fulfilled, (state, action) => {
        state.loading.navigation = false;
        state.navigation = action.payload;
      })
      .addCase(fetchHelpNavigation.rejected, (state, action) => {
        state.loading.navigation = false;
        state.error = action.error.message || '获取导航信息失败';
      });

    // 创建内容
    builder
      .addCase(createContent.fulfilled, (state, action) => {
        state.contentList.unshift(action.payload);
        state.currentContent = action.payload;
      });

    // 更新内容
    builder
      .addCase(updateContent.fulfilled, (state, action) => {
        const index = state.contentList.findIndex(item => item.id === action.payload.id);
        if (index !== -1) {
          state.contentList[index] = action.payload;
        }
        if (state.currentContent?.id === action.payload.id) {
          state.currentContent = action.payload;
        }
      });

    // 删除内容
    builder
      .addCase(deleteContent.fulfilled, (state, action) => {
        state.contentList = state.contentList.filter(item => item.id !== action.payload);
        if (state.currentContent?.id === action.payload) {
          state.currentContent = null;
        }
      });

    // 提交反馈
    builder
      .addCase(submitFeedback.pending, (state) => {
        state.loading.feedback = true;
      })
      .addCase(submitFeedback.fulfilled, (state, action) => {
        state.loading.feedback = false;
        state.feedback.unshift(action.payload);
      })
      .addCase(submitFeedback.rejected, (state, action) => {
        state.loading.feedback = false;
        state.error = action.error.message || '提交反馈失败';
      });

    // 增加浏览量
    builder
      .addCase(incrementViewCount.fulfilled, (state, action) => {
        if (state.currentContent?.id === action.payload) {
          state.currentContent.viewCount++;
        }
        const index = state.contentList.findIndex(item => item.id === action.payload);
        if (index !== -1) {
          state.contentList[index].viewCount++;
        }
      });

    // 点赞内容
    builder
      .addCase(likeContent.fulfilled, (state, action) => {
        if (state.currentContent?.id === action.payload) {
          state.currentContent.likeCount++;
        }
        const index = state.contentList.findIndex(item => item.id === action.payload);
        if (index !== -1) {
          state.contentList[index].likeCount++;
        }
      });
  },
});

// 辅助函数
function flattenCategories(categories: HelpCategory[]): HelpCategory[] {
  const result: HelpCategory[] = [];
  
  function flatten(cats: HelpCategory[]) {
    for (const cat of cats) {
      result.push(cat);
      if (cat.children && cat.children.length > 0) {
        flatten(cat.children);
      }
    }
  }
  
  flatten(categories);
  return result;
}

function updatePagination(state: HelpState, pageResponse: PageResponse<any>) {
  state.pagination = {
    currentPage: pageResponse.number,
    totalPages: pageResponse.totalPages,
    totalElements: pageResponse.totalElements,
    size: pageResponse.size,
    hasNext: !pageResponse.last,
    hasPrevious: !pageResponse.first,
  };
}

export const {
  setSidebarCollapsed,
  setCurrentView,
  setSelectedCategory,
  setSelectedContentType,
  setShowFeedbackModal,
  setShowContentModal,
  setSearchParams,
  clearSearchParams,
  setCurrentContent,
  setCurrentCategory,
  clearError,
  clearCurrentContent,
  clearSearchResults,
  updateContentInList,
  removeContentFromList,
} = helpSlice.actions;

export default helpSlice.reducer;
