package com.linkboms.service.help.impl;

import com.linkboms.entity.help.HelpContent;
import com.linkboms.entity.help.HelpTag;
import com.linkboms.entity.help.HelpAttachment;
import com.linkboms.repository.help.HelpContentRepository;
import com.linkboms.repository.help.HelpTagRepository;
import com.linkboms.repository.help.HelpAttachmentRepository;
import com.linkboms.service.help.HelpContentService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import jakarta.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 帮助内容服务实现类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class HelpContentServiceImpl implements HelpContentService {

    private final HelpContentRepository contentRepository;
    private final HelpTagRepository tagRepository;
    private final HelpAttachmentRepository attachmentRepository;

    @Override
    @Transactional
    public HelpContent createContent(HelpContent content) {
        log.info("创建帮助内容: {}", content.getTitle());
        
        // 设置默认值
        if (content.getStatus() == null) {
            content.setStatus(HelpContent.ContentStatus.DRAFT);
        }
        if (content.getViewCount() == null) {
            content.setViewCount(0L);
        }
        if (content.getLikeCount() == null) {
            content.setLikeCount(0L);
        }
        if (content.getVersion() == null) {
            content.setVersion("1.0");
        }
        
        content.setCreatedAt(LocalDateTime.now());
        content.setUpdatedAt(LocalDateTime.now());
        
        return contentRepository.save(content);
    }

    @Override
    @Transactional
    public HelpContent updateContent(Long id, HelpContent content) {
        log.info("更新帮助内容: {}", id);
        
        HelpContent existingContent = getContentById(id);
        
        // 更新字段
        existingContent.setTitle(content.getTitle());
        existingContent.setContent(content.getContent());
        existingContent.setSummary(content.getSummary());
        existingContent.setType(content.getType());
        existingContent.setCategoryId(content.getCategoryId());
        existingContent.setTags(content.getTags());
        existingContent.setDifficulty(content.getDifficulty());
        existingContent.setEstimatedReadTime(content.getEstimatedReadTime());
        existingContent.setKeywords(content.getKeywords());
        existingContent.setMetaDescription(content.getMetaDescription());
        existingContent.setUpdatedAt(LocalDateTime.now());
        
        return contentRepository.save(existingContent);
    }

    @Override
    public HelpContent getContentById(Long id) {
        return contentRepository.findByIdAndDeletedFalse(id)
            .orElseThrow(() -> new RuntimeException("帮助内容不存在: " + id));
    }

    @Override
    public HelpContent getContentByIdIncludeDeleted(Long id) {
        return contentRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("帮助内容不存在: " + id));
    }

    @Override
    @Transactional
    public void deleteContent(Long id) {
        log.info("删除帮助内容: {}", id);
        contentRepository.softDelete(id);
    }

    @Override
    @Transactional
    public void batchDeleteContent(List<Long> ids) {
        log.info("批量删除帮助内容: {}", ids);
        contentRepository.batchSoftDelete(ids);
    }

    @Override
    @Transactional
    public void restoreContent(Long id) {
        log.info("恢复帮助内容: {}", id);
        contentRepository.restore(id);
    }

    @Override
    @Transactional
    public void permanentDeleteContent(Long id) {
        log.info("永久删除帮助内容: {}", id);
        contentRepository.deleteById(id);
    }

    @Override
    public Page<HelpContent> getContentList(Pageable pageable) {
        return contentRepository.findByDeletedFalseOrderByUpdatedAtDesc(pageable);
    }

    @Override
    public Page<HelpContent> getContentByType(HelpContent.ContentType type, Pageable pageable) {
        return contentRepository.findByTypeAndDeletedFalseOrderByUpdatedAtDesc(type, pageable);
    }

    @Override
    public Page<HelpContent> getContentByCategory(Long categoryId, Pageable pageable) {
        return contentRepository.findByCategoryIdAndDeletedFalseOrderByUpdatedAtDesc(categoryId, pageable);
    }

    @Override
    public Page<HelpContent> getContentByStatus(HelpContent.ContentStatus status, Pageable pageable) {
        return contentRepository.findByStatusAndDeletedFalseOrderByUpdatedAtDesc(status, pageable);
    }

    @Override
    public Page<HelpContent> getContentByAuthor(String author, Pageable pageable) {
        return contentRepository.findByAuthorAndDeletedFalseOrderByUpdatedAtDesc(author, pageable);
    }

    @Override
    public Page<HelpContent> searchContent(String keyword, Pageable pageable) {
        return contentRepository.searchByKeyword(keyword, pageable);
    }

    @Override
    public Page<HelpContent> advancedSearchContent(
            String keyword,
            HelpContent.ContentType type,
            Long categoryId,
            HelpContent.ContentStatus status,
            String author,
            LocalDateTime startDate,
            LocalDateTime endDate,
            List<String> tags,
            Pageable pageable) {
        
        Specification<HelpContent> spec = (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            // 基本条件：未删除
            predicates.add(criteriaBuilder.equal(root.get("deleted"), false));
            
            // 关键词搜索
            if (StringUtils.hasText(keyword)) {
                String likePattern = "%" + keyword.toLowerCase() + "%";
                Predicate titlePredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("title")), likePattern);
                Predicate contentPredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("content")), likePattern);
                Predicate summaryPredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("summary")), likePattern);
                
                predicates.add(criteriaBuilder.or(titlePredicate, contentPredicate, summaryPredicate));
            }
            
            // 类型过滤
            if (type != null) {
                predicates.add(criteriaBuilder.equal(root.get("type"), type));
            }
            
            // 分类过滤
            if (categoryId != null) {
                predicates.add(criteriaBuilder.equal(root.get("categoryId"), categoryId));
            }
            
            // 状态过滤
            if (status != null) {
                predicates.add(criteriaBuilder.equal(root.get("status"), status));
            }
            
            // 作者过滤
            if (StringUtils.hasText(author)) {
                predicates.add(criteriaBuilder.equal(root.get("author"), author));
            }
            
            // 时间范围过滤
            if (startDate != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createdAt"), startDate));
            }
            if (endDate != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createdAt"), endDate));
            }
            
            // 标签过滤
            if (tags != null && !tags.isEmpty()) {
                for (String tag : tags) {
                    predicates.add(criteriaBuilder.like(
                        criteriaBuilder.lower(root.get("tags")), 
                        "%" + tag.toLowerCase() + "%"));
                }
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
        
        return contentRepository.findAll(spec, pageable);
    }

    @Override
    public Page<HelpContent> fullTextSearch(String keyword, Pageable pageable) {
        return contentRepository.fullTextSearch(keyword, pageable);
    }

    @Override
    public List<HelpContent> getPopularContent(int limit) {
        return contentRepository.findPopularContent(limit);
    }

    @Override
    public List<HelpContent> getLatestContent(int limit) {
        return contentRepository.findLatestContent(limit);
    }

    @Override
    public List<HelpContent> getRecommendedContent(int limit) {
        return contentRepository.findRecommendedContent(limit);
    }

    @Override
    public List<HelpContent> getFeaturedContent(int limit) {
        return contentRepository.findFeaturedContent(limit);
    }

    @Override
    public List<HelpContent> getPinnedContent() {
        return contentRepository.findPinnedContent();
    }

    @Override
    public List<HelpContent> getRelatedContent(Long contentId, int limit) {
        return contentRepository.findRelatedContent(contentId, limit);
    }

    @Override
    @Transactional
    public void incrementViewCount(Long id) {
        contentRepository.incrementViewCount(id);
    }

    @Override
    @Transactional
    public void incrementLikeCount(Long id) {
        contentRepository.incrementLikeCount(id);
    }

    @Override
    @Transactional
    public void decrementLikeCount(Long id) {
        contentRepository.decrementLikeCount(id);
    }

    @Override
    @Transactional
    public void publishContent(Long id, String reviewer) {
        log.info("发布帮助内容: {}, 审核者: {}", id, reviewer);
        contentRepository.updateStatus(id, HelpContent.ContentStatus.PUBLISHED);
        contentRepository.updateReviewer(id, reviewer, LocalDateTime.now());
    }

    @Override
    @Transactional
    public void unpublishContent(Long id) {
        log.info("撤回发布帮助内容: {}", id);
        contentRepository.updateStatus(id, HelpContent.ContentStatus.DRAFT);
    }

    @Override
    @Transactional
    public void approveContent(Long id, String reviewer) {
        log.info("审核通过帮助内容: {}, 审核者: {}", id, reviewer);
        contentRepository.updateStatus(id, HelpContent.ContentStatus.PUBLISHED);
        contentRepository.updateReviewer(id, reviewer, LocalDateTime.now());
    }

    @Override
    @Transactional
    public void rejectContent(Long id, String reviewer, String reason) {
        log.info("审核拒绝帮助内容: {}, 审核者: {}, 原因: {}", id, reviewer, reason);
        contentRepository.updateStatus(id, HelpContent.ContentStatus.REJECTED);
        contentRepository.updateReviewer(id, reviewer, LocalDateTime.now());
    }

    @Override
    @Transactional
    public void setFeatured(Long id, boolean featured) {
        log.info("设置帮助内容特色状态: {}, 特色: {}", id, featured);
        contentRepository.updateFeatured(id, featured);
    }

    @Override
    @Transactional
    public void setPinned(Long id, boolean pinned) {
        log.info("设置帮助内容置顶状态: {}, 置顶: {}", id, pinned);
        contentRepository.updatePinned(id, pinned);
    }

    @Override
    @Transactional
    public void batchUpdateStatus(List<Long> ids, HelpContent.ContentStatus status) {
        log.info("批量更新帮助内容状态: {}, 状态: {}", ids, status);
        contentRepository.batchUpdateStatus(ids, status);
    }

    @Override
    @Transactional
    public void addTag(Long contentId, String tagName) {
        log.info("为帮助内容添加标签: {}, 标签: {}", contentId, tagName);
        
        HelpContent content = getContentById(contentId);
        HelpTag tag = tagRepository.findByNameAndDeletedFalse(tagName)
            .orElseGet(() -> {
                HelpTag newTag = new HelpTag();
                newTag.setName(tagName);
                newTag.setType(HelpTag.TagType.CONTENT);
                newTag.setEnabled(true);
                newTag.setCreatedAt(LocalDateTime.now());
                newTag.setUpdatedAt(LocalDateTime.now());
                return tagRepository.save(newTag);
            });
        
        content.addTag(tag);
        contentRepository.save(content);
        
        // 增加标签使用次数
        tagRepository.incrementUsageCount(tag.getId());
    }

    @Override
    @Transactional
    public void removeTag(Long contentId, String tagName) {
        log.info("从帮助内容移除标签: {}, 标签: {}", contentId, tagName);
        
        HelpContent content = getContentById(contentId);
        HelpTag tag = tagRepository.findByNameAndDeletedFalse(tagName)
            .orElseThrow(() -> new RuntimeException("标签不存在: " + tagName));
        
        content.removeTag(tag);
        contentRepository.save(content);
        
        // 减少标签使用次数
        tagRepository.decrementUsageCount(tag.getId());
    }

    @Override
    @Transactional
    public void batchAddTags(Long contentId, List<String> tagNames) {
        log.info("为帮助内容批量添加标签: {}, 标签: {}", contentId, tagNames);
        
        for (String tagName : tagNames) {
            addTag(contentId, tagName);
        }
    }

    @Override
    @Transactional
    public void uploadAttachment(Long contentId, MultipartFile file, String description) {
        log.info("为帮助内容上传附件: {}, 文件: {}", contentId, file.getOriginalFilename());
        
        // TODO: 实现文件上传逻辑
        // 1. 保存文件到存储系统
        // 2. 创建附件记录
        // 3. 关联到内容
        
        throw new UnsupportedOperationException("文件上传功能待实现");
    }

    @Override
    @Transactional
    public void batchUploadAttachments(Long contentId, List<MultipartFile> files, List<String> descriptions) {
        log.info("为帮助内容批量上传附件: {}, 文件数量: {}", contentId, files.size());
        
        for (int i = 0; i < files.size(); i++) {
            String description = i < descriptions.size() ? descriptions.get(i) : "";
            uploadAttachment(contentId, files.get(i), description);
        }
    }

    @Override
    @Transactional
    public void deleteAttachment(Long contentId, Long attachmentId) {
        log.info("删除帮助内容附件: {}, 附件: {}", contentId, attachmentId);
        attachmentRepository.softDelete(attachmentId);
    }

    @Override
    @Transactional
    public HelpContent copyContent(Long id, String newTitle) {
        log.info("复制帮助内容: {}, 新标题: {}", id, newTitle);
        
        HelpContent original = getContentById(id);
        HelpContent copy = new HelpContent();
        
        // 复制基本信息
        copy.setTitle(newTitle);
        copy.setContent(original.getContent());
        copy.setSummary(original.getSummary());
        copy.setType(original.getType());
        copy.setCategoryId(original.getCategoryId());
        copy.setTags(original.getTags());
        copy.setDifficulty(original.getDifficulty());
        copy.setEstimatedReadTime(original.getEstimatedReadTime());
        copy.setKeywords(original.getKeywords());
        copy.setMetaDescription(original.getMetaDescription());
        copy.setAuthor(original.getAuthor());
        copy.setStatus(HelpContent.ContentStatus.DRAFT);
        copy.setVersion("1.0");
        copy.setViewCount(0L);
        copy.setLikeCount(0L);
        copy.setCreatedAt(LocalDateTime.now());
        copy.setUpdatedAt(LocalDateTime.now());
        
        return contentRepository.save(copy);
    }

    @Override
    public byte[] exportContent(Long id, String format) {
        log.info("导出帮助内容: {}, 格式: {}", id, format);
        
        // TODO: 实现内容导出逻辑
        throw new UnsupportedOperationException("内容导出功能待实现");
    }

    @Override
    public byte[] batchExportContent(List<Long> ids, String format) {
        log.info("批量导出帮助内容: {}, 格式: {}", ids, format);
        
        // TODO: 实现批量内容导出逻辑
        throw new UnsupportedOperationException("批量内容导出功能待实现");
    }

    @Override
    @Transactional
    public List<HelpContent> importContent(MultipartFile file) {
        log.info("导入帮助内容: {}", file.getOriginalFilename());
        
        // TODO: 实现内容导入逻辑
        throw new UnsupportedOperationException("内容导入功能待实现");
    }

    @Override
    public Map<String, Object> getContentStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        statistics.put("totalCount", contentRepository.countByDeletedFalse());
        statistics.put("publishedCount", contentRepository.countByStatusAndDeletedFalse(HelpContent.ContentStatus.PUBLISHED));
        statistics.put("draftCount", contentRepository.countByStatusAndDeletedFalse(HelpContent.ContentStatus.DRAFT));
        statistics.put("reviewingCount", contentRepository.countByStatusAndDeletedFalse(HelpContent.ContentStatus.REVIEWING));
        statistics.put("totalViews", contentRepository.getTotalViewCount());
        statistics.put("totalLikes", contentRepository.getTotalLikeCount());
        
        return statistics;
    }

    @Override
    public Map<HelpContent.ContentType, Long> getContentStatisticsByType() {
        List<Object[]> results = contentRepository.countByType();
        return results.stream()
            .collect(Collectors.toMap(
                result -> (HelpContent.ContentType) result[0],
                result -> (Long) result[1]
            ));
    }

    @Override
    public Map<HelpContent.ContentStatus, Long> getContentStatisticsByStatus() {
        List<Object[]> results = contentRepository.countByStatus();
        return results.stream()
            .collect(Collectors.toMap(
                result -> (HelpContent.ContentStatus) result[0],
                result -> (Long) result[1]
            ));
    }

    @Override
    public Map<String, Long> getContentStatisticsByCategory() {
        List<Object[]> results = contentRepository.countByCategory();
        return results.stream()
            .collect(Collectors.toMap(
                result -> String.valueOf(result[0]),
                result -> (Long) result[1]
            ));
    }

    @Override
    public Map<String, Long> getContentStatisticsByAuthor() {
        List<Object[]> results = contentRepository.countByAuthor();
        return results.stream()
            .collect(Collectors.toMap(
                result -> (String) result[0],
                result -> (Long) result[1]
            ));
    }

    @Override
    public Map<String, Long> getContentStatisticsByDate(LocalDateTime startDate, LocalDateTime endDate) {
        List<Object[]> results = contentRepository.countByDateRange(startDate, endDate);
        return results.stream()
            .collect(Collectors.toMap(
                result -> result[0].toString(),
                result -> (Long) result[1]
            ));
    }

    @Override
    @Transactional
    public void cleanupExpiredContent(int days) {
        log.info("清理过期帮助内容，天数: {}", days);
        
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(days);
        List<HelpContent> expiredContent = contentRepository.findExpiredContent(cutoffDate);
        
        for (HelpContent content : expiredContent) {
            contentRepository.softDelete(content.getId());
        }
        
        log.info("清理了 {} 条过期内容", expiredContent.size());
    }

    @Override
    @Transactional
    public void rebuildSearchIndex() {
        log.info("重建搜索索引");
        
        // TODO: 实现搜索索引重建逻辑
        throw new UnsupportedOperationException("搜索索引重建功能待实现");
    }

    @Override
    public List<String> validateContent(HelpContent content) {
        List<String> errors = new ArrayList<>();
        
        if (!StringUtils.hasText(content.getTitle())) {
            errors.add("标题不能为空");
        } else if (content.getTitle().length() > 200) {
            errors.add("标题长度不能超过200个字符");
        }
        
        if (!StringUtils.hasText(content.getContent())) {
            errors.add("内容不能为空");
        }
        
        if (content.getType() == null) {
            errors.add("内容类型不能为空");
        }
        
        if (content.getCategoryId() == null) {
            errors.add("分类不能为空");
        }
        
        // 检查标题重复
        if (StringUtils.hasText(content.getTitle())) {
            boolean titleExists = content.getId() == null 
                ? existsByTitle(content.getTitle())
                : existsByTitleAndIdNot(content.getTitle(), content.getId());
            
            if (titleExists) {
                errors.add("标题已存在");
            }
        }
        
        return errors;
    }

    @Override
    public String previewContent(Long id) {
        HelpContent content = getContentById(id);
        
        // TODO: 实现内容预览逻辑（可能包括Markdown渲染等）
        return content.getContent();
    }

    @Override
    public String generateSummary(String content, int maxLength) {
        if (!StringUtils.hasText(content)) {
            return "";
        }
        
        // 简单的摘要生成逻辑
        String plainText = content.replaceAll("<[^>]*>", ""); // 移除HTML标签
        plainText = plainText.replaceAll("\\s+", " ").trim(); // 规范化空白字符
        
        if (plainText.length() <= maxLength) {
            return plainText;
        }
        
        return plainText.substring(0, maxLength - 3) + "...";
    }

    @Override
    public boolean existsById(Long id) {
        return contentRepository.existsByIdAndDeletedFalse(id);
    }

    @Override
    public boolean existsByTitle(String title) {
        return contentRepository.existsByTitleAndDeletedFalse(title);
    }

    @Override
    public boolean existsByTitleAndIdNot(String title, Long id) {
        return contentRepository.existsByTitleAndIdNotAndDeletedFalse(title, id);
    }

    @Override
    public List<HelpContent> getContentVersionHistory(Long id) {
        // TODO: 实现版本历史功能
        throw new UnsupportedOperationException("版本历史功能待实现");
    }

    @Override
    @Transactional
    public HelpContent createContentVersion(Long id, String versionNote) {
        // TODO: 实现版本创建功能
        throw new UnsupportedOperationException("版本创建功能待实现");
    }

    @Override
    @Transactional
    public HelpContent restoreToVersion(Long id, String version) {
        // TODO: 实现版本恢复功能
        throw new UnsupportedOperationException("版本恢复功能待实现");
    }

    @Override
    public Map<String, Object> compareVersions(Long id, String version1, String version2) {
        // TODO: 实现版本比较功能
        throw new UnsupportedOperationException("版本比较功能待实现");
    }
}