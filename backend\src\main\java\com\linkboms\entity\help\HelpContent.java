package com.linkboms.entity.help;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

/**
 * 帮助内容实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Table(name = "help_content")
@EntityListeners(AuditingEntityListener.class)
@Data
@EqualsAndHashCode(callSuper = false)
public class HelpContent {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "标题不能为空")
    @Size(max = 200, message = "标题长度不能超过200个字符")
    @Column(nullable = false, length = 200)
    private String title;

    @NotBlank(message = "内容不能为空")
    @Column(nullable = false, columnDefinition = "TEXT")
    private String content;

    @Size(max = 500, message = "摘要长度不能超过500个字符")
    @Column(length = 500)
    private String summary;

    @NotNull(message = "内容类型不能为空")
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 50)
    private ContentType type;

    @NotNull(message = "分类ID不能为空")
    @Column(name = "category_id", nullable = false)
    private Long categoryId;

    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;

    @NotNull(message = "状态不能为空")
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private ContentStatus status = ContentStatus.DRAFT;

    @Column(name = "view_count", nullable = false)
    private Long viewCount = 0L;

    @Column(name = "like_count", nullable = false)
    private Long likeCount = 0L;

    @Column(name = "dislike_count", nullable = false)
    private Long dislikeCount = 0L;

    @Size(max = 100, message = "作者名称长度不能超过100个字符")
    @Column(length = 100)
    private String author;

    @Column(name = "author_id")
    private Long authorId;

    @Size(max = 100, message = "审核者名称长度不能超过100个字符")
    @Column(length = 100)
    private String reviewer;

    @Column(name = "reviewer_id")
    private Long reviewerId;

    @Column(name = "reviewed_at")
    private LocalDateTime reviewedAt;

    @Size(max = 20, message = "版本号长度不能超过20个字符")
    @Column(length = 20)
    private String version = "1.0";

    @Column(name = "is_featured", nullable = false)
    private Boolean featured = false;

    @Column(name = "is_published", nullable = false)
    private Boolean published = false;

    @Column(name = "is_deleted", nullable = false)
    private Boolean deleted = false;

    @Column(name = "publish_at")
    private LocalDateTime publishAt;

    @Column(name = "expire_at")
    private LocalDateTime expireAt;

    @Size(max = 1000, message = "SEO关键词长度不能超过1000个字符")
    @Column(length = 1000)
    private String keywords;

    @Size(max = 500, message = "SEO描述长度不能超过500个字符")
    @Column(length = 500)
    private String metaDescription;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // 标签关联
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "help_content_tag",
        joinColumns = @JoinColumn(name = "content_id"),
        inverseJoinColumns = @JoinColumn(name = "tag_id")
    )
    private Set<HelpTag> tags = new HashSet<>();

    // 内容类型枚举
    public enum ContentType {
        FUNCTION_GUIDE("功能指南"),
        ROLE_PERMISSION("角色权限"),
        INDUSTRY_KNOWLEDGE("行业知识"),
        WORKFLOW("工作流程"),
        BUSINESS_PROCESS("业务流程"),
        FAQ("常见问题"),
        TUTORIAL("教程"),
        ANNOUNCEMENT("公告"),
        DIAGRAM("流程图"),
        VIDEO("视频教程");

        private final String description;

        ContentType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 内容状态枚举
    public enum ContentStatus {
        DRAFT("草稿"),
        PENDING_REVIEW("待审核"),
        APPROVED("已审核"),
        PUBLISHED("已发布"),
        ARCHIVED("已归档"),
        REJECTED("已拒绝");

        private final String description;

        ContentStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 添加标签
     */
    public void addTag(HelpTag tag) {
        if (tags == null) {
            tags = new HashSet<>();
        }
        tags.add(tag);
    }

    /**
     * 移除标签
     */
    public void removeTag(HelpTag tag) {
        if (tags != null) {
            tags.remove(tag);
        }
    }

    /**
     * 增加浏览量
     */
    public void incrementViewCount() {
        this.viewCount++;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 增加点赞数
     */
    public void incrementLikeCount() {
        this.likeCount++;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 减少点赞数
     */
    public void decrementLikeCount() {
        if (this.likeCount > 0) {
            this.likeCount--;
            this.updatedAt = LocalDateTime.now();
        }
    }

    /**
     * 增加点踩数
     */
    public void incrementDislikeCount() {
        this.dislikeCount++;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 发布内容
     */
    public void publish() {
        this.status = ContentStatus.PUBLISHED;
        this.published = true;
        this.publishAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 撤回发布
     */
    public void unpublish() {
        this.status = ContentStatus.DRAFT;
        this.published = false;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 设为特色内容
     */
    public void setFeatured(boolean featured) {
        this.featured = featured;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 软删除
     */
    public void softDelete() {
        this.deleted = true;
        this.published = false;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 恢复删除
     */
    public void restore() {
        this.deleted = false;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 审核通过
     */
    public void approve(String reviewer, Long reviewerId) {
        this.status = ContentStatus.APPROVED;
        this.reviewer = reviewer;
        this.reviewerId = reviewerId;
        this.reviewedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 审核拒绝
     */
    public void reject(String reviewer, Long reviewerId) {
        this.status = ContentStatus.REJECTED;
        this.reviewer = reviewer;
        this.reviewerId = reviewerId;
        this.reviewedAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
}
