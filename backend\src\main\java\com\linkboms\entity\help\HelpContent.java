package com.linkboms.entity.help;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.List;
import java.util.HashSet;
import java.util.Set;

/**
 * 帮助内容实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Table(name = "help_content")
@EntityListeners(AuditingEntityListener.class)
public class HelpContent {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "标题不能为空")
    @Size(max = 200, message = "标题长度不能超过200个字符")
    @Column(nullable = false, length = 200)
    private String title;

    @NotBlank(message = "内容不能为空")
    @Column(nullable = false, columnDefinition = "TEXT")
    private String content;

    @Size(max = 500, message = "摘要长度不能超过500个字符")
    @Column(length = 500)
    private String summary;

    @NotNull(message = "内容类型不能为空")
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 50)
    private ContentType type;

    @NotNull(message = "分类ID不能为空")
    @Column(name = "category_id", nullable = false)
    private Long categoryId;

    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;

    @NotNull(message = "状态不能为空")
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private ContentStatus status = ContentStatus.DRAFT;

    @Column(name = "view_count", nullable = false)
    private Long viewCount = 0L;

    @Column(name = "like_count", nullable = false)
    private Long likeCount = 0L;



    @Size(max = 100, message = "作者名称长度不能超过100个字符")
    @Column(length = 100)
    private String author;

    @Column(name = "author_id")
    private Long authorId;

    @Size(max = 100, message = "审核者名称长度不能超过100个字符")
    @Column(name = "reviewer", length = 100)
    private String reviewer;

    @Column(name = "reviewer_id")
    private Long reviewerId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "reviewed_at")
    private LocalDateTime reviewedAt;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "published_at")
    private LocalDateTime publishedAt;

    @Column(name = "version", nullable = false)
    private Integer version = 1;

    @Size(max = 500, message = "版本说明长度不能超过500个字符")
    @Column(name = "version_notes", length = 500)
    private String versionNotes;

    @Column(name = "is_featured", nullable = false)
    private Boolean featured = false;

    @Column(name = "is_pinned", nullable = false)
    private Boolean pinned = false;

    @Column(name = "deleted", nullable = false)
    private Boolean deleted = false;

    @CreatedDate
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // 多对一关系：内容属于某个分类
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id", insertable = false, updatable = false)
    private HelpCategory category;

    // 一对多关系：内容可以有多个附件
    @OneToMany(mappedBy = "content", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<HelpAttachment> attachments = new HashSet<>();

    // 一对多关系：内容可以有多个反馈
    @OneToMany(mappedBy = "contentId", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private Set<HelpFeedback> feedbacks = new HashSet<>();

    // 多对多关系：内容可以有多个标签
    @ManyToMany(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
        name = "help_content_tag",
        joinColumns = @JoinColumn(name = "content_id"),
        inverseJoinColumns = @JoinColumn(name = "tag_id")
    )
    private Set<HelpTag> tags = new HashSet<>();

    // 内容类型枚举
    public enum ContentType {
        FUNCTION_GUIDE("功能指南"),
        ROLE_PERMISSION("角色权限"),
        INDUSTRY_KNOWLEDGE("行业知识"),
        WORKFLOW("工作流程"),
        BUSINESS_PROCESS("业务流程"),
        FAQ("常见问题"),
        TUTORIAL("教程"),
        ANNOUNCEMENT("公告");

        private final String description;

        ContentType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // 内容状态枚举
    public enum ContentStatus {
        DRAFT("草稿"),
        PENDING_REVIEW("待审核"),
        APPROVED("已审核"),
        PUBLISHED("已发布"),
        ARCHIVED("已归档"),
        REJECTED("已拒绝");

        private final String description;

        ContentStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // Constructors
    public HelpContent() {}

    public HelpContent(String title, String content, ContentType type, Long categoryId) {
        this.title = title;
        this.content = content;
        this.type = type;
        this.categoryId = categoryId;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public ContentType getType() {
        return type;
    }

    public void setType(ContentType type) {
        this.type = type;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public ContentStatus getStatus() {
        return status;
    }

    public void setStatus(ContentStatus status) {
        this.status = status;
    }

    public Long getViewCount() {
        return viewCount;
    }

    public void setViewCount(Long viewCount) {
        this.viewCount = viewCount;
    }

    public Long getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(Long likeCount) {
        this.likeCount = likeCount;
    }



    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public Long getAuthorId() {
        return authorId;
    }

    public void setAuthorId(Long authorId) {
        this.authorId = authorId;
    }

    public String getReviewer() {
        return reviewer;
    }

    public void setReviewer(String reviewer) {
        this.reviewer = reviewer;
    }

    public Long getReviewerId() {
        return reviewerId;
    }

    public void setReviewerId(Long reviewerId) {
        this.reviewerId = reviewerId;
    }

    public LocalDateTime getReviewedAt() {
        return reviewedAt;
    }

    public void setReviewedAt(LocalDateTime reviewedAt) {
        this.reviewedAt = reviewedAt;
    }

    public LocalDateTime getPublishedAt() {
        return publishedAt;
    }

    public void setPublishedAt(LocalDateTime publishedAt) {
        this.publishedAt = publishedAt;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getVersionNotes() {
        return versionNotes;
    }

    public void setVersionNotes(String versionNotes) {
        this.versionNotes = versionNotes;
    }

    public Boolean getFeatured() {
        return featured;
    }

    public void setFeatured(Boolean featured) {
        this.featured = featured;
    }

    public Boolean getPinned() {
        return pinned;
    }

    public void setPinned(Boolean pinned) {
        this.pinned = pinned;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public HelpCategory getCategory() {
        return category;
    }

    public void setCategory(HelpCategory category) {
        this.category = category;
    }

    public Set<HelpAttachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(Set<HelpAttachment> attachments) {
        this.attachments = attachments;
    }

    public Set<HelpFeedback> getFeedbacks() {
        return feedbacks;
    }

    public void setFeedbacks(Set<HelpFeedback> feedbacks) {
        this.feedbacks = feedbacks;
    }

    public Set<HelpTag> getTags() {
        return tags;
    }

    public void setTags(Set<HelpTag> tags) {
        this.tags = tags;
    }

    /**
     * 添加标签
     */
    public void addTag(HelpTag tag) {
        tags.add(tag);
        tag.getContents().add(this);
        tag.incrementUsage();
    }

    /**
     * 移除标签
     */
    public void removeTag(HelpTag tag) {
        tags.remove(tag);
        tag.getContents().remove(this);
        tag.decrementUsage();
    }

    /**
     * 添加附件
     */
    public void addAttachment(HelpAttachment attachment) {
        attachments.add(attachment);
        attachment.setContent(this);
    }

    /**
     * 移除附件
     */
    public void removeAttachment(HelpAttachment attachment) {
        attachments.remove(attachment);
        attachment.setContent(null);
    }

    /**
     * 获取平均评分
     */
    public Double getAverageRating() {
        if (feedbacks == null || feedbacks.isEmpty()) {
            return null;
        }
        
        return feedbacks.stream()
            .filter(feedback -> feedback.getRating() != null)
            .mapToInt(HelpFeedback::getRating)
            .average()
            .orElse(0.0);
    }

    /**
     * 获取反馈总数
     */
    public int getFeedbackCount() {
        return feedbacks != null ? feedbacks.size() : 0;
    }

    /**
     * 获取附件总数
     */
    public int getAttachmentCount() {
        return attachments != null ? attachments.size() : 0;
    }

    @Override
    public String toString() {
        return "HelpContent{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", type=" + type +
                ", status=" + status +
                ", viewCount=" + viewCount +
                ", createdAt=" + createdAt +
                '}';
    }
}