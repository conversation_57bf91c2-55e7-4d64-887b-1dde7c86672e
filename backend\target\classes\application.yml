# Link-BOM-S 系统配置文件
spring:
  application:
    name: link-bom-s-backend
  
  # 数据库配置
  datasource:
    url: *******************************************
    username: ${DB_USERNAME:linkboms}
    password: ${DB_PASSWORD:linkboms123}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      max-lifetime: 1200000
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
    open-in-view: false
  
  # Jackson配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # 缓存配置
  cache:
    type: simple
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB
  
  # 安全配置
  security:
    user:
      name: admin
      password: admin123
      roles: ADMIN

# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
    min-response-size: 1024

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.linkboms.entity

# 日志配置
logging:
  level:
    com.linkboms: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/link-bom-s.log
    max-size: 100MB
    max-history: 30

# 应用自定义配置
app:
  # JWT配置
  jwt:
    secret: ${JWT_SECRET:linkboms-jwt-secret-key-2024}
    expiration: 86400000 # 24小时
    refresh-expiration: 604800000 # 7天
  
  # 文件存储配置
  file:
    upload-path: ${FILE_UPLOAD_PATH:./uploads}
    max-size: 10485760 # 10MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar
  
  # 帮助手册配置
  help:
    content-path: ${HELP_CONTENT_PATH:./help-content}
    cache-enabled: true
    cache-ttl: 3600 # 1小时
    search-index-enabled: true
    max-search-results: 50
  
  # 跨域配置
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:3001}
    allowed-methods: GET,POST,PUT,DELETE,OPTIONS
    allowed-headers: "*"
    allow-credentials: true
    max-age: 3600

# API文档配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    operations-sorter: method
    tags-sorter: alpha
  info:
    title: Link-BOM-S API
    description: Link-BOM-S 系统 REST API 文档
    version: 1.0.0
    contact:
      name: Link-BOM-S Team
      email: <EMAIL>
  
# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  info:
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: update

logging:
  level:
    com.linkboms: DEBUG
    org.springframework.security: DEBUG

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: validate

logging:
  level:
    com.linkboms: INFO
    org.springframework.security: WARN
    root: WARN

server:
  port: 8080

app:
  jwt:
    secret: ${JWT_SECRET}
  cors:
    allowed-origins: ${CORS_ALLOWED_ORIGINS}