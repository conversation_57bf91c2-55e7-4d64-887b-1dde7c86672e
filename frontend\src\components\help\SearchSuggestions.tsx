import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Typography,
  Space,
  Tag,
  Button,
  Avatar,
  Divider,
  Empty,
  Spin
} from 'antd';
import {
  SearchOutlined,
  FireOutlined,
  ClockCircleOutlined,
  TagOutlined,
  UserOutlined,
  TrophyOutlined,
  BulbOutlined
} from '@ant-design/icons';
import { HelpContent, HelpTag } from '../../types/help';
import { useAppSelector } from '../../hooks/redux';

const { Title, Text } = Typography;

interface SearchSuggestion {
  id: string;
  type: 'content' | 'tag' | 'category' | 'keyword';
  title: string;
  description?: string;
  icon?: React.ReactNode;
  count?: number;
  relevance?: number;
}

interface SearchSuggestionsProps {
  keyword?: string;
  onSuggestionClick: (suggestion: SearchSuggestion) => void;
  onKeywordClick: (keyword: string) => void;
}

/**
 * 搜索建议组件
 */
const SearchSuggestions: React.FC<SearchSuggestionsProps> = ({
  keyword,
  onSuggestionClick,
  onKeywordClick
}) => {
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [loading, setLoading] = useState(false);
  const [hotKeywords, setHotKeywords] = useState<string[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([]);

  const { popularTags, featuredContent, latestContent } = useAppSelector(state => state.help);

  useEffect(() => {
    // 加载热门关键词
    setHotKeywords([
      'BOM创建',
      '物料管理',
      '库存盘点',
      '采购流程',
      '成本分析',
      '用户权限',
      '角色管理',
      '系统设置',
      '数据导入',
      '报表生成'
    ]);

    // 加载最近搜索
    const recent = localStorage.getItem('recent_searches');
    if (recent) {
      setRecentSearches(JSON.parse(recent));
    }
  }, []);

  useEffect(() => {
    if (keyword && keyword.length >= 2) {
      generateSuggestions(keyword);
    } else {
      setSuggestions([]);
    }
  }, [keyword, popularTags, featuredContent]);

  // 生成搜索建议
  const generateSuggestions = async (searchKeyword: string) => {
    setLoading(true);
    
    try {
      const suggestions: SearchSuggestion[] = [];
      const lowerKeyword = searchKeyword.toLowerCase();

      // 内容建议
      if (featuredContent) {
        const contentSuggestions = featuredContent
          .filter(content => 
            content.title.toLowerCase().includes(lowerKeyword) ||
            content.summary?.toLowerCase().includes(lowerKeyword)
          )
          .slice(0, 3)
          .map(content => ({
            id: `content-${content.id}`,
            type: 'content' as const,
            title: content.title,
            description: content.summary,
            icon: <SearchOutlined />,
            relevance: calculateRelevance(content.title, searchKeyword)
          }));
        
        suggestions.push(...contentSuggestions);
      }

      // 标签建议
      const tagSuggestions = popularTags
        .filter(tag => tag.name.toLowerCase().includes(lowerKeyword))
        .slice(0, 3)
        .map(tag => ({
          id: `tag-${tag.id}`,
          type: 'tag' as const,
          title: tag.name,
          description: tag.description,
          icon: <TagOutlined />,
          count: tag.usageCount,
          relevance: calculateRelevance(tag.name, searchKeyword)
        }));
      
      suggestions.push(...tagSuggestions);

      // 关键词建议
      const keywordSuggestions = hotKeywords
        .filter(kw => kw.toLowerCase().includes(lowerKeyword))
        .slice(0, 3)
        .map(kw => ({
          id: `keyword-${kw}`,
          type: 'keyword' as const,
          title: kw,
          icon: <BulbOutlined />,
          relevance: calculateRelevance(kw, searchKeyword)
        }));
      
      suggestions.push(...keywordSuggestions);

      // 按相关性排序
      suggestions.sort((a, b) => (b.relevance || 0) - (a.relevance || 0));

      setSuggestions(suggestions.slice(0, 8));
    } catch (error) {
      console.error('生成搜索建议失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 计算相关性得分
  const calculateRelevance = (text: string, keyword: string): number => {
    const lowerText = text.toLowerCase();
    const lowerKeyword = keyword.toLowerCase();
    
    if (lowerText === lowerKeyword) return 100;
    if (lowerText.startsWith(lowerKeyword)) return 90;
    if (lowerText.includes(lowerKeyword)) return 70;
    
    // 模糊匹配
    const words = lowerKeyword.split(' ');
    let score = 0;
    words.forEach(word => {
      if (lowerText.includes(word)) {
        score += 30;
      }
    });
    
    return score;
  };

  // 处理建议点击
  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    if (suggestion.type === 'keyword') {
      onKeywordClick(suggestion.title);
    } else {
      onSuggestionClick(suggestion);
    }
    
    // 保存到最近搜索
    saveToRecentSearches(suggestion.title);
  };

  // 保存到最近搜索
  const saveToRecentSearches = (searchTerm: string) => {
    const newRecent = [searchTerm, ...recentSearches.filter(item => item !== searchTerm)].slice(0, 10);
    setRecentSearches(newRecent);
    localStorage.setItem('recent_searches', JSON.stringify(newRecent));
  };

  // 获取建议图标
  const getSuggestionIcon = (suggestion: SearchSuggestion) => {
    switch (suggestion.type) {
      case 'content':
        return <SearchOutlined style={{ color: '#1890ff' }} />;
      case 'tag':
        return <TagOutlined style={{ color: '#52c41a' }} />;
      case 'category':
        return <UserOutlined style={{ color: '#722ed1' }} />;
      case 'keyword':
        return <BulbOutlined style={{ color: '#fa8c16' }} />;
      default:
        return <SearchOutlined />;
    }
  };

  // 获取类型标签
  const getTypeTag = (type: string) => {
    const typeMap = {
      content: { label: '内容', color: 'blue' },
      tag: { label: '标签', color: 'green' },
      category: { label: '分类', color: 'purple' },
      keyword: { label: '关键词', color: 'orange' }
    };
    
    const config = typeMap[type as keyof typeof typeMap];
    return config ? <Tag color={config.color} size="small">{config.label}</Tag> : null;
  };

  if (keyword && keyword.length >= 2) {
    return (
      <Card size="small" style={{ marginTop: '8px' }}>
        <Spin spinning={loading}>
          {suggestions.length > 0 ? (
            <List
              size="small"
              dataSource={suggestions}
              renderItem={(suggestion) => (
                <List.Item
                  style={{ cursor: 'pointer', padding: '8px 0' }}
                  onClick={() => handleSuggestionClick(suggestion)}
                >
                  <List.Item.Meta
                    avatar={<Avatar size="small" icon={getSuggestionIcon(suggestion)} />}
                    title={
                      <Space>
                        <Text ellipsis style={{ maxWidth: '200px' }}>
                          {suggestion.title}
                        </Text>
                        {getTypeTag(suggestion.type)}
                        {suggestion.count && (
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            ({suggestion.count})
                          </Text>
                        )}
                      </Space>
                    }
                    description={
                      suggestion.description && (
                        <Text type="secondary" ellipsis style={{ fontSize: '12px' }}>
                          {suggestion.description}
                        </Text>
                      )
                    }
                  />
                </List.Item>
              )}
            />
          ) : (
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="没有找到相关建议"
              style={{ margin: '16px 0' }}
            />
          )}
        </Spin>
      </Card>
    );
  }

  // 默认显示热门搜索和最近搜索
  return (
    <Card size="small" style={{ marginTop: '8px' }}>
      {/* 热门搜索 */}
      <div style={{ marginBottom: '16px' }}>
        <Title level={5} style={{ marginBottom: '8px' }}>
          <FireOutlined style={{ color: '#fa541c', marginRight: '4px' }} />
          热门搜索
        </Title>
        <Space wrap>
          {hotKeywords.slice(0, 8).map((keyword, index) => (
            <Tag
              key={keyword}
              style={{ cursor: 'pointer' }}
              color={index < 3 ? 'red' : 'default'}
              onClick={() => onKeywordClick(keyword)}
            >
              {index < 3 && <TrophyOutlined style={{ marginRight: '4px' }} />}
              {keyword}
            </Tag>
          ))}
        </Space>
      </div>

      {/* 最近搜索 */}
      {recentSearches.length > 0 && (
        <>
          <Divider style={{ margin: '12px 0' }} />
          <div>
            <Title level={5} style={{ marginBottom: '8px' }}>
              <ClockCircleOutlined style={{ color: '#1890ff', marginRight: '4px' }} />
              最近搜索
            </Title>
            <Space wrap>
              {recentSearches.slice(0, 6).map(search => (
                <Tag
                  key={search}
                  style={{ cursor: 'pointer' }}
                  onClick={() => onKeywordClick(search)}
                >
                  {search}
                </Tag>
              ))}
            </Space>
          </div>
        </>
      )}

      {/* 搜索提示 */}
      <Divider style={{ margin: '12px 0' }} />
      <div>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          💡 搜索提示：支持多关键词搜索，使用空格分隔；支持标签搜索，格式为 tag:标签名
        </Text>
      </div>
    </Card>
  );
};

export default SearchSuggestions;
