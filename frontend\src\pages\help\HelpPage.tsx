import React, { useEffect, useState } from 'react';
import { Layout, Spin, message, Grid } from 'antd';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import {
  fetchCategoryTree,
  fetchHelpNavigation,
  fetchHelpStatistics,
  fetchContentById,
  fetchContentByCategory,
  searchContent,
  setCurrentView,
  setSelectedCategory,
  clearError,
  incrementViewCount
} from '../../store/slices/helpSlice';

// 导入子组件
import HelpSidebar from '../../components/help/HelpSidebar';
import HelpHeader from '../../components/help/HelpHeader';
import HelpHome from '../../components/help/HelpHome';
import HelpContentViewer from '../../components/help/HelpContentViewer';
import HelpCategoryView from '../../components/help/HelpCategoryView';
import HelpSearchResults from '../../components/help/HelpSearchResults';
import HelpMobileNavigation from '../../components/help/HelpMobileNavigation';

const { Header, Sider, Content } = Layout;
const { useBreakpoint } = Grid;

/**
 * 帮助手册主页面
 */
const HelpPage: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  const screens = useBreakpoint();

  const {
    categoryTree,
    currentContent,
    currentCategory,
    searchResults,
    navigation,
    statistics,
    loading,
    error,
    ui
  } = useAppSelector(state => state.help);

  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);

  // 判断是否为移动端
  const isMobile = !screens.md;

  useEffect(() => {
    // 初始化加载数据
    dispatch(fetchCategoryTree());
    dispatch(fetchHelpNavigation());
    dispatch(fetchHelpStatistics());
  }, [dispatch]);

  useEffect(() => {
    // 根据路由参数确定当前视图
    const path = location.pathname;
    const searchParams = new URLSearchParams(location.search);

    if (path.includes('/help/content/')) {
      const contentId = params.contentId;
      if (contentId) {
        dispatch(setCurrentView('content'));
        dispatch(fetchContentById(Number(contentId)));
        // 增加浏览量
        dispatch(incrementViewCount(Number(contentId)));
      }
    } else if (path.includes('/help/category/')) {
      const categoryId = params.categoryId;
      if (categoryId) {
        dispatch(setCurrentView('category'));
        dispatch(setSelectedCategory(Number(categoryId)));
        dispatch(fetchContentByCategory({ categoryId: Number(categoryId) }));
      }
    } else if (path.includes('/help/search')) {
      const keyword = searchParams.get('q');
      const type = searchParams.get('type');
      const categoryId = searchParams.get('category');
      
      dispatch(setCurrentView('search'));
      dispatch(searchContent({
        keyword: keyword || '',
        type: type as any,
        categoryId: categoryId ? Number(categoryId) : undefined
      }));
    } else {
      dispatch(setCurrentView('home'));
    }
  }, [location, params, dispatch]);

  useEffect(() => {
    // 错误处理
    if (error) {
      message.error(error);
      dispatch(clearError());
    }
  }, [error, dispatch]);

  useEffect(() => {
    // 移动端自动收起侧边栏
    if (isMobile) {
      setSidebarCollapsed(true);
    }
  }, [isMobile]);

  // 处理侧边栏折叠
  const handleSidebarCollapse = (collapsed: boolean) => {
    setSidebarCollapsed(collapsed);
  };

  // 处理移动端菜单
  const handleMobileMenuToggle = () => {
    setMobileMenuVisible(!mobileMenuVisible);
  };

  // 处理分类选择
  const handleCategorySelect = (categoryId: number) => {
    navigate(`/help/category/${categoryId}`);
    if (isMobile) {
      setMobileMenuVisible(false);
    }
  };

  // 处理内容选择
  const handleContentSelect = (contentId: number) => {
    navigate(`/help/content/${contentId}`);
    if (isMobile) {
      setMobileMenuVisible(false);
    }
  };

  // 处理搜索
  const handleSearch = (keyword: string, filters?: any) => {
    const searchParams = new URLSearchParams();
    searchParams.set('q', keyword);
    
    if (filters?.type) {
      searchParams.set('type', filters.type);
    }
    if (filters?.categoryId) {
      searchParams.set('category', filters.categoryId.toString());
    }
    
    navigate(`/help/search?${searchParams.toString()}`);
  };

  // 处理返回首页
  const handleGoHome = () => {
    navigate('/help');
  };

  // 渲染主要内容
  const renderMainContent = () => {
    if (loading.content || loading.navigation) {
      return (
        <div style={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center', 
          height: '400px' 
        }}>
          <Spin size="large" />
        </div>
      );
    }

    switch (ui.currentView) {
      case 'content':
        return currentContent ? (
          <HelpContentViewer
            content={currentContent}
            onCategorySelect={handleCategorySelect}
            onContentSelect={handleContentSelect}
          />
        ) : null;

      case 'category':
        return (
          <HelpCategoryView
            category={currentCategory}
            onContentSelect={handleContentSelect}
            onCategorySelect={handleCategorySelect}
          />
        );

      case 'search':
        return searchResults ? (
          <HelpSearchResults
            results={searchResults}
            onContentSelect={handleContentSelect}
            onCategorySelect={handleCategorySelect}
            onSearch={handleSearch}
          />
        ) : null;

      case 'home':
      default:
        return (
          <HelpHome
            navigation={navigation}
            statistics={statistics}
            onCategorySelect={handleCategorySelect}
            onContentSelect={handleContentSelect}
            onSearch={handleSearch}
          />
        );
    }
  };

  return (
    <Layout style={{ minHeight: '100vh', background: '#f5f5f5' }}>
      {/* 移动端导航 */}
      {isMobile && (
        <HelpMobileNavigation
          visible={mobileMenuVisible}
          categories={categoryTree}
          onCategorySelect={handleCategorySelect}
          onClose={() => setMobileMenuVisible(false)}
        />
      )}

      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <Sider
          width={280}
          collapsed={sidebarCollapsed}
          onCollapse={handleSidebarCollapse}
          collapsible
          style={{
            background: '#fff',
            borderRight: '1px solid #f0f0f0',
            height: '100vh',
            position: 'fixed',
            left: 0,
            top: 0,
            zIndex: 100,
          }}
        >
          <HelpSidebar
            categories={categoryTree}
            popularTags={navigation?.popularTags || []}
            collapsed={sidebarCollapsed}
            selectedCategoryId={ui.selectedCategoryId}
            onCategorySelect={handleCategorySelect}
            onSearch={handleSearch}
          />
        </Sider>
      )}

      <Layout
        style={{
          marginLeft: isMobile ? 0 : (sidebarCollapsed ? 80 : 280),
          transition: 'margin-left 0.2s',
        }}
      >
        {/* 头部 */}
        <Header
          style={{
            background: '#fff',
            padding: '0 24px',
            borderBottom: '1px solid #f0f0f0',
            position: 'sticky',
            top: 0,
            zIndex: 99,
          }}
        >
          <HelpHeader
            currentView={ui.currentView}
            currentContent={currentContent}
            currentCategory={currentCategory}
            isMobile={isMobile}
            onSearch={handleSearch}
            onGoHome={handleGoHome}
            onMobileMenuToggle={handleMobileMenuToggle}
          />
        </Header>

        {/* 主要内容区域 */}
        <Content
          style={{
            padding: isMobile ? '16px' : '24px',
            background: '#f5f5f5',
            minHeight: 'calc(100vh - 64px)',
          }}
        >
          <div
            style={{
              background: '#fff',
              borderRadius: '8px',
              padding: isMobile ? '16px' : '24px',
              minHeight: 'calc(100vh - 112px)',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            }}
          >
            {renderMainContent()}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default HelpPage;
