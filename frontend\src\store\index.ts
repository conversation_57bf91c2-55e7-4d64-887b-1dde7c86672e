import { configureStore } from '@reduxjs/toolkit';
import authSlice from './slices/authSlice';
import bomSlice from './slices/bomSlice';
import materialSlice from './slices/materialSlice';
import inventorySlice from './slices/inventorySlice';
import purchaseSlice from './slices/purchaseSlice';
import costSlice from './slices/costSlice';
import serviceSlice from './slices/serviceSlice';
import ecnSlice from './slices/ecnSlice';
import dashboardSlice from './slices/dashboardSlice';
import systemSlice from './slices/systemSlice';
import reportSlice from './slices/reportSlice';
import mobileSlice from './slices/mobileSlice';
import helpSlice from './slices/helpSlice';


export const store = configureStore({
  reducer: {
    auth: authSlice,
    bom: bomSlice,
    material: materialSlice,
    inventory: inventorySlice,
    purchase: purchaseSlice,
    cost: costSlice,
    service: serviceSlice,
    ecn: ecnSlice,
    dashboard: dashboardSlice,
    system: systemSlice,
    reports: reportSlice,
    mobile: mobileSlice,
    help: helpSlice,

  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
