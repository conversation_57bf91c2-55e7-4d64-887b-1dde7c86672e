import axios from 'axios';
import { API_BASE_URL } from '../constants';
import {
  HelpCategory,
  HelpContent,
  HelpTag,
  HelpAttachment,
  HelpFeedback,
  HelpNavigation,
  HelpStatistics,
  SearchParams,
  SearchResult,
  CategoryForm,
  ContentForm,
  FeedbackForm,
  ApiResponse,
  PageResponse,
  ContentType,
  ContentStatus,
  FeedbackType,
  FeedbackStatus
} from '../types/help';

// 创建 axios 实例
const api = axios.create({
  baseURL: `${API_BASE_URL}/help`,
  timeout: 10000,
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

class HelpService {
  // ==================== 分类管理 ====================
  
  /**
   * 获取分类树
   */
  async getCategoryTree(): Promise<HelpCategory[]> {
    return api.get('/categories/tree');
  }

  /**
   * 获取根分类
   */
  async getRootCategories(): Promise<HelpCategory[]> {
    return api.get('/categories/roots');
  }

  /**
   * 获取子分类
   */
  async getChildCategories(parentId: number): Promise<HelpCategory[]> {
    return api.get(`/categories/${parentId}/children`);
  }

  /**
   * 获取分类详情
   */
  async getCategoryById(id: number): Promise<HelpCategory> {
    return api.get(`/categories/${id}`);
  }

  /**
   * 创建分类
   */
  async createCategory(category: CategoryForm): Promise<HelpCategory> {
    return api.post('/categories', category);
  }

  /**
   * 更新分类
   */
  async updateCategory(id: number, category: CategoryForm): Promise<HelpCategory> {
    return api.put(`/categories/${id}`, category);
  }

  /**
   * 删除分类
   */
  async deleteCategory(id: number): Promise<void> {
    return api.delete(`/categories/${id}`);
  }

  /**
   * 搜索分类
   */
  async searchCategories(keyword: string): Promise<HelpCategory[]> {
    return api.get('/categories/search', { params: { keyword } });
  }

  /**
   * 获取热门分类
   */
  async getPopularCategories(limit: number = 10): Promise<HelpCategory[]> {
    return api.get('/categories/popular', { params: { limit } });
  }

  // ==================== 内容管理 ====================

  /**
   * 获取内容列表
   */
  async getContentList(params: SearchParams): Promise<PageResponse<HelpContent>> {
    return api.get('/content', { params });
  }

  /**
   * 根据分类获取内容
   */
  async getContentByCategory(categoryId: number, page: number = 0, size: number = 20): Promise<PageResponse<HelpContent>> {
    return api.get('/content/category', { 
      params: { categoryId, page, size } 
    });
  }

  /**
   * 根据类型获取内容
   */
  async getContentByType(type: ContentType, page: number = 0, size: number = 20): Promise<PageResponse<HelpContent>> {
    return api.get('/content/type', { 
      params: { type, page, size } 
    });
  }

  /**
   * 获取内容详情
   */
  async getContentById(id: number): Promise<HelpContent> {
    return api.get(`/content/${id}`);
  }

  /**
   * 创建内容
   */
  async createContent(content: ContentForm): Promise<HelpContent> {
    return api.post('/content', content);
  }

  /**
   * 更新内容
   */
  async updateContent(id: number, content: ContentForm): Promise<HelpContent> {
    return api.put(`/content/${id}`, content);
  }

  /**
   * 删除内容
   */
  async deleteContent(id: number): Promise<void> {
    return api.delete(`/content/${id}`);
  }

  /**
   * 发布内容
   */
  async publishContent(id: number): Promise<void> {
    return api.put(`/content/${id}/publish`);
  }

  /**
   * 撤回发布
   */
  async unpublishContent(id: number): Promise<void> {
    return api.put(`/content/${id}/unpublish`);
  }

  /**
   * 设置特色内容
   */
  async setFeaturedContent(id: number, featured: boolean): Promise<void> {
    return api.put(`/content/${id}/featured`, { featured });
  }

  /**
   * 增加浏览量
   */
  async incrementViewCount(id: number): Promise<void> {
    return api.put(`/content/${id}/view`);
  }

  /**
   * 点赞内容
   */
  async likeContent(id: number): Promise<void> {
    return api.put(`/content/${id}/like`);
  }

  /**
   * 取消点赞
   */
  async unlikeContent(id: number): Promise<void> {
    return api.put(`/content/${id}/unlike`);
  }

  /**
   * 搜索内容
   */
  async searchContent(params: SearchParams): Promise<SearchResult> {
    return api.get('/content/search', { params });
  }

  /**
   * 获取相关内容
   */
  async getRelatedContent(contentId: number, limit: number = 5): Promise<HelpContent[]> {
    return api.get(`/content/${contentId}/related`, { params: { limit } });
  }

  /**
   * 获取特色内容
   */
  async getFeaturedContent(page: number = 0, size: number = 10): Promise<PageResponse<HelpContent>> {
    return api.get('/content/featured', { params: { page, size } });
  }

  /**
   * 获取最新内容
   */
  async getLatestContent(page: number = 0, size: number = 10): Promise<PageResponse<HelpContent>> {
    return api.get('/content/latest', { params: { page, size } });
  }

  /**
   * 获取热门内容
   */
  async getPopularContent(page: number = 0, size: number = 10): Promise<PageResponse<HelpContent>> {
    return api.get('/content/popular', { params: { page, size } });
  }

  // ==================== 标签管理 ====================

  /**
   * 获取所有标签
   */
  async getAllTags(): Promise<HelpTag[]> {
    return api.get('/tags');
  }

  /**
   * 获取热门标签
   */
  async getPopularTags(limit: number = 20): Promise<HelpTag[]> {
    return api.get('/tags/popular', { params: { limit } });
  }

  /**
   * 搜索标签
   */
  async searchTags(keyword: string): Promise<HelpTag[]> {
    return api.get('/tags/search', { params: { keyword } });
  }

  /**
   * 创建标签
   */
  async createTag(tag: Partial<HelpTag>): Promise<HelpTag> {
    return api.post('/tags', tag);
  }

  /**
   * 更新标签
   */
  async updateTag(id: number, tag: Partial<HelpTag>): Promise<HelpTag> {
    return api.put(`/tags/${id}`, tag);
  }

  /**
   * 删除标签
   */
  async deleteTag(id: number): Promise<void> {
    return api.delete(`/tags/${id}`);
  }

  // ==================== 附件管理 ====================

  /**
   * 上传附件
   */
  async uploadAttachment(contentId: number, file: File, description?: string): Promise<HelpAttachment> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('contentId', contentId.toString());
    if (description) {
      formData.append('description', description);
    }

    return api.post('/attachments/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }

  /**
   * 获取内容的附件列表
   */
  async getContentAttachments(contentId: number): Promise<HelpAttachment[]> {
    return api.get(`/attachments/content/${contentId}`);
  }

  /**
   * 下载附件
   */
  async downloadAttachment(id: number): Promise<Blob> {
    return api.get(`/attachments/${id}/download`, {
      responseType: 'blob',
    });
  }

  /**
   * 删除附件
   */
  async deleteAttachment(id: number): Promise<void> {
    return api.delete(`/attachments/${id}`);
  }

  // ==================== 反馈管理 ====================

  /**
   * 提交反馈
   */
  async submitFeedback(feedback: FeedbackForm): Promise<HelpFeedback> {
    return api.post('/feedback', feedback);
  }

  /**
   * 获取内容的反馈列表
   */
  async getContentFeedback(contentId: number, page: number = 0, size: number = 20): Promise<PageResponse<HelpFeedback>> {
    return api.get(`/feedback/content/${contentId}`, { 
      params: { page, size } 
    });
  }

  /**
   * 回复反馈
   */
  async replyFeedback(id: number, reply: string): Promise<void> {
    return api.put(`/feedback/${id}/reply`, { reply });
  }

  /**
   * 更新反馈状态
   */
  async updateFeedbackStatus(id: number, status: FeedbackStatus): Promise<void> {
    return api.put(`/feedback/${id}/status`, { status });
  }

  /**
   * 删除反馈
   */
  async deleteFeedback(id: number): Promise<void> {
    return api.delete(`/feedback/${id}`);
  }

  // ==================== 统计和分析 ====================

  /**
   * 获取帮助统计信息
   */
  async getHelpStatistics(): Promise<HelpStatistics> {
    return api.get('/statistics');
  }

  /**
   * 获取分类统计
   */
  async getCategoryStatistics(): Promise<any> {
    return api.get('/categories/statistics');
  }

  /**
   * 获取内容统计
   */
  async getContentStatistics(): Promise<any> {
    return api.get('/content/statistics');
  }

  /**
   * 获取反馈统计
   */
  async getFeedbackStatistics(): Promise<any> {
    return api.get('/feedback/statistics');
  }

  // ==================== 导航和配置 ====================

  /**
   * 获取帮助导航
   */
  async getHelpNavigation(): Promise<HelpNavigation> {
    const [categories, popularTags, featuredContent] = await Promise.all([
      this.getCategoryTree(),
      this.getPopularTags(10),
      this.getFeaturedContent(0, 5).then(result => result.content)
    ]);

    return {
      categories,
      popularTags,
      featuredContent,
      quickLinks: [] // 可以从配置中获取
    };
  }

  // ==================== 工具方法 ====================

  /**
   * 格式化文件大小
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 获取内容类型显示名称
   */
  getContentTypeLabel(type: ContentType): string {
    const labels = {
      [ContentType.FUNCTION_GUIDE]: '功能指南',
      [ContentType.ROLE_PERMISSION]: '角色权限',
      [ContentType.INDUSTRY_KNOWLEDGE]: '行业知识',
      [ContentType.WORKFLOW]: '工作流程',
      [ContentType.BUSINESS_PROCESS]: '业务流程',
      [ContentType.FAQ]: '常见问题',
      [ContentType.TUTORIAL]: '教程',
      [ContentType.ANNOUNCEMENT]: '公告',
      [ContentType.DIAGRAM]: '流程图',
      [ContentType.VIDEO]: '视频教程'
    };
    return labels[type] || type;
  }

  /**
   * 获取内容状态显示名称
   */
  getContentStatusLabel(status: ContentStatus): string {
    const labels = {
      [ContentStatus.DRAFT]: '草稿',
      [ContentStatus.PENDING_REVIEW]: '待审核',
      [ContentStatus.APPROVED]: '已审核',
      [ContentStatus.PUBLISHED]: '已发布',
      [ContentStatus.ARCHIVED]: '已归档',
      [ContentStatus.REJECTED]: '已拒绝'
    };
    return labels[status] || status;
  }

  /**
   * 获取反馈类型显示名称
   */
  getFeedbackTypeLabel(type: FeedbackType): string {
    const labels = {
      [FeedbackType.LIKE]: '点赞',
      [FeedbackType.DISLIKE]: '点踩',
      [FeedbackType.COMMENT]: '评论',
      [FeedbackType.SUGGESTION]: '建议',
      [FeedbackType.BUG_REPORT]: '错误报告',
      [FeedbackType.CONTENT_ERROR]: '内容错误',
      [FeedbackType.IMPROVEMENT]: '改进建议'
    };
    return labels[type] || type;
  }
}

export const helpService = new HelpService();
export default helpService;
