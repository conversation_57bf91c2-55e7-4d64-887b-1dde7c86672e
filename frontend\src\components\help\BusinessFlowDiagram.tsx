import React, { useState } from 'react';
import { Card, Tabs, Typography, Space, Tag, Button, Modal, List, Avatar } from 'antd';
import {
  ApartmentOutlined,
  EyeOutlined,
  DownloadOutlined,
  InfoCircleOutlined,
  PlayCircleOutlined
} from '@ant-design/icons';
import MermaidDiagram from './MermaidDiagram';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

interface FlowStep {
  id: string;
  title: string;
  description: string;
  responsible: string;
  duration: string;
  inputs: string[];
  outputs: string[];
}

interface BusinessFlow {
  id: string;
  title: string;
  description: string;
  category: string;
  diagram: string;
  steps: FlowStep[];
}

/**
 * 业务流程图组件
 */
const BusinessFlowDiagram: React.FC = () => {
  const [selectedFlow, setSelectedFlow] = useState<string>('bom-process');
  const [showStepsModal, setShowStepsModal] = useState(false);

  // 业务流程数据
  const businessFlows: BusinessFlow[] = [
    {
      id: 'bom-process',
      title: 'BOM创建与管理流程',
      description: '从产品设计到BOM发布的完整业务流程，包括设计、审核、发布等关键环节。',
      category: 'BOM管理',
      diagram: `
        graph TD
          A[产品需求分析] --> B[产品结构设计]
          B --> C[物料选型]
          C --> D[BOM结构创建]
          D --> E[工艺路线设计]
          E --> F[成本预算]
          F --> G{设计评审}
          G -->|通过| H[BOM数据录入]
          G -->|不通过| I[设计修改]
          I --> B
          H --> J[BOM验证]
          J --> K{数据验证}
          K -->|通过| L[提交审核]
          K -->|不通过| M[数据修正]
          M --> H
          L --> N[技术审核]
          N --> O{技术审核}
          O -->|通过| P[工艺审核]
          O -->|不通过| Q[技术修改]
          Q --> L
          P --> R{工艺审核}
          R -->|通过| S[最终审批]
          R -->|不通过| T[工艺修改]
          T --> L
          S --> U{最终审批}
          U -->|通过| V[BOM发布]
          U -->|不通过| W[重新设计]
          W --> B
          V --> X[生产使用]
          V --> Y[版本管理]
          
          style A fill:#e1f5fe
          style X fill:#e8f5e8
          style Y fill:#e8f5e8
          style G fill:#fff3e0
          style K fill:#fff3e0
          style O fill:#fff3e0
          style R fill:#fff3e0
          style U fill:#fff3e0
      `,
      steps: [
        {
          id: 'step1',
          title: '产品需求分析',
          description: '分析客户需求，确定产品功能和技术规格',
          responsible: '产品经理',
          duration: '2-3天',
          inputs: ['客户需求文档', '市场调研报告'],
          outputs: ['产品需求规格书', '技术要求文档']
        },
        {
          id: 'step2',
          title: '产品结构设计',
          description: '根据需求设计产品结构和功能模块',
          responsible: '设计工程师',
          duration: '5-7天',
          inputs: ['产品需求规格书', '技术标准'],
          outputs: ['产品结构图', '设计方案']
        },
        {
          id: 'step3',
          title: '物料选型',
          description: '选择合适的原材料和标准件',
          responsible: '技术工程师',
          duration: '3-5天',
          inputs: ['设计方案', '供应商目录'],
          outputs: ['物料清单', '规格说明']
        }
      ]
    },
    {
      id: 'procurement-process',
      title: '采购业务流程',
      description: '从采购需求到收货入库的完整采购业务流程。',
      category: '采购管理',
      diagram: `
        graph TD
          A[采购需求] --> B[需求审核]
          B --> C{需求审核}
          C -->|通过| D[供应商选择]
          C -->|不通过| E[需求修改]
          E --> B
          D --> F[询价报价]
          F --> G[比价分析]
          G --> H[供应商确定]
          H --> I[合同谈判]
          I --> J[合同签订]
          J --> K[采购订单]
          K --> L[订单确认]
          L --> M[生产安排]
          M --> N[物流配送]
          N --> O[收货验收]
          O --> P{质量检验}
          P -->|合格| Q[入库处理]
          P -->|不合格| R[退货处理]
          R --> M
          Q --> S[发票处理]
          S --> T[付款审批]
          T --> U[付款执行]
          U --> V[采购完成]
          
          style A fill:#e1f5fe
          style V fill:#e8f5e8
          style C fill:#fff3e0
          style P fill:#fff3e0
      `,
      steps: [
        {
          id: 'step1',
          title: '采购需求',
          description: '根据生产计划或库存需求生成采购申请',
          responsible: '需求部门',
          duration: '1天',
          inputs: ['生产计划', '库存报告'],
          outputs: ['采购申请单']
        },
        {
          id: 'step2',
          title: '需求审核',
          description: '审核采购需求的合理性和必要性',
          responsible: '部门主管',
          duration: '1-2天',
          inputs: ['采购申请单', '预算计划'],
          outputs: ['审核意见', '批准文件']
        }
      ]
    },
    {
      id: 'inventory-process',
      title: '库存管理流程',
      description: '库存监控、补货、调拨、盘点的完整管理流程。',
      category: '库存管理',
      diagram: `
        graph TD
          A[库存监控] --> B{库存状态检查}
          B -->|正常| A
          B -->|低库存| C[补货提醒]
          B -->|高库存| D[调拨建议]
          B -->|零库存| E[紧急采购]
          
          C --> F[补货申请]
          F --> G[采购流程]
          G --> H[收货入库]
          
          D --> I[调拨申请]
          I --> J[调拨审批]
          J --> K[库存转移]
          
          E --> L[紧急采购]
          L --> M[快速收货]
          
          H --> N[库存更新]
          K --> N
          M --> N
          N --> O[定期盘点]
          O --> P{盘点结果}
          P -->|账实相符| A
          P -->|存在差异| Q[差异分析]
          Q --> R[原因调查]
          R --> S[差异处理]
          S --> A
          
          style A fill:#e1f5fe
          style B fill:#fff3e0
          style P fill:#fff3e0
      `,
      steps: [
        {
          id: 'step1',
          title: '库存监控',
          description: '实时监控库存水平，设置安全库存预警',
          responsible: '仓库管理员',
          duration: '持续',
          inputs: ['库存数据', '安全库存设置'],
          outputs: ['库存报告', '预警信息']
        }
      ]
    }
  ];

  // 获取当前选中的流程
  const getCurrentFlow = () => {
    return businessFlows.find(flow => flow.id === selectedFlow) || businessFlows[0];
  };

  // 渲染流程步骤
  const renderSteps = (steps: FlowStep[]) => {
    return (
      <List
        itemLayout="vertical"
        dataSource={steps}
        renderItem={(step, index) => (
          <List.Item>
            <List.Item.Meta
              avatar={
                <Avatar style={{ backgroundColor: '#1890ff' }}>
                  {index + 1}
                </Avatar>
              }
              title={step.title}
              description={
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Text>{step.description}</Text>
                  <Space wrap>
                    <Tag color="blue">负责人: {step.responsible}</Tag>
                    <Tag color="green">时长: {step.duration}</Tag>
                  </Space>
                  <div>
                    <Text strong>输入: </Text>
                    {step.inputs.join(', ')}
                  </div>
                  <div>
                    <Text strong>输出: </Text>
                    {step.outputs.join(', ')}
                  </div>
                </Space>
              }
            />
          </List.Item>
        )}
      />
    );
  };

  const currentFlow = getCurrentFlow();

  return (
    <div>
      <Card>
        <Tabs
          activeKey={selectedFlow}
          onChange={setSelectedFlow}
          tabBarExtraContent={
            <Space>
              <Button
                icon={<InfoCircleOutlined />}
                onClick={() => setShowStepsModal(true)}
              >
                流程步骤
              </Button>
              <Button icon={<PlayCircleOutlined />}>
                流程演示
              </Button>
            </Space>
          }
        >
          {businessFlows.map(flow => (
            <TabPane
              tab={
                <Space>
                  <ApartmentOutlined />
                  {flow.title}
                </Space>
              }
              key={flow.id}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Title level={4}>{flow.title}</Title>
                  <Paragraph>{flow.description}</Paragraph>
                  <Tag color="blue">{flow.category}</Tag>
                </div>
                
                <MermaidDiagram
                  definition={flow.diagram}
                  height={600}
                  showControls={true}
                />
              </Space>
            </TabPane>
          ))}
        </Tabs>
      </Card>

      {/* 流程步骤模态框 */}
      <Modal
        title={`${currentFlow.title} - 详细步骤`}
        open={showStepsModal}
        onCancel={() => setShowStepsModal(false)}
        footer={null}
        width={800}
      >
        {renderSteps(currentFlow.steps)}
      </Modal>
    </div>
  );
};

export default BusinessFlowDiagram;
