package com.linkboms.service.help;

import com.linkboms.entity.help.HelpAttachment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 帮助附件服务接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface HelpAttachmentService {

    // ==================== 基础CRUD操作 ====================
    
    /**
     * 上传附件
     * 
     * @param file 上传文件
     * @param contentId 内容ID
     * @param description 描述
     * @param altText 替代文本
     * @return 上传的附件
     */
    HelpAttachment uploadAttachment(MultipartFile file, Long contentId, String description, String altText);
    
    /**
     * 创建附件记录
     * 
     * @param attachment 附件信息
     * @return 创建的附件
     */
    HelpAttachment createAttachment(HelpAttachment attachment);
    
    /**
     * 更新附件信息
     * 
     * @param id 附件ID
     * @param attachment 附件信息
     * @return 更新后的附件
     */
    HelpAttachment updateAttachment(Long id, HelpAttachment attachment);
    
    /**
     * 删除附件（软删除）
     * 
     * @param id 附件ID
     */
    void deleteAttachment(Long id);
    
    /**
     * 永久删除附件
     * 
     * @param id 附件ID
     */
    void permanentDeleteAttachment(Long id);
    
    /**
     * 恢复已删除的附件
     * 
     * @param id 附件ID
     */
    void restoreAttachment(Long id);
    
    // ==================== 查询操作 ====================
    
    /**
     * 根据ID获取附件
     * 
     * @param id 附件ID
     * @return 附件信息
     */
    HelpAttachment getAttachmentById(Long id);
    
    /**
     * 分页获取附件列表
     * 
     * @param pageable 分页参数
     * @return 附件分页列表
     */
    Page<HelpAttachment> getAttachmentList(Pageable pageable);
    
    /**
     * 根据内容ID获取附件
     * 
     * @param contentId 内容ID
     * @return 附件列表
     */
    List<HelpAttachment> getAttachmentsByContentId(Long contentId);
    
    /**
     * 根据类型获取附件
     * 
     * @param type 附件类型
     * @param pageable 分页参数
     * @return 附件分页列表
     */
    Page<HelpAttachment> getAttachmentsByType(HelpAttachment.AttachmentType type, Pageable pageable);
    
    /**
     * 根据文件名搜索附件
     * 
     * @param filename 文件名
     * @param pageable 分页参数
     * @return 附件分页列表
     */
    Page<HelpAttachment> getAttachmentsByFilename(String filename, Pageable pageable);
    
    /**
     * 根据MIME类型获取附件
     * 
     * @param mimeType MIME类型
     * @param pageable 分页参数
     * @return 附件分页列表
     */
    Page<HelpAttachment> getAttachmentsByMimeType(String mimeType, Pageable pageable);
    
    /**
     * 根据上传者获取附件
     * 
     * @param uploaderId 上传者ID
     * @param pageable 分页参数
     * @return 附件分页列表
     */
    Page<HelpAttachment> getAttachmentsByUploader(Long uploaderId, Pageable pageable);
    
    /**
     * 获取公开访问的附件
     * 
     * @param pageable 分页参数
     * @return 公开附件列表
     */
    Page<HelpAttachment> getPublicAttachments(Pageable pageable);
    
    /**
     * 获取有缩略图的附件
     * 
     * @param pageable 分页参数
     * @return 有缩略图的附件列表
     */
    Page<HelpAttachment> getAttachmentsWithThumbnail(Pageable pageable);
    
    /**
     * 根据文件大小范围获取附件
     * 
     * @param minSize 最小文件大小
     * @param maxSize 最大文件大小
     * @param pageable 分页参数
     * @return 附件列表
     */
    Page<HelpAttachment> getAttachmentsByFileSize(Long minSize, Long maxSize, Pageable pageable);
    
    /**
     * 根据创建时间范围获取附件
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 附件列表
     */
    Page<HelpAttachment> getAttachmentsByTimeRange(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);
    
    /**
     * 根据下载次数范围获取附件
     * 
     * @param minDownloads 最小下载次数
     * @param maxDownloads 最大下载次数
     * @param pageable 分页参数
     * @return 附件列表
     */
    Page<HelpAttachment> getAttachmentsByDownloadCount(Long minDownloads, Long maxDownloads, Pageable pageable);
    
    /**
     * 搜索附件
     * 
     * @param keyword 关键词
     * @param pageable 分页参数
     * @return 附件列表
     */
    Page<HelpAttachment> searchAttachments(String keyword, Pageable pageable);
    
    /**
     * 获取孤儿附件（没有关联内容的附件）
     * 
     * @param pageable 分页参数
     * @return 孤儿附件列表
     */
    Page<HelpAttachment> getOrphanAttachments(Pageable pageable);
    
    // ==================== 文件操作 ====================
    
    /**
     * 下载附件
     * 
     * @param id 附件ID
     * @return 文件字节数组
     */
    byte[] downloadAttachment(Long id);
    
    /**
     * 获取附件访问URL
     * 
     * @param id 附件ID
     * @return 访问URL
     */
    String getAttachmentUrl(Long id);
    
    /**
     * 获取附件缩略图URL
     * 
     * @param id 附件ID
     * @return 缩略图URL
     */
    String getThumbnailUrl(Long id);
    
    /**
     * 生成缩略图
     * 
     * @param id 附件ID
     * @param width 宽度
     * @param height 高度
     * @return 是否成功
     */
    boolean generateThumbnail(Long id, Integer width, Integer height);
    
    /**
     * 批量生成缩略图
     * 
     * @param ids 附件ID列表
     * @param width 宽度
     * @param height 高度
     */
    void batchGenerateThumbnails(List<Long> ids, Integer width, Integer height);
    
    /**
     * 压缩图片
     * 
     * @param id 附件ID
     * @param quality 压缩质量（0-100）
     * @return 是否成功
     */
    boolean compressImage(Long id, Integer quality);
    
    /**
     * 转换文件格式
     * 
     * @param id 附件ID
     * @param targetFormat 目标格式
     * @return 转换后的附件
     */
    HelpAttachment convertFormat(Long id, String targetFormat);
    
    // ==================== 状态管理 ====================
    
    /**
     * 设置附件排序
     * 
     * @param id 附件ID
     * @param sortOrder 排序号
     */
    void setAttachmentSortOrder(Long id, Integer sortOrder);
    
    /**
     * 批量排序附件
     * 
     * @param sortData 排序数据
     */
    void sortAttachments(List<Map<String, Object>> sortData);
    
    /**
     * 设置公开访问权限
     * 
     * @param id 附件ID
     * @param publicAccess 是否公开
     */
    void setPublicAccess(Long id, boolean publicAccess);
    
    /**
     * 批量设置公开访问权限
     * 
     * @param ids 附件ID列表
     * @param publicAccess 是否公开
     */
    void batchSetPublicAccess(List<Long> ids, boolean publicAccess);
    
    // ==================== 下载统计 ====================
    
    /**
     * 增加下载次数
     * 
     * @param id 附件ID
     */
    void incrementDownloadCount(Long id);
    
    /**
     * 批量增加下载次数
     * 
     * @param ids 附件ID列表
     */
    void batchIncrementDownloadCount(List<Long> ids);
    
    /**
     * 更新下载次数
     * 
     * @param id 附件ID
     * @param count 下载次数
     */
    void updateDownloadCount(Long id, Long count);
    
    /**
     * 重置下载次数
     * 
     * @param id 附件ID
     */
    void resetDownloadCount(Long id);
    
    // ==================== 批量操作 ====================
    
    /**
     * 批量上传附件
     * 
     * @param files 文件列表
     * @param contentId 内容ID
     * @return 上传的附件列表
     */
    List<HelpAttachment> batchUploadAttachments(List<MultipartFile> files, Long contentId);
    
    /**
     * 批量删除附件
     * 
     * @param ids 附件ID列表
     */
    void batchDeleteAttachments(List<Long> ids);
    
    /**
     * 批量恢复附件
     * 
     * @param ids 附件ID列表
     */
    void batchRestoreAttachments(List<Long> ids);
    
    /**
     * 批量移动附件
     * 
     * @param ids 附件ID列表
     * @param targetContentId 目标内容ID
     */
    void batchMoveAttachments(List<Long> ids, Long targetContentId);
    
    /**
     * 批量复制附件
     * 
     * @param ids 附件ID列表
     * @param targetContentId 目标内容ID
     * @return 复制的附件列表
     */
    List<HelpAttachment> batchCopyAttachments(List<Long> ids, Long targetContentId);
    
    // ==================== 统计信息 ====================
    
    /**
     * 统计附件数量
     * 
     * @param contentId 内容ID
     * @return 附件数量
     */
    Long countAttachments(Long contentId);
    
    /**
     * 按类型统计附件数量
     * 
     * @param contentId 内容ID
     * @return 类型统计
     */
    Map<HelpAttachment.AttachmentType, Long> countAttachmentsByType(Long contentId);
    
    /**
     * 统计文件总大小
     * 
     * @param contentId 内容ID
     * @return 文件总大小
     */
    Long getTotalFileSize(Long contentId);
    
    /**
     * 统计下载总次数
     * 
     * @param contentId 内容ID
     * @return 下载总次数
     */
    Long getTotalDownloadCount(Long contentId);
    
    /**
     * 统计指定时间范围内的附件数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 附件数量
     */
    Long countAttachmentsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 统计指定时间范围内的文件大小
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 文件大小
     */
    Long getTotalFileSizeByTimeRange(LocalDateTime startTime, LocalDateTime endTime);
    
    /**
     * 获取附件统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getAttachmentStatistics();
    
    /**
     * 获取内容附件统计
     * 
     * @param contentId 内容ID
     * @return 内容附件统计
     */
    Map<String, Object> getContentAttachmentStatistics(Long contentId);
    
    /**
     * 获取用户上传统计
     * 
     * @param uploaderId 上传者ID
     * @return 用户上传统计
     */
    Map<String, Object> getUserUploadStatistics(Long uploaderId);
    
    // ==================== 验证与检查 ====================
    
    /**
     * 验证附件数据
     * 
     * @param attachment 附件信息
     * @return 验证错误列表
     */
    List<String> validateAttachment(HelpAttachment attachment);
    
    /**
     * 验证文件类型
     * 
     * @param file 上传文件
     * @return 是否允许
     */
    boolean isFileTypeAllowed(MultipartFile file);
    
    /**
     * 验证文件大小
     * 
     * @param file 上传文件
     * @return 是否允许
     */
    boolean isFileSizeAllowed(MultipartFile file);
    
    /**
     * 检查文件是否存在
     * 
     * @param id 附件ID
     * @return 是否存在
     */
    boolean checkFileExists(Long id);
    
    /**
     * 获取下一个排序号
     * 
     * @param contentId 内容ID
     * @return 下一个排序号
     */
    Integer getNextSortOrder(Long contentId);
    
    // ==================== 清理与维护 ====================
    
    /**
     * 清理孤儿附件
     */
    void cleanupOrphanAttachments();
    
    /**
     * 清理过期附件
     * 
     * @param days 保留天数
     */
    void cleanupExpiredAttachments(int days);
    
    /**
     * 清理重复附件
     */
    void cleanupDuplicateAttachments();
    
    /**
     * 清理损坏的文件
     */
    void cleanupCorruptedFiles();
    
    /**
     * 同步文件系统
     */
    void syncFileSystem();
    
    /**
     * 修复文件路径
     */
    void repairFilePaths();
    
    // ==================== 导入导出 ====================
    
    /**
     * 导出附件信息
     * 
     * @param format 导出格式（json, csv, excel）
     * @param contentId 内容ID（可选）
     * @return 导出数据
     */
    byte[] exportAttachments(String format, Long contentId);
    
    /**
     * 导入附件信息
     * 
     * @param file 导入文件
     * @return 导入的附件列表
     */
    List<HelpAttachment> importAttachments(MultipartFile file);
    
    /**
     * 打包下载附件
     * 
     * @param ids 附件ID列表
     * @return 压缩包字节数组
     */
    byte[] packageAttachments(List<Long> ids);
    
    // ==================== 安全与权限 ====================
    
    /**
     * 检查访问权限
     * 
     * @param id 附件ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    boolean checkAccessPermission(Long id, Long userId);
    
    /**
     * 检查下载权限
     * 
     * @param id 附件ID
     * @param userId 用户ID
     * @return 是否有权限
     */
    boolean checkDownloadPermission(Long id, Long userId);
    
    /**
     * 生成临时访问链接
     * 
     * @param id 附件ID
     * @param expirationMinutes 过期时间（分钟）
     * @return 临时链接
     */
    String generateTemporaryLink(Long id, int expirationMinutes);
    
    /**
     * 验证临时访问链接
     * 
     * @param token 临时令牌
     * @return 附件ID
     */
    Long validateTemporaryLink(String token);
    
    // ==================== 图片处理 ====================
    
    /**
     * 调整图片大小
     * 
     * @param id 附件ID
     * @param width 宽度
     * @param height 高度
     * @return 调整后的附件
     */
    HelpAttachment resizeImage(Long id, Integer width, Integer height);
    
    /**
     * 裁剪图片
     * 
     * @param id 附件ID
     * @param x 起始X坐标
     * @param y 起始Y坐标
     * @param width 宽度
     * @param height 高度
     * @return 裁剪后的附件
     */
    HelpAttachment cropImage(Long id, Integer x, Integer y, Integer width, Integer height);
    
    /**
     * 旋转图片
     * 
     * @param id 附件ID
     * @param angle 旋转角度
     * @return 旋转后的附件
     */
    HelpAttachment rotateImage(Long id, Integer angle);
    
    /**
     * 添加水印
     * 
     * @param id 附件ID
     * @param watermarkText 水印文本
     * @param position 水印位置
     * @return 添加水印后的附件
     */
    HelpAttachment addWatermark(Long id, String watermarkText, String position);
}