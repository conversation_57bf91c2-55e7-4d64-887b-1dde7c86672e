package com.linkboms.service.help;

import com.linkboms.entity.help.HelpAttachment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 帮助附件服务接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface HelpAttachmentService {

    /**
     * 上传附件
     */
    HelpAttachment uploadAttachment(Long contentId, MultipartFile file, String description);

    /**
     * 批量上传附件
     */
    List<HelpAttachment> batchUploadAttachments(Long contentId, List<MultipartFile> files, List<String> descriptions);

    /**
     * 创建附件记录
     */
    HelpAttachment createAttachment(HelpAttachment attachment);

    /**
     * 更新附件信息
     */
    HelpAttachment updateAttachment(Long id, HelpAttachment attachment);

    /**
     * 根据ID获取附件
     */
    HelpAttachment getAttachmentById(Long id);

    /**
     * 根据ID获取附件（可选）
     */
    Optional<HelpAttachment> findAttachmentById(Long id);

    /**
     * 根据文件路径获取附件
     */
    Optional<HelpAttachment> findAttachmentByPath(String filePath);

    /**
     * 删除附件
     */
    void deleteAttachment(Long id);

    /**
     * 软删除附件
     */
    void softDeleteAttachment(Long id);

    /**
     * 恢复附件
     */
    void restoreAttachment(Long id);

    /**
     * 物理删除附件文件
     */
    void deleteAttachmentFile(Long id);

    /**
     * 获取所有附件（分页）
     */
    Page<HelpAttachment> getAllAttachments(Pageable pageable);

    /**
     * 根据内容ID获取附件
     */
    List<HelpAttachment> getAttachmentsByContent(Long contentId);

    /**
     * 根据内容ID分页获取附件
     */
    Page<HelpAttachment> getAttachmentsByContent(Long contentId, Pageable pageable);

    /**
     * 根据类型获取附件
     */
    Page<HelpAttachment> getAttachmentsByType(HelpAttachment.AttachmentType type, Pageable pageable);

    /**
     * 根据上传者获取附件
     */
    Page<HelpAttachment> getAttachmentsByUploader(Long uploaderId, Pageable pageable);

    /**
     * 搜索附件
     */
    Page<HelpAttachment> searchAttachments(String keyword, HelpAttachment.AttachmentType type, Pageable pageable);

    /**
     * 获取热门下载附件
     */
    List<HelpAttachment> getPopularDownloads(int limit);

    /**
     * 查找大文件
     */
    List<HelpAttachment> findLargeFiles(Long minSize, int limit);

    /**
     * 查找小文件
     */
    List<HelpAttachment> findSmallFiles(Long maxSize, int limit);

    /**
     * 查找孤立附件
     */
    List<HelpAttachment> findOrphanedAttachments();

    /**
     * 查找重复文件
     */
    List<HelpAttachment> findDuplicateFiles();

    /**
     * 查找过期临时文件
     */
    List<HelpAttachment> findExpiredTempFiles(LocalDateTime expireDate);

    /**
     * 根据扩展名查找附件
     */
    List<HelpAttachment> findAttachmentsByExtension(String extension, int limit);

    /**
     * 根据时间范围查找附件
     */
    List<HelpAttachment> findAttachmentsByDateRange(LocalDateTime startDate, LocalDateTime endDate, int limit);

    /**
     * 增加下载次数
     */
    void incrementDownloadCount(Long id);

    /**
     * 批量删除附件
     */
    void batchDeleteAttachments(List<Long> ids);

    /**
     * 根据内容ID批量删除附件
     */
    void deleteAttachmentsByContent(Long contentId);

    /**
     * 移动附件到其他内容
     */
    void moveAttachmentToContent(Long attachmentId, Long newContentId);

    /**
     * 复制附件
     */
    HelpAttachment copyAttachment(Long id, Long newContentId);

    /**
     * 压缩图片
     */
    void compressImage(Long attachmentId, int quality);

    /**
     * 生成缩略图
     */
    String generateThumbnail(Long attachmentId, int width, int height);

    /**
     * 验证文件类型
     */
    boolean validateFileType(MultipartFile file);

    /**
     * 验证文件大小
     */
    boolean validateFileSize(MultipartFile file);

    /**
     * 获取文件存储路径
     */
    String getStoragePath(String fileName, HelpAttachment.AttachmentType type);

    /**
     * 生成唯一文件名
     */
    String generateUniqueFileName(String originalFileName);

    /**
     * 检测文件类型
     */
    HelpAttachment.AttachmentType detectFileType(MultipartFile file);

    /**
     * 获取附件统计信息
     */
    Object getAttachmentStatistics();

    /**
     * 获取附件类型统计
     */
    Object getAttachmentTypeStatistics();

    /**
     * 获取存储空间统计
     */
    Object getStorageStatistics();

    /**
     * 清理孤立文件
     */
    void cleanupOrphanedFiles();

    /**
     * 清理临时文件
     */
    void cleanupTempFiles();

    /**
     * 清理重复文件
     */
    void cleanupDuplicateFiles();

    /**
     * 备份附件
     */
    void backupAttachments(List<Long> attachmentIds, String backupPath);

    /**
     * 恢复附件
     */
    void restoreAttachments(String backupPath);

    /**
     * 导出附件列表
     */
    byte[] exportAttachmentList(List<Long> attachmentIds);

    /**
     * 批量下载附件
     */
    byte[] batchDownloadAttachments(List<Long> attachmentIds);

    /**
     * 获取附件下载URL
     */
    String getDownloadUrl(Long attachmentId);

    /**
     * 获取附件预览URL
     */
    String getPreviewUrl(Long attachmentId);

    /**
     * 检查附件访问权限
     */
    boolean checkAccessPermission(Long attachmentId, Long userId);

    /**
     * 记录附件访问日志
     */
    void logAttachmentAccess(Long attachmentId, Long userId, String action);

    /**
     * 获取附件访问统计
     */
    Object getAttachmentAccessStatistics(Long attachmentId);

    /**
     * 扫描病毒
     */
    boolean scanVirus(Long attachmentId);

    /**
     * 水印处理
     */
    void addWatermark(Long attachmentId, String watermarkText);
}
