package com.linkboms.repository.help;

import com.linkboms.entity.help.HelpTag;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 帮助标签Repository接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface HelpTagRepository extends JpaRepository<HelpTag, Long> {

    /**
     * 根据名称查找标签
     */
    Optional<HelpTag> findByNameAndDeletedFalse(String name);

    /**
     * 根据类型查找标签
     */
    List<HelpTag> findByTypeAndEnabledTrueAndDeletedFalseOrderByUsageCountDesc(HelpTag.TagType type);

    /**
     * 查找所有启用的标签
     */
    List<HelpTag> findByEnabledTrueAndDeletedFalseOrderByUsageCountDesc();

    /**
     * 分页查找标签
     */
    Page<HelpTag> findByDeletedFalseOrderByUsageCountDesc(Pageable pageable);

    /**
     * 根据类型分页查找标签
     */
    Page<HelpTag> findByTypeAndDeletedFalseOrderByUsageCountDesc(HelpTag.TagType type, Pageable pageable);

    /**
     * 搜索标签
     */
    @Query("SELECT t FROM HelpTag t WHERE " +
           "(LOWER(t.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(t.description) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "t.enabled = true AND t.deleted = false " +
           "ORDER BY t.usageCount DESC")
    List<HelpTag> searchTags(@Param("keyword") String keyword);

    /**
     * 查找热门标签
     */
    @Query("SELECT t FROM HelpTag t WHERE t.enabled = true AND t.deleted = false " +
           "ORDER BY t.usageCount DESC")
    List<HelpTag> findPopularTags(Pageable pageable);

    /**
     * 查找未使用的标签
     */
    @Query("SELECT t FROM HelpTag t WHERE t.usageCount = 0 AND t.deleted = false")
    List<HelpTag> findUnusedTags();

    /**
     * 查找使用次数超过指定值的标签
     */
    @Query("SELECT t FROM HelpTag t WHERE t.usageCount >= :minUsage AND t.enabled = true AND t.deleted = false " +
           "ORDER BY t.usageCount DESC")
    List<HelpTag> findTagsWithMinUsage(@Param("minUsage") Long minUsage);

    /**
     * 根据颜色查找标签
     */
    List<HelpTag> findByColorAndEnabledTrueAndDeletedFalse(String color);

    /**
     * 增加使用次数
     */
    @Modifying
    @Query("UPDATE HelpTag t SET t.usageCount = t.usageCount + 1, t.updatedAt = CURRENT_TIMESTAMP WHERE t.id = :id")
    void incrementUsageCount(@Param("id") Long id);

    /**
     * 减少使用次数
     */
    @Modifying
    @Query("UPDATE HelpTag t SET t.usageCount = CASE WHEN t.usageCount > 0 THEN t.usageCount - 1 ELSE 0 END, " +
           "t.updatedAt = CURRENT_TIMESTAMP WHERE t.id = :id")
    void decrementUsageCount(@Param("id") Long id);

    /**
     * 更新启用状态
     */
    @Modifying
    @Query("UPDATE HelpTag t SET t.enabled = :enabled, t.updatedAt = CURRENT_TIMESTAMP WHERE t.id = :id")
    void updateEnabled(@Param("id") Long id, @Param("enabled") Boolean enabled);

    /**
     * 批量更新启用状态
     */
    @Modifying
    @Query("UPDATE HelpTag t SET t.enabled = :enabled, t.updatedAt = CURRENT_TIMESTAMP WHERE t.id IN :ids")
    void batchUpdateEnabled(@Param("ids") List<Long> ids, @Param("enabled") Boolean enabled);

    /**
     * 批量软删除
     */
    @Modifying
    @Query("UPDATE HelpTag t SET t.deleted = true, t.enabled = false, t.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE t.id IN :ids")
    void batchSoftDelete(@Param("ids") List<Long> ids);

    /**
     * 重置使用次数
     */
    @Modifying
    @Query("UPDATE HelpTag t SET t.usageCount = " +
           "(SELECT COUNT(ct) FROM HelpContent c JOIN c.tags ct WHERE ct.id = t.id AND c.deleted = false), " +
           "t.updatedAt = CURRENT_TIMESTAMP WHERE t.id = :id")
    void resetUsageCount(@Param("id") Long id);

    /**
     * 批量重置使用次数
     */
    @Modifying
    @Query("UPDATE HelpTag t SET t.usageCount = " +
           "(SELECT COUNT(ct) FROM HelpContent c JOIN c.tags ct WHERE ct.id = t.id AND c.deleted = false), " +
           "t.updatedAt = CURRENT_TIMESTAMP")
    void batchResetUsageCount();

    /**
     * 检查标签名称是否已存在
     */
    boolean existsByNameAndDeletedFalse(String name);

    /**
     * 检查标签名称是否已存在（排除指定ID）
     */
    boolean existsByNameAndIdNotAndDeletedFalse(String name, Long id);

    /**
     * 统计各类型的标签数量
     */
    @Query("SELECT t.type, COUNT(t) FROM HelpTag t WHERE t.deleted = false GROUP BY t.type")
    List<Object[]> countByType();

    /**
     * 统计启用的标签数量
     */
    @Query("SELECT COUNT(t) FROM HelpTag t WHERE t.enabled = true AND t.deleted = false")
    Long countEnabledTags();

    /**
     * 统计总使用次数
     */
    @Query("SELECT SUM(t.usageCount) FROM HelpTag t WHERE t.deleted = false")
    Long sumUsageCount();

    /**
     * 查找相似标签（基于名称）
     */
    @Query("SELECT t FROM HelpTag t WHERE " +
           "LOWER(t.name) LIKE LOWER(CONCAT('%', :name, '%')) AND " +
           "t.id != :excludeId AND t.enabled = true AND t.deleted = false " +
           "ORDER BY t.usageCount DESC")
    List<HelpTag> findSimilarTags(@Param("name") String name, @Param("excludeId") Long excludeId);

    /**
     * 查找指定内容的标签
     */
    @Query("SELECT t FROM HelpTag t JOIN t.contents c WHERE c.id = :contentId AND t.deleted = false")
    List<HelpTag> findByContentId(@Param("contentId") Long contentId);

    /**
     * 查找标签云数据（用于标签云展示）
     */
    @Query("SELECT t.name, t.usageCount FROM HelpTag t WHERE " +
           "t.enabled = true AND t.deleted = false AND t.usageCount > 0 " +
           "ORDER BY t.usageCount DESC")
    List<Object[]> findTagCloudData(Pageable pageable);

    /**
     * 查找推荐标签（基于使用频率和相关性）
     */
    @Query("SELECT DISTINCT t FROM HelpTag t JOIN t.contents c WHERE " +
           "c.categoryId = :categoryId AND t.enabled = true AND t.deleted = false " +
           "ORDER BY t.usageCount DESC")
    List<HelpTag> findRecommendedTagsByCategory(@Param("categoryId") Long categoryId, Pageable pageable);
}
