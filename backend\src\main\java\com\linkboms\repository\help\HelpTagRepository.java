package com.linkboms.repository.help;

import com.linkboms.entity.help.HelpTag;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 帮助标签Repository接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface HelpTagRepository extends JpaRepository<HelpTag, Long> {

    /**
     * 查找所有启用的标签
     */
    List<HelpTag> findByEnabledTrueAndDeletedFalseOrderBySortOrderAsc();

    /**
     * 根据类型查找标签
     */
    List<HelpTag> findByTypeAndEnabledTrueAndDeletedFalseOrderBySortOrderAsc(HelpTag.TagType type);

    /**
     * 根据名称查找标签
     */
    Optional<HelpTag> findByNameAndDeletedFalse(String name);

    /**
     * 根据名称列表查找标签
     */
    List<HelpTag> findByNameInAndDeletedFalse(List<String> names);

    /**
     * 检查标签名称是否已存在
     */
    boolean existsByNameAndDeletedFalse(String name);

    /**
     * 检查标签名称是否已存在（排除指定ID）
     */
    boolean existsByNameAndIdNotAndDeletedFalse(String name, Long id);

    /**
     * 根据关键词搜索标签
     */
    @Query("SELECT t FROM HelpTag t WHERE " +
           "(LOWER(t.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(t.description) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "t.enabled = true AND t.deleted = false " +
           "ORDER BY t.usageCount DESC, t.sortOrder ASC")
    List<HelpTag> searchByKeyword(@Param("keyword") String keyword);

    /**
     * 查找热门标签
     */
    @Query("SELECT t FROM HelpTag t WHERE " +
           "t.usageCount >= :minUsage AND t.enabled = true AND t.deleted = false " +
           "ORDER BY t.usageCount DESC")
    List<HelpTag> findPopularTags(@Param("minUsage") Integer minUsage, Pageable pageable);

    /**
     * 查找最常用的标签
     */
    List<HelpTag> findTop20ByEnabledTrueAndDeletedFalseOrderByUsageCountDesc();

    /**
     * 查找未使用的标签
     */
    List<HelpTag> findByUsageCountAndEnabledTrueAndDeletedFalseOrderByCreatedAtDesc(Integer usageCount);

    /**
     * 增加标签使用次数
     */
    @Modifying
    @Query("UPDATE HelpTag t SET t.usageCount = t.usageCount + 1 WHERE t.id = :id")
    void incrementUsageCount(@Param("id") Long id);

    /**
     * 减少标签使用次数
     */
    @Modifying
    @Query("UPDATE HelpTag t SET t.usageCount = t.usageCount - 1 WHERE t.id = :id AND t.usageCount > 0")
    void decrementUsageCount(@Param("id") Long id);

    /**
     * 批量更新标签使用次数
     */
    @Modifying
    @Query("UPDATE HelpTag t SET t.usageCount = :count WHERE t.id = :id")
    void updateUsageCount(@Param("id") Long id, @Param("count") Integer count);

    /**
     * 软删除标签
     */
    @Modifying
    @Query("UPDATE HelpTag t SET t.deleted = true WHERE t.id = :id")
    void softDelete(@Param("id") Long id);

    /**
     * 批量软删除
     */
    @Modifying
    @Query("UPDATE HelpTag t SET t.deleted = true WHERE t.id IN :ids")
    void batchSoftDelete(@Param("ids") List<Long> ids);

    /**
     * 恢复已删除的标签
     */
    @Modifying
    @Query("UPDATE HelpTag t SET t.deleted = false WHERE t.id = :id")
    void restore(@Param("id") Long id);

    /**
     * 更新标签状态
     */
    @Modifying
    @Query("UPDATE HelpTag t SET t.enabled = :enabled WHERE t.id = :id")
    void updateStatus(@Param("id") Long id, @Param("enabled") Boolean enabled);

    /**
     * 批量更新标签状态
     */
    @Modifying
    @Query("UPDATE HelpTag t SET t.enabled = :enabled WHERE t.id IN :ids")
    void batchUpdateStatus(@Param("ids") List<Long> ids, @Param("enabled") Boolean enabled);

    /**
     * 统计各类型的标签数量
     */
    @Query("SELECT t.type, COUNT(t) FROM HelpTag t WHERE t.deleted = false GROUP BY t.type")
    List<Object[]> countByType();

    /**
     * 统计启用和禁用的标签数量
     */
    @Query("SELECT t.enabled, COUNT(t) FROM HelpTag t WHERE t.deleted = false GROUP BY t.enabled")
    List<Object[]> countByStatus();

    /**
     * 查找指定内容的标签
     */
    @Query("SELECT t FROM HelpTag t JOIN t.contents c WHERE c.id = :contentId AND t.deleted = false")
    List<HelpTag> findByContentId(@Param("contentId") Long contentId);

    /**
     * 查找相关标签（基于共同内容）
     */
    @Query("SELECT DISTINCT t FROM HelpTag t JOIN t.contents c1 " +
           "WHERE c1.id IN (SELECT c2.id FROM HelpContent c2 JOIN c2.tags t2 WHERE t2.id = :tagId) " +
           "AND t.id != :tagId AND t.enabled = true AND t.deleted = false " +
           "ORDER BY t.usageCount DESC")
    List<HelpTag> findRelatedTags(@Param("tagId") Long tagId, Pageable pageable);

    /**
     * 查找标签云数据
     */
    @Query("SELECT t.name, t.usageCount FROM HelpTag t WHERE " +
           "t.enabled = true AND t.deleted = false AND t.usageCount > 0 " +
           "ORDER BY t.usageCount DESC")
    List<Object[]> findTagCloudData(Pageable pageable);

    /**
     * 查找建议标签（基于名称相似度）
     */
    @Query("SELECT t FROM HelpTag t WHERE " +
           "LOWER(t.name) LIKE LOWER(CONCAT('%', :keyword, '%')) AND " +
           "t.enabled = true AND t.deleted = false " +
           "ORDER BY LENGTH(t.name) ASC, t.usageCount DESC")
    List<HelpTag> findSuggestedTags(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 清理未使用的标签
     */
    @Modifying
    @Query("UPDATE HelpTag t SET t.deleted = true WHERE t.usageCount = 0 AND t.enabled = true")
    void cleanupUnusedTags();

    /**
     * 重新计算所有标签的使用次数
     */
    @Modifying
    @Query("UPDATE HelpTag t SET t.usageCount = " +
           "(SELECT COUNT(c) FROM HelpContent c JOIN c.tags ct WHERE ct.id = t.id AND c.deleted = false)")
    void recalculateAllUsageCounts();

    /**
     * 查找需要重新计算使用次数的标签
     */
    @Query("SELECT t FROM HelpTag t WHERE t.usageCount != " +
           "(SELECT COUNT(c) FROM HelpContent c JOIN c.tags ct WHERE ct.id = t.id AND c.deleted = false) " +
           "AND t.deleted = false")
    List<HelpTag> findTagsNeedingRecalculation();

    /**
     * 分页查询标签
     */
    Page<HelpTag> findByDeletedFalseOrderByUsageCountDescCreatedAtDesc(Pageable pageable);

    /**
     * 根据类型分页查询标签
     */
    Page<HelpTag> findByTypeAndDeletedFalseOrderByUsageCountDescCreatedAtDesc(
        HelpTag.TagType type, Pageable pageable
    );

    /**
     * 查找最大排序号
     */
    @Query("SELECT COALESCE(MAX(t.sortOrder), 0) FROM HelpTag t WHERE t.type = :type AND t.deleted = false")
    Integer findMaxSortOrderByType(@Param("type") HelpTag.TagType type);
}