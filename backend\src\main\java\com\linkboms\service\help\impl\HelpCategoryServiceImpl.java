package com.linkboms.service.help.impl;

import com.linkboms.entity.help.HelpCategory;
import com.linkboms.repository.help.HelpCategoryRepository;
import com.linkboms.service.help.HelpCategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 帮助分类服务实现类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class HelpCategoryServiceImpl implements HelpCategoryService {

    private final HelpCategoryRepository categoryRepository;

    @Override
    @Transactional
    public HelpCategory createCategory(HelpCategory category) {
        log.info("创建帮助分类: {}", category.getName());
        
        validateCategory(category);
        
        // 设置层级和路径
        if (category.getParentId() != null) {
            HelpCategory parent = getCategoryById(category.getParentId());
            category.setLevel(parent.getLevel() + 1);
            category.setPath(parent.getPath() + "/" + category.getName());
        } else {
            category.setLevel(0);
            category.setPath("/" + category.getName());
        }
        
        // 设置排序号
        if (category.getSortOrder() == null) {
            Integer maxSortOrder = categoryRepository.findMaxSortOrderByParentId(category.getParentId());
            category.setSortOrder(maxSortOrder + 1);
        }
        
        // 设置默认值
        if (category.getVisible() == null) {
            category.setVisible(true);
        }
        if (category.getEnabled() == null) {
            category.setEnabled(true);
        }
        if (category.getDeleted() == null) {
            category.setDeleted(false);
        }
        
        category.setCreatedAt(LocalDateTime.now());
        category.setUpdatedAt(LocalDateTime.now());
        
        return categoryRepository.save(category);
    }

    @Override
    @Transactional
    public HelpCategory updateCategory(Long id, HelpCategory category) {
        log.info("更新帮助分类: {}", id);
        
        HelpCategory existingCategory = getCategoryById(id);
        
        // 更新基本信息
        existingCategory.setName(category.getName());
        existingCategory.setDescription(category.getDescription());
        existingCategory.setIcon(category.getIcon());
        existingCategory.setVisible(category.getVisible());
        existingCategory.setEnabled(category.getEnabled());
        existingCategory.setUpdater(category.getUpdater());
        existingCategory.setUpdaterId(category.getUpdaterId());
        existingCategory.setUpdatedAt(LocalDateTime.now());
        
        // 如果父分类发生变化，需要重新计算路径
        if (!Objects.equals(existingCategory.getParentId(), category.getParentId())) {
            existingCategory.setParentId(category.getParentId());
            rebuildCategoryPath(id);
        }
        
        validateCategory(existingCategory);
        
        return categoryRepository.save(existingCategory);
    }

    @Override
    public HelpCategory getCategoryById(Long id) {
        return categoryRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("帮助分类不存在: " + id));
    }

    @Override
    public Optional<HelpCategory> findCategoryById(Long id) {
        return categoryRepository.findById(id);
    }

    @Override
    public Optional<HelpCategory> findCategoryByName(String name) {
        return categoryRepository.findByNameAndDeletedFalse(name);
    }

    @Override
    public Optional<HelpCategory> findCategoryByPath(String path) {
        return categoryRepository.findByPathAndDeletedFalse(path);
    }

    @Override
    @Transactional
    public void deleteCategory(Long id) {
        log.info("删除帮助分类: {}", id);
        
        HelpCategory category = getCategoryById(id);
        
        // 检查是否有子分类
        List<HelpCategory> children = categoryRepository.findByParentIdAndDeletedFalseOrderBySortOrderAsc(id);
        if (!children.isEmpty()) {
            throw new RuntimeException("存在子分类，无法删除");
        }
        
        // 检查是否有关联内容
        Long contentCount = categoryRepository.countContentsByCategoryId(id);
        if (contentCount > 0) {
            throw new RuntimeException("存在关联内容，无法删除");
        }
        
        categoryRepository.delete(category);
    }

    @Override
    @Transactional
    public void softDeleteCategory(Long id) {
        log.info("软删除帮助分类: {}", id);
        
        HelpCategory category = getCategoryById(id);
        category.softDelete();
        categoryRepository.save(category);
        
        // 同时软删除所有子分类
        List<HelpCategory> descendants = categoryRepository.findAllDescendants(category.getPath());
        for (HelpCategory descendant : descendants) {
            if (!descendant.getId().equals(id)) {
                descendant.softDelete();
                categoryRepository.save(descendant);
            }
        }
    }

    @Override
    @Transactional
    public void restoreCategory(Long id) {
        log.info("恢复帮助分类: {}", id);
        
        HelpCategory category = getCategoryById(id);
        category.restore();
        categoryRepository.save(category);
    }

    @Override
    @Transactional
    public void enableCategory(Long id) {
        log.info("启用帮助分类: {}", id);
        
        HelpCategory category = getCategoryById(id);
        category.enable();
        categoryRepository.save(category);
    }

    @Override
    @Transactional
    public void disableCategory(Long id) {
        log.info("禁用帮助分类: {}", id);
        
        HelpCategory category = getCategoryById(id);
        category.disable();
        categoryRepository.save(category);
    }

    @Override
    @Transactional
    public void showCategory(Long id) {
        log.info("显示帮助分类: {}", id);
        
        HelpCategory category = getCategoryById(id);
        category.show();
        categoryRepository.save(category);
    }

    @Override
    @Transactional
    public void hideCategory(Long id) {
        log.info("隐藏帮助分类: {}", id);
        
        HelpCategory category = getCategoryById(id);
        category.hide();
        categoryRepository.save(category);
    }

    @Override
    public Page<HelpCategory> getAllCategories(Pageable pageable) {
        return categoryRepository.findAll(pageable);
    }

    @Override
    public List<HelpCategory> getAllEnabledCategories() {
        return categoryRepository.findByEnabledTrueAndDeletedFalseOrderByLevelAscSortOrderAsc();
    }

    @Override
    public List<HelpCategory> getAllVisibleCategories() {
        return categoryRepository.findByVisibleTrueAndEnabledTrueAndDeletedFalseOrderBySortOrderAsc();
    }

    @Override
    public List<HelpCategory> getRootCategories() {
        return categoryRepository.findByParentIdIsNullAndEnabledTrueAndDeletedFalseOrderBySortOrderAsc();
    }

    @Override
    public List<HelpCategory> getChildCategories(Long parentId) {
        return categoryRepository.findByParentIdAndEnabledTrueAndDeletedFalseOrderBySortOrderAsc(parentId);
    }

    @Override
    public List<HelpCategory> getCategoryTree() {
        List<HelpCategory> allCategories = getAllEnabledCategories();
        return buildCategoryTree(allCategories);
    }

    @Override
    public HelpCategory getCategoryTreeById(Long id) {
        HelpCategory category = getCategoryById(id);
        List<HelpCategory> descendants = categoryRepository.findAllDescendants(category.getPath());
        
        Map<Long, HelpCategory> categoryMap = descendants.stream()
            .collect(Collectors.toMap(HelpCategory::getId, c -> c));
        categoryMap.put(category.getId(), category);
        
        return buildSingleCategoryTree(category, categoryMap);
    }

    @Override
    public List<HelpCategory> getCategoryPath(Long id) {
        HelpCategory category = getCategoryById(id);
        List<HelpCategory> path = new ArrayList<>();
        
        HelpCategory current = category;
        while (current != null) {
            path.add(0, current);
            if (current.getParentId() != null) {
                current = getCategoryById(current.getParentId());
            } else {
                current = null;
            }
        }
        
        return path;
    }

    @Override
    public List<HelpCategory> searchCategories(String keyword) {
        return categoryRepository.searchCategories(keyword);
    }

    @Override
    public List<HelpCategory> getPopularCategories(int limit) {
        List<Object[]> results = categoryRepository.findPopularCategories();
        return results.stream()
            .limit(limit)
            .map(result -> (HelpCategory) result[0])
            .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void moveCategory(Long id, Long newParentId, Integer newSortOrder) {
        log.info("移动帮助分类: {} 到父分类: {}", id, newParentId);
        
        HelpCategory category = getCategoryById(id);
        category.setParentId(newParentId);
        
        if (newSortOrder != null) {
            category.setSortOrder(newSortOrder);
        }
        
        category.setUpdatedAt(LocalDateTime.now());
        categoryRepository.save(category);
        
        // 重建路径
        rebuildCategoryPath(id);
    }

    @Override
    @Transactional
    public void adjustCategorySortOrder(Long id, Integer newSortOrder) {
        log.info("调整帮助分类排序: {} 新排序: {}", id, newSortOrder);
        
        HelpCategory category = getCategoryById(id);
        category.setSortOrder(newSortOrder);
        category.setUpdatedAt(LocalDateTime.now());
        categoryRepository.save(category);
    }

    @Override
    @Transactional
    public void batchUpdateCategoryStatus(List<Long> ids, boolean enabled) {
        log.info("批量更新帮助分类状态: {} 启用: {}", ids, enabled);
        categoryRepository.batchUpdateEnabled(ids, enabled);
    }

    @Override
    @Transactional
    public void batchDeleteCategories(List<Long> ids) {
        log.info("批量删除帮助分类: {}", ids);
        categoryRepository.batchSoftDelete(ids);
    }

    @Override
    @Transactional
    public void rebuildCategoryPaths() {
        log.info("重建所有分类路径");
        
        List<HelpCategory> categories = categoryRepository.findCategoriesNeedingPathRebuild();
        for (HelpCategory category : categories) {
            rebuildCategoryPath(category.getId());
        }
    }

    @Override
    @Transactional
    public void rebuildCategoryPath(Long id) {
        HelpCategory category = getCategoryById(id);
        
        if (category.getParentId() != null) {
            HelpCategory parent = getCategoryById(category.getParentId());
            category.setLevel(parent.getLevel() + 1);
            category.setPath(parent.getPath() + "/" + category.getName());
        } else {
            category.setLevel(0);
            category.setPath("/" + category.getName());
        }
        
        category.setUpdatedAt(LocalDateTime.now());
        categoryRepository.save(category);
        
        // 递归更新子分类路径
        List<HelpCategory> children = categoryRepository.findByParentIdAndDeletedFalseOrderBySortOrderAsc(id);
        for (HelpCategory child : children) {
            rebuildCategoryPath(child.getId());
        }
    }

    @Override
    public void validateCategory(HelpCategory category) {
        if (category.getName() == null || category.getName().trim().isEmpty()) {
            throw new RuntimeException("分类名称不能为空");
        }
        
        // 检查名称是否重复
        if (category.getId() == null) {
            if (isCategoryNameExists(category.getName())) {
                throw new RuntimeException("分类名称已存在: " + category.getName());
            }
        } else {
            if (isCategoryNameExists(category.getName(), category.getId())) {
                throw new RuntimeException("分类名称已存在: " + category.getName());
            }
        }
        
        // 检查父分类是否存在
        if (category.getParentId() != null) {
            if (!categoryRepository.existsById(category.getParentId())) {
                throw new RuntimeException("父分类不存在: " + category.getParentId());
            }
            
            // 检查是否形成循环引用
            if (category.getId() != null && isCircularReference(category.getId(), category.getParentId())) {
                throw new RuntimeException("不能设置自己或子分类为父分类");
            }
        }
    }

    @Override
    public boolean isCategoryNameExists(String name) {
        return categoryRepository.existsByNameAndDeletedFalse(name);
    }

    @Override
    public boolean isCategoryNameExists(String name, Long excludeId) {
        return categoryRepository.existsByNameAndIdNotAndDeletedFalse(name, excludeId);
    }

    @Override
    public boolean isCategoryPathExists(String path) {
        return categoryRepository.existsByPathAndDeletedFalse(path);
    }

    @Override
    public boolean isCategoryPathExists(String path, Long excludeId) {
        return categoryRepository.existsByPathAndIdNotAndDeletedFalse(path, excludeId);
    }

    @Override
    public Object getCategoryStatistics() {
        // 实现分类统计逻辑
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalCategories", categoryRepository.count());
        stats.put("enabledCategories", categoryRepository.findByEnabledTrueAndDeletedFalseOrderByLevelAscSortOrderAsc().size());
        stats.put("visibleCategories", categoryRepository.findByVisibleTrueAndEnabledTrueAndDeletedFalseOrderBySortOrderAsc().size());
        return stats;
    }

    @Override
    public Long getCategoryContentCount(Long categoryId) {
        return categoryRepository.countContentsByCategoryId(categoryId);
    }

    @Override
    public Long getCategoryTreeContentCount(Long categoryId) {
        HelpCategory category = getCategoryById(categoryId);
        return categoryRepository.countContentsInCategoryTree(categoryId, category.getPath());
    }

    @Override
    public byte[] exportCategories() {
        // 实现导出逻辑
        throw new UnsupportedOperationException("导出功能待实现");
    }

    @Override
    public void importCategories(byte[] data) {
        // 实现导入逻辑
        throw new UnsupportedOperationException("导入功能待实现");
    }

    // 私有辅助方法
    
    private List<HelpCategory> buildCategoryTree(List<HelpCategory> categories) {
        Map<Long, HelpCategory> categoryMap = categories.stream()
            .collect(Collectors.toMap(HelpCategory::getId, c -> c));
        
        List<HelpCategory> rootCategories = new ArrayList<>();
        
        for (HelpCategory category : categories) {
            if (category.getParentId() == null) {
                rootCategories.add(buildSingleCategoryTree(category, categoryMap));
            }
        }
        
        return rootCategories;
    }
    
    private HelpCategory buildSingleCategoryTree(HelpCategory category, Map<Long, HelpCategory> categoryMap) {
        List<HelpCategory> children = categoryMap.values().stream()
            .filter(c -> Objects.equals(c.getParentId(), category.getId()))
            .sorted(Comparator.comparing(HelpCategory::getSortOrder))
            .map(c -> buildSingleCategoryTree(c, categoryMap))
            .collect(Collectors.toList());
        
        category.setChildren(children);
        return category;
    }
    
    private boolean isCircularReference(Long categoryId, Long parentId) {
        if (categoryId.equals(parentId)) {
            return true;
        }
        
        HelpCategory parent = getCategoryById(parentId);
        if (parent.getParentId() != null) {
            return isCircularReference(categoryId, parent.getParentId());
        }
        
        return false;
    }
}
