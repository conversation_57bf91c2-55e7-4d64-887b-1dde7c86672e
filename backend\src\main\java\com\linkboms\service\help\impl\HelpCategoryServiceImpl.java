package com.linkboms.service.help.impl;

import com.linkboms.entity.help.HelpCategory;
import com.linkboms.repository.help.HelpCategoryRepository;
import com.linkboms.service.help.HelpCategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 帮助分类服务实现类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class HelpCategoryServiceImpl implements HelpCategoryService {

    private final HelpCategoryRepository categoryRepository;

    @Override
    @Transactional
    public HelpCategory createCategory(HelpCategory category) {
        log.info("创建帮助分类: {}", category.getName());
        
        // 验证分类数据
        validateCategoryForCreate(category);
        
        // 设置层级和路径
        if (category.getParentId() != null) {
            HelpCategory parent = getCategoryById(category.getParentId());
            category.setLevel(parent.getLevel() + 1);
            category.setPath(parent.getPath() + "/" + category.getName());
        } else {
            category.setLevel(0);
            category.setPath("/" + category.getName());
        }
        
        // 设置排序号
        if (category.getSortOrder() == null) {
            category.setSortOrder(getNextSortOrder(category.getParentId()));
        }
        
        // 设置默认值
        if (category.getEnabled() == null) {
            category.setEnabled(true);
        }
        if (category.getVisible() == null) {
            category.setVisible(true);
        }
        if (category.getContentCount() == null) {
            category.setContentCount(0L);
        }
        if (category.getDeleted() == null) {
            category.setDeleted(false);
        }
        
        category.setCreatedAt(LocalDateTime.now());
        category.setUpdatedAt(LocalDateTime.now());
        
        return categoryRepository.save(category);
    }

    @Override
    @Transactional
    public HelpCategory updateCategory(Long id, HelpCategory category) {
        log.info("更新帮助分类: {}", id);
        
        HelpCategory existingCategory = getCategoryById(id);
        
        // 验证更新数据
        validateCategoryForUpdate(id, category);
        
        // 更新基本信息
        existingCategory.setName(category.getName());
        existingCategory.setDescription(category.getDescription());
        existingCategory.setIcon(category.getIcon());
        existingCategory.setSortOrder(category.getSortOrder());
        existingCategory.setEnabled(category.getEnabled());
        existingCategory.setVisible(category.getVisible());
        existingCategory.setSeoTitle(category.getSeoTitle());
        existingCategory.setSeoDescription(category.getSeoDescription());
        existingCategory.setSeoKeywords(category.getSeoKeywords());
        existingCategory.setPermissions(category.getPermissions());
        existingCategory.setUpdatedAt(LocalDateTime.now());
        
        // 如果父分类发生变化，需要重新计算路径和层级
        if (!Objects.equals(existingCategory.getParentId(), category.getParentId())) {
            moveCategory(id, category.getParentId());
        } else {
            // 如果名称发生变化，需要更新路径
            if (!Objects.equals(existingCategory.getName(), category.getName())) {
                updateCategoryPath(existingCategory);
            }
        }
        
        return categoryRepository.save(existingCategory);
    }

    @Override
    @Transactional
    public void deleteCategory(Long id) {
        log.info("软删除帮助分类: {}", id);
        
        HelpCategory category = getCategoryById(id);
        
        // 检查是否有子分类
        List<HelpCategory> children = getChildCategories(id);
        if (!children.isEmpty()) {
            throw new IllegalStateException("无法删除包含子分类的分类");
        }
        
        // 检查是否有内容
        if (category.getContentCount() > 0) {
            throw new IllegalStateException("无法删除包含内容的分类");
        }
        
        category.setDeleted(true);
        category.setUpdatedAt(LocalDateTime.now());
        categoryRepository.save(category);
    }

    @Override
    @Transactional
    public void permanentDeleteCategory(Long id) {
        log.info("永久删除帮助分类: {}", id);
        
        HelpCategory category = getCategoryById(id);
        
        // 检查是否有子分类
        List<HelpCategory> children = getChildCategories(id);
        if (!children.isEmpty()) {
            throw new IllegalStateException("无法删除包含子分类的分类");
        }
        
        // 检查是否有内容
        if (category.getContentCount() > 0) {
            throw new IllegalStateException("无法删除包含内容的分类");
        }
        
        categoryRepository.delete(category);
    }

    @Override
    @Transactional
    public void restoreCategory(Long id) {
        log.info("恢复帮助分类: {}", id);
        
        HelpCategory category = getCategoryById(id);
        category.setDeleted(false);
        category.setUpdatedAt(LocalDateTime.now());
        categoryRepository.save(category);
    }

    @Override
    public HelpCategory getCategoryById(Long id) {
        return categoryRepository.findById(id)
            .orElseThrow(() -> new IllegalArgumentException("分类不存在: " + id));
    }

    @Override
    public Page<HelpCategory> getCategoryList(Pageable pageable) {
        return categoryRepository.findByDeletedFalse(pageable);
    }

    @Override
    public List<HelpCategory> getCategoryTree() {
        List<HelpCategory> allCategories = categoryRepository.findByDeletedFalseOrderBySortOrderAsc();
        return buildCategoryTree(allCategories, null);
    }

    @Override
    public List<HelpCategory> getRootCategories() {
        return categoryRepository.findRootCategories();
    }

    @Override
    public List<HelpCategory> getChildCategories(Long parentId) {
        return categoryRepository.findByParentIdAndDeletedFalseOrderBySortOrderAsc(parentId);
    }

    @Override
    public List<HelpCategory> getDescendantCategories(Long parentId) {
        return categoryRepository.findAllDescendants(parentId);
    }

    @Override
    public List<HelpCategory> getCategoriesByLevel(Integer level) {
        return categoryRepository.findByLevelAndDeletedFalseOrderBySortOrderAsc(level);
    }

    @Override
    public List<HelpCategory> getVisibleCategories() {
        return categoryRepository.findVisibleCategories();
    }

    @Override
    public List<HelpCategory> getEnabledCategories() {
        return categoryRepository.findEnabledCategories();
    }

    @Override
    public List<HelpCategory> getCategoriesWithContent() {
        return categoryRepository.findCategoriesWithContent();
    }

    @Override
    public List<HelpCategory> searchCategories(String keyword) {
        return categoryRepository.searchByKeyword(keyword);
    }

    @Override
    public HelpCategory getCategoryByName(String name) {
        return categoryRepository.findByNameAndDeletedFalse(name)
            .orElseThrow(() -> new IllegalArgumentException("分类不存在: " + name));
    }

    @Override
    public HelpCategory getCategoryByPath(String path) {
        return categoryRepository.findByPathAndDeletedFalse(path)
            .orElseThrow(() -> new IllegalArgumentException("分类路径不存在: " + path));
    }

    @Override
    @Transactional
    public void moveCategory(Long id, Long newParentId) {
        log.info("移动帮助分类: {} 到父分类: {}", id, newParentId);
        
        HelpCategory category = getCategoryById(id);
        
        // 验证移动操作
        if (newParentId != null) {
            HelpCategory newParent = getCategoryById(newParentId);
            
            // 检查是否会形成循环引用
            if (isAncestorOf(id, newParentId)) {
                throw new IllegalArgumentException("无法将分类移动到其子分类下");
            }
            
            category.setParentId(newParentId);
            category.setLevel(newParent.getLevel() + 1);
            category.setPath(newParent.getPath() + "/" + category.getName());
        } else {
            category.setParentId(null);
            category.setLevel(0);
            category.setPath("/" + category.getName());
        }
        
        category.setUpdatedAt(LocalDateTime.now());
        categoryRepository.save(category);
        
        // 更新所有子分类的路径和层级
        updateDescendantPaths(category);
    }

    @Override
    @Transactional
    public void setCategorySortOrder(Long id, Integer sortOrder) {
        log.info("设置帮助分类排序: {}, 排序号: {}", id, sortOrder);
        
        HelpCategory category = getCategoryById(id);
        category.setSortOrder(sortOrder);
        category.setUpdatedAt(LocalDateTime.now());
        categoryRepository.save(category);
    }

    @Override
    @Transactional
    public void sortCategories(List<Map<String, Object>> sortData) {
        log.info("批量排序帮助分类: {}", sortData.size());
        
        for (Map<String, Object> data : sortData) {
            Long id = Long.valueOf(data.get("id").toString());
            Integer sortOrder = Integer.valueOf(data.get("sortOrder").toString());
            setCategorySortOrder(id, sortOrder);
        }
    }

    @Override
    @Transactional
    public void enableCategory(Long id) {
        log.info("启用帮助分类: {}", id);
        
        HelpCategory category = getCategoryById(id);
        category.setEnabled(true);
        category.setUpdatedAt(LocalDateTime.now());
        categoryRepository.save(category);
    }

    @Override
    @Transactional
    public void disableCategory(Long id) {
        log.info("禁用帮助分类: {}", id);
        
        HelpCategory category = getCategoryById(id);
        category.setEnabled(false);
        category.setUpdatedAt(LocalDateTime.now());
        categoryRepository.save(category);
    }

    @Override
    @Transactional
    public void setCategoryVisibility(Long id, boolean visible) {
        log.info("设置帮助分类可见性: {}, 可见: {}", id, visible);
        
        HelpCategory category = getCategoryById(id);
        category.setVisible(visible);
        category.setUpdatedAt(LocalDateTime.now());
        categoryRepository.save(category);
    }

    @Override
    @Transactional
    public void incrementContentCount(Long id) {
        categoryRepository.incrementContentCount(id);
    }

    @Override
    @Transactional
    public void decrementContentCount(Long id) {
        categoryRepository.decrementContentCount(id);
    }

    @Override
    @Transactional
    public void updateContentCount(Long id, Long count) {
        categoryRepository.updateContentCount(id, count);
    }

    @Override
    @Transactional
    public HelpCategory copyCategory(Long id, String newName, boolean includeChildren) {
        log.info("复制帮助分类: {}, 新名称: {}, 包含子分类: {}", id, newName, includeChildren);
        
        HelpCategory originalCategory = getCategoryById(id);
        
        // 创建副本
        HelpCategory copiedCategory = new HelpCategory();
        copiedCategory.setName(newName);
        copiedCategory.setDescription(originalCategory.getDescription());
        copiedCategory.setIcon(originalCategory.getIcon());
        copiedCategory.setParentId(originalCategory.getParentId());
        copiedCategory.setLevel(originalCategory.getLevel());
        copiedCategory.setEnabled(originalCategory.getEnabled());
        copiedCategory.setVisible(originalCategory.getVisible());
        copiedCategory.setSeoTitle(originalCategory.getSeoTitle());
        copiedCategory.setSeoDescription(originalCategory.getSeoDescription());
        copiedCategory.setSeoKeywords(originalCategory.getSeoKeywords());
        copiedCategory.setPermissions(originalCategory.getPermissions());
        copiedCategory.setContentCount(0L);
        copiedCategory.setDeleted(false);
        
        copiedCategory = createCategory(copiedCategory);
        
        // 如果需要复制子分类
        if (includeChildren) {
            List<HelpCategory> children = getChildCategories(id);
            for (HelpCategory child : children) {
                HelpCategory copiedChild = copyCategory(child.getId(), child.getName(), true);
                copiedChild.setParentId(copiedCategory.getId());
                updateCategory(copiedChild.getId(), copiedChild);
            }
        }
        
        return copiedCategory;
    }

    @Override
    public String getCategoryPath(Long id) {
        HelpCategory category = getCategoryById(id);
        return category.getPath();
    }

    @Override
    public List<HelpCategory> getCategoryBreadcrumb(Long id) {
        return getParentChain(id);
    }

    @Override
    public List<HelpCategory> getParentChain(Long id) {
        List<HelpCategory> parentChain = new ArrayList<>();
        HelpCategory current = getCategoryById(id);
        
        while (current != null) {
            parentChain.add(0, current);
            if (current.getParentId() != null) {
                current = getCategoryById(current.getParentId());
            } else {
                break;
            }
        }
        
        return parentChain;
    }

    @Override
    public boolean isParentOf(Long parentId, Long childId) {
        HelpCategory child = getCategoryById(childId);
        return Objects.equals(child.getParentId(), parentId);
    }

    @Override
    public boolean isAncestorOf(Long ancestorId, Long descendantId) {
        HelpCategory current = getCategoryById(descendantId);
        
        while (current.getParentId() != null) {
            if (Objects.equals(current.getParentId(), ancestorId)) {
                return true;
            }
            current = getCategoryById(current.getParentId());
        }
        
        return false;
    }

    @Override
    public List<String> validateCategory(HelpCategory category) {
        List<String> errors = new ArrayList<>();
        
        // 验证名称
        if (category.getName() == null || category.getName().trim().isEmpty()) {
            errors.add("分类名称不能为空");
        } else if (category.getName().length() > 100) {
            errors.add("分类名称长度不能超过100个字符");
        }
        
        // 验证描述
        if (category.getDescription() != null && category.getDescription().length() > 500) {
            errors.add("分类描述长度不能超过500个字符");
        }
        
        // 验证父分类
        if (category.getParentId() != null) {
            try {
                getCategoryById(category.getParentId());
            } catch (IllegalArgumentException e) {
                errors.add("父分类不存在");
            }
        }
        
        // 验证排序号
        if (category.getSortOrder() != null && category.getSortOrder() < 0) {
            errors.add("排序号不能为负数");
        }
        
        return errors;
    }

    @Override
    public Map<String, Object> getCategoryStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        statistics.put("totalCount", categoryRepository.countByDeletedFalse());
        statistics.put("enabledCount", categoryRepository.countEnabledCategories());
        statistics.put("visibleCount", categoryRepository.countVisibleCategories());
        statistics.put("withContentCount", categoryRepository.countCategoriesWithContent());
        statistics.put("rootCount", categoryRepository.countRootCategories());
        statistics.put("leafCount", categoryRepository.countLeafCategories());
        
        return statistics;
    }

    @Override
    public Map<Integer, Long> getCategoryStatisticsByLevel() {
        return categoryRepository.countByLevel();
    }

    @Override
    public Map<String, Long> getContentCountByCategory() {
        List<HelpCategory> categories = categoryRepository.findByDeletedFalseOrderBySortOrderAsc();
        return categories.stream()
            .collect(Collectors.toMap(
                HelpCategory::getName,
                HelpCategory::getContentCount
            ));
    }

    @Override
    @Transactional
    public void rebuildCategoryPaths() {
        log.info("重建帮助分类路径");
        
        List<HelpCategory> rootCategories = getRootCategories();
        for (HelpCategory root : rootCategories) {
            rebuildCategoryPath(root);
        }
    }

    @Override
    @Transactional
    public void rebuildCategoryLevels() {
        log.info("重建帮助分类层级");
        
        List<HelpCategory> rootCategories = getRootCategories();
        for (HelpCategory root : rootCategories) {
            rebuildCategoryLevel(root, 0);
        }
    }

    @Override
    @Transactional
    public void cleanupCategories() {
        log.info("清理帮助分类");
        
        // 清理空分类（没有内容且没有子分类）
        List<HelpCategory> emptyCategories = categoryRepository.findEmptyCategories();
        for (HelpCategory category : emptyCategories) {
            if (getChildCategories(category.getId()).isEmpty()) {
                permanentDeleteCategory(category.getId());
            }
        }
    }

    @Override
    public byte[] exportCategories(String format) {
        log.info("导出帮助分类，格式: {}", format);
        
        // TODO: 实现分类导出功能
        throw new UnsupportedOperationException("分类导出功能待实现");
    }

    @Override
    @Transactional
    public List<HelpCategory> importCategories(MultipartFile file) {
        log.info("导入帮助分类: {}", file.getOriginalFilename());
        
        // TODO: 实现分类导入功能
        throw new UnsupportedOperationException("分类导入功能待实现");
    }

    @Override
    public Integer getNextSortOrder(Long parentId) {
        return categoryRepository.findMaxSortOrderByParentId(parentId)
            .map(max -> max + 1)
            .orElse(1);
    }

    @Override
    public boolean checkNameExists(String name, Long parentId, Long excludeId) {
        if (excludeId != null) {
            return categoryRepository.existsByNameAndParentIdAndIdNotAndDeletedFalse(name, parentId, excludeId);
        } else {
            return categoryRepository.existsByNameAndParentIdAndDeletedFalse(name, parentId);
        }
    }

    @Override
    public boolean checkPathExists(String path, Long excludeId) {
        if (excludeId != null) {
            return categoryRepository.existsByPathAndIdNotAndDeletedFalse(path, excludeId);
        } else {
            return categoryRepository.existsByPathAndDeletedFalse(path);
        }
    }

    // 私有辅助方法
    
    private void validateCategoryForCreate(HelpCategory category) {
        List<String> errors = validateCategory(category);
        
        // 检查名称是否重复
        if (checkNameExists(category.getName(), category.getParentId(), null)) {
            errors.add("同级分类中已存在相同名称的分类");
        }
        
        if (!errors.isEmpty()) {
            throw new IllegalArgumentException("分类验证失败: " + String.join(", ", errors));
        }
    }
    
    private void validateCategoryForUpdate(Long id, HelpCategory category) {
        List<String> errors = validateCategory(category);
        
        // 检查名称是否重复
        if (checkNameExists(category.getName(), category.getParentId(), id)) {
            errors.add("同级分类中已存在相同名称的分类");
        }
        
        if (!errors.isEmpty()) {
            throw new IllegalArgumentException("分类验证失败: " + String.join(", ", errors));
        }
    }
    
    private List<HelpCategory> buildCategoryTree(List<HelpCategory> allCategories, Long parentId) {
        return allCategories.stream()
            .filter(category -> Objects.equals(category.getParentId(), parentId))
            .peek(category -> {
                List<HelpCategory> children = buildCategoryTree(allCategories, category.getId());
                category.setChildren(children);
            })
            .collect(Collectors.toList());
    }
    
    private void updateCategoryPath(HelpCategory category) {
        if (category.getParentId() != null) {
            HelpCategory parent = getCategoryById(category.getParentId());
            category.setPath(parent.getPath() + "/" + category.getName());
        } else {
            category.setPath("/" + category.getName());
        }
    }
    
    private void updateDescendantPaths(HelpCategory parent) {
        List<HelpCategory> children = getChildCategories(parent.getId());
        for (HelpCategory child : children) {
            child.setLevel(parent.getLevel() + 1);
            child.setPath(parent.getPath() + "/" + child.getName());
            child.setUpdatedAt(LocalDateTime.now());
            categoryRepository.save(child);
            
            // 递归更新子分类
            updateDescendantPaths(child);
        }
    }
    
    private void rebuildCategoryPath(HelpCategory category) {
        updateCategoryPath(category);
        categoryRepository.save(category);
        
        List<HelpCategory> children = getChildCategories(category.getId());
        for (HelpCategory child : children) {
            rebuildCategoryPath(child);
        }
    }
    
    private void rebuildCategoryLevel(HelpCategory category, int level) {
        category.setLevel(level);
        categoryRepository.save(category);
        
        List<HelpCategory> children = getChildCategories(category.getId());
        for (HelpCategory child : children) {
            rebuildCategoryLevel(child, level + 1);
        }
    }
}