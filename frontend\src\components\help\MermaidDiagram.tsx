import React, { useEffect, useRef, useState } from 'react';
import { Card, Button, Space, message, Spin, Alert } from 'antd';
import {
  FullscreenOutlined,
  DownloadOutlined,
  CopyOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  ReloadOutlined
} from '@ant-design/icons';

interface MermaidDiagramProps {
  definition: string;
  title?: string;
  height?: number;
  showControls?: boolean;
  className?: string;
}

/**
 * Mermaid 图表组件
 */
const MermaidDiagram: React.FC<MermaidDiagramProps> = ({
  definition,
  title,
  height = 400,
  showControls = true,
  className
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [zoom, setZoom] = useState(1);
  const [mermaid, setMermaid] = useState<any>(null);

  // 动态加载 Mermaid
  useEffect(() => {
    const loadMermaid = async () => {
      try {
        // 动态导入 mermaid
        const mermaidModule = await import('mermaid');
        const mermaidInstance = mermaidModule.default;
        
        // 初始化 Mermaid
        mermaidInstance.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
          fontFamily: 'Arial, sans-serif',
          fontSize: 14,
          flowchart: {
            useMaxWidth: true,
            htmlLabels: true,
            curve: 'basis'
          },
          sequence: {
            diagramMarginX: 50,
            diagramMarginY: 10,
            actorMargin: 50,
            width: 150,
            height: 65,
            boxMargin: 10,
            boxTextMargin: 5,
            noteMargin: 10,
            messageMargin: 35,
            mirrorActors: true,
            bottomMarginAdj: 1,
            useMaxWidth: true
          },
          gantt: {
            titleTopMargin: 25,
            barHeight: 20,
            fontFamily: 'Arial, sans-serif',
            fontSize: 11,
            gridLineStartPadding: 35,
            bottomPadding: 25,
            leftPadding: 75,
            topPadding: 50,
            rightPadding: 25
          }
        });
        
        setMermaid(mermaidInstance);
      } catch (err) {
        console.error('Failed to load Mermaid:', err);
        setError('无法加载图表库');
      }
    };

    loadMermaid();
  }, []);

  // 渲染图表
  useEffect(() => {
    if (!mermaid || !definition || !containerRef.current) {
      return;
    }

    const renderDiagram = async () => {
      setLoading(true);
      setError(null);

      try {
        // 清空容器
        const container = containerRef.current;
        if (container) {
          container.innerHTML = '';
        }

        // 生成唯一ID
        const id = `mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        
        // 验证语法
        const isValid = await mermaid.parse(definition);
        if (!isValid) {
          throw new Error('图表语法错误');
        }

        // 渲染图表
        const { svg } = await mermaid.render(id, definition);
        
        if (container) {
          container.innerHTML = svg;
          
          // 应用缩放
          const svgElement = container.querySelector('svg');
          if (svgElement) {
            svgElement.style.transform = `scale(${zoom})`;
            svgElement.style.transformOrigin = 'top left';
            svgElement.style.maxWidth = '100%';
            svgElement.style.height = 'auto';
          }
        }
        
        setLoading(false);
      } catch (err) {
        console.error('Mermaid render error:', err);
        setError(err instanceof Error ? err.message : '图表渲染失败');
        setLoading(false);
      }
    };

    renderDiagram();
  }, [mermaid, definition, zoom]);

  // 处理全屏
  const handleFullscreen = () => {
    if (containerRef.current) {
      if (containerRef.current.requestFullscreen) {
        containerRef.current.requestFullscreen();
      }
    }
  };

  // 处理下载
  const handleDownload = () => {
    if (!containerRef.current) return;

    const svgElement = containerRef.current.querySelector('svg');
    if (!svgElement) return;

    try {
      // 创建 SVG 数据
      const svgData = new XMLSerializer().serializeToString(svgElement);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      
      // 创建下载链接
      const url = URL.createObjectURL(svgBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${title || 'diagram'}.svg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      message.success('图表下载成功');
    } catch (err) {
      message.error('下载失败');
    }
  };

  // 处理复制
  const handleCopy = () => {
    navigator.clipboard.writeText(definition).then(() => {
      message.success('图表定义已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败');
    });
  };

  // 处理缩放
  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.1, 2));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.1, 0.5));
  };

  const handleResetZoom = () => {
    setZoom(1);
  };

  // 处理刷新
  const handleRefresh = () => {
    if (mermaid && definition && containerRef.current) {
      setZoom(1);
      // 触发重新渲染
      const container = containerRef.current;
      container.innerHTML = '';
      // 延迟一下再重新渲染
      setTimeout(() => {
        setLoading(true);
      }, 100);
    }
  };

  return (
    <Card
      title={title}
      className={className}
      extra={
        showControls && (
          <Space>
            <Button
              size="small"
              icon={<ZoomOutOutlined />}
              onClick={handleZoomOut}
              disabled={zoom <= 0.5}
              title="缩小"
            />
            <Button
              size="small"
              onClick={handleResetZoom}
              title="重置缩放"
            >
              {Math.round(zoom * 100)}%
            </Button>
            <Button
              size="small"
              icon={<ZoomInOutlined />}
              onClick={handleZoomIn}
              disabled={zoom >= 2}
              title="放大"
            />
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
              title="刷新"
            />
            <Button
              size="small"
              icon={<CopyOutlined />}
              onClick={handleCopy}
              title="复制定义"
            />
            <Button
              size="small"
              icon={<DownloadOutlined />}
              onClick={handleDownload}
              title="下载SVG"
            />
            <Button
              size="small"
              icon={<FullscreenOutlined />}
              onClick={handleFullscreen}
              title="全屏"
            />
          </Space>
        )
      }
    >
      <div style={{ position: 'relative', minHeight: height }}>
        {loading && (
          <div
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              zIndex: 10
            }}
          >
            <Spin size="large" />
          </div>
        )}
        
        {error && (
          <Alert
            message="图表渲染错误"
            description={error}
            type="error"
            showIcon
            style={{ margin: '20px 0' }}
            action={
              <Button size="small" onClick={handleRefresh}>
                重试
              </Button>
            }
          />
        )}
        
        <div
          ref={containerRef}
          style={{
            width: '100%',
            minHeight: height,
            overflow: 'auto',
            border: '1px solid #f0f0f0',
            borderRadius: '6px',
            padding: '16px',
            background: '#fafafa'
          }}
        />
      </div>
    </Card>
  );
};

export default MermaidDiagram;
