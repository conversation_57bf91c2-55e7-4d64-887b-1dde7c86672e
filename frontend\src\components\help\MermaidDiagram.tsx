import React, { useEffect, useRef, useState } from 'react';
import { Card, Button, Space, Alert, Spin, message, Modal, Select } from 'antd';
import { 
  FullscreenOutlined, 
  DownloadOutlined, 
  ZoomInOutlined,
  ZoomOutOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons';
import mermaid from 'mermaid';

const { Option } = Select;

interface MermaidDiagramProps {
  definition: string;
  title?: string;
  height?: number;
  theme?: 'default' | 'dark' | 'forest' | 'neutral';
  onFullscreen?: () => void;
  onDownload?: () => void;
}

const MermaidDiagram: React.FC<MermaidDiagramProps> = ({
  definition,
  title,
  height = 400,
  theme = 'default',
  onFullscreen,
  onDownload,
}) => {
  const diagramRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [zoom, setZoom] = useState(1);
  const [currentTheme, setCurrentTheme] = useState(theme);
  const [showSettings, setShowSettings] = useState(false);
  const [diagramId] = useState(`mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);

  useEffect(() => {
    initializeMermaid();
  }, []);

  useEffect(() => {
    if (definition) {
      renderDiagram();
    }
  }, [definition, currentTheme]);

  const initializeMermaid = () => {
    try {
      mermaid.initialize({
        startOnLoad: false,
        theme: currentTheme,
        securityLevel: 'loose',
        flowchart: {
          useMaxWidth: true,
          htmlLabels: true,
          curve: 'basis',
        },
        sequence: {
          diagramMarginX: 50,
          diagramMarginY: 10,
          actorMargin: 50,
          width: 150,
          height: 65,
          boxMargin: 10,
          boxTextMargin: 5,
          noteMargin: 10,
          messageMargin: 35,
          mirrorActors: true,
          bottomMarginAdj: 1,
          useMaxWidth: true,
        },
        gantt: {
          titleTopMargin: 25,
          barHeight: 20,
          fontSize: 11,
          gridLineStartPadding: 35,
          leftPadding: 75,
          topPadding: 50,
          rightPadding: 25,
        },
        class: {
          useMaxWidth: true,
        },
        pie: {
          useMaxWidth: true,
        },
        er: {
          useMaxWidth: true,
        },
        journey: {
          useMaxWidth: true,
        },
      });
      setLoading(false);
    } catch (err) {
      console.error('Failed to initialize Mermaid:', err);
      setError('图表渲染引擎初始化失败');
      setLoading(false);
    }
  };

  const renderDiagram = async () => {
    if (!diagramRef.current || !definition) return;

    setLoading(true);
    setError(null);

    try {
      // 清空容器
      diagramRef.current.innerHTML = '';

      // 验证图表定义
      const isValid = await mermaid.parse(definition);
      if (!isValid) {
        throw new Error('图表定义格式不正确');
      }

      // 渲染图表
      const { svg } = await mermaid.render(diagramId, definition);
      
      if (diagramRef.current) {
        diagramRef.current.innerHTML = svg;
        
        // 应用缩放
        const svgElement = diagramRef.current.querySelector('svg');
        if (svgElement) {
          svgElement.style.transform = `scale(${zoom})`;
          svgElement.style.transformOrigin = 'top left';
          svgElement.style.maxWidth = '100%';
          svgElement.style.height = 'auto';
        }
      }
    } catch (err) {
      console.error('Failed to render diagram:', err);
      setError(err instanceof Error ? err.message : '图表渲染失败');
    } finally {
      setLoading(false);
    }
  };

  const handleZoomIn = () => {
    const newZoom = Math.min(zoom + 0.2, 3);
    setZoom(newZoom);
    applyZoom(newZoom);
  };

  const handleZoomOut = () => {
    const newZoom = Math.max(zoom - 0.2, 0.2);
    setZoom(newZoom);
    applyZoom(newZoom);
  };

  const handleResetZoom = () => {
    setZoom(1);
    applyZoom(1);
  };

  const applyZoom = (zoomLevel: number) => {
    if (diagramRef.current) {
      const svgElement = diagramRef.current.querySelector('svg');
      if (svgElement) {
        svgElement.style.transform = `scale(${zoomLevel})`;
      }
    }
  };

  const handleThemeChange = (newTheme: string) => {
    const validTheme = newTheme as 'default' | 'dark' | 'forest' | 'neutral';
    setCurrentTheme(validTheme);
    mermaid.initialize({
      theme: validTheme,
      startOnLoad: false,
    });
  };

  const handleDownload = () => {
    if (!diagramRef.current) return;

    const svgElement = diagramRef.current.querySelector('svg');
    if (!svgElement) return;

    try {
      // 创建一个新的SVG元素用于下载
      const svgClone = svgElement.cloneNode(true) as SVGElement;
      svgClone.style.transform = 'none'; // 移除缩放变换
      
      const svgData = new XMLSerializer().serializeToString(svgClone);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      const svgUrl = URL.createObjectURL(svgBlob);
      
      const downloadLink = document.createElement('a');
      downloadLink.href = svgUrl;
      downloadLink.download = `${title || 'diagram'}.svg`;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
      URL.revokeObjectURL(svgUrl);
      
      message.success('图表已下载');
    } catch (err) {
      console.error('Download failed:', err);
      message.error('下载失败');
    }

    onDownload?.();
  };

  const handleFullscreen = () => {
    if (!diagramRef.current) return;

    try {
      if (diagramRef.current.requestFullscreen) {
        diagramRef.current.requestFullscreen();
      } else if ((diagramRef.current as any).webkitRequestFullscreen) {
        (diagramRef.current as any).webkitRequestFullscreen();
      } else if ((diagramRef.current as any).msRequestFullscreen) {
        (diagramRef.current as any).msRequestFullscreen();
      }
    } catch (err) {
      console.error('Fullscreen failed:', err);
      message.error('全屏模式不支持');
    }

    onFullscreen?.();
  };

  const renderControls = () => (
    <Space>
      <Button 
        icon={<ZoomOutOutlined />} 
        onClick={handleZoomOut}
        disabled={zoom <= 0.2}
        size="small"
      />
      <span style={{ fontSize: '12px', minWidth: '40px', textAlign: 'center' }}>
        {Math.round(zoom * 100)}%
      </span>
      <Button 
        icon={<ZoomInOutlined />} 
        onClick={handleZoomIn}
        disabled={zoom >= 3}
        size="small"
      />
      <Button 
        icon={<ReloadOutlined />} 
        onClick={handleResetZoom}
        size="small"
        title="重置缩放"
      />
      <Button 
        icon={<SettingOutlined />} 
        onClick={() => setShowSettings(true)}
        size="small"
        title="设置"
      />
      <Button 
        icon={<DownloadOutlined />} 
        onClick={handleDownload}
        size="small"
        title="下载SVG"
      />
      <Button 
        icon={<FullscreenOutlined />} 
        onClick={handleFullscreen}
        size="small"
        title="全屏查看"
      />
    </Space>
  );

  return (
    <>
      <Card
        title={title}
        extra={renderControls()}
        style={{ width: '100%' }}
      >
        {loading && (
          <div style={{ 
            textAlign: 'center', 
            padding: '50px',
            minHeight: height 
          }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>正在渲染图表...</div>
          </div>
        )}

        {error && (
          <Alert
            message="图表渲染失败"
            description={error}
            type="error"
            showIcon
            action={
              <Button size="small" onClick={renderDiagram}>
                重试
              </Button>
            }
          />
        )}

        {!loading && !error && (
          <div
            ref={diagramRef}
            style={{
              minHeight: height,
              overflow: 'auto',
              border: '1px solid #f0f0f0',
              borderRadius: '6px',
              backgroundColor: '#fafafa',
              padding: '16px',
            }}
          />
        )}
      </Card>

      {/* 设置弹窗 */}
      <Modal
        title="图表设置"
        open={showSettings}
        onCancel={() => setShowSettings(false)}
        footer={[
          <Button key="cancel" onClick={() => setShowSettings(false)}>
            取消
          </Button>,
          <Button 
            key="apply" 
            type="primary" 
            onClick={() => {
              setShowSettings(false);
              renderDiagram();
            }}
          >
            应用
          </Button>,
        ]}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <label>主题:</label>
            <Select
              value={currentTheme}
              onChange={handleThemeChange}
              style={{ width: '100%', marginTop: '8px' }}
            >
              <Option value="default">默认</Option>
              <Option value="dark">深色</Option>
              <Option value="forest">森林</Option>
              <Option value="neutral">中性</Option>
            </Select>
          </div>
        </Space>
      </Modal>
    </>
  );
};

export default MermaidDiagram;
