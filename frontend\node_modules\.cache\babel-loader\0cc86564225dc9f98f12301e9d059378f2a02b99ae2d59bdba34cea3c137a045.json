{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\components\\\\help\\\\MermaidDiagram.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef, useState } from 'react';\nimport { Card, Button, Space, Alert, Spin, message, Modal, Select } from 'antd';\nimport { FullscreenOutlined, DownloadOutlined, ZoomInOutlined, ZoomOutOutlined, ReloadOutlined, SettingOutlined } from '@ant-design/icons';\nimport mermaid from 'mermaid';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst MermaidDiagram = ({\n  definition,\n  title,\n  height = 400,\n  theme = 'default',\n  onFullscreen,\n  onDownload\n}) => {\n  _s();\n  const diagramRef = useRef(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [zoom, setZoom] = useState(1);\n  const [currentTheme, setCurrentTheme] = useState(theme);\n  const [showSettings, setShowSettings] = useState(false);\n  const [diagramId] = useState(`mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);\n  useEffect(() => {\n    initializeMermaid();\n  }, []);\n  useEffect(() => {\n    if (definition) {\n      renderDiagram();\n    }\n  }, [definition, currentTheme]);\n  const initializeMermaid = () => {\n    try {\n      mermaid.initialize({\n        startOnLoad: false,\n        theme: currentTheme,\n        securityLevel: 'loose',\n        flowchart: {\n          useMaxWidth: true,\n          htmlLabels: true,\n          curve: 'basis'\n        },\n        sequence: {\n          diagramMarginX: 50,\n          diagramMarginY: 10,\n          actorMargin: 50,\n          width: 150,\n          height: 65,\n          boxMargin: 10,\n          boxTextMargin: 5,\n          noteMargin: 10,\n          messageMargin: 35,\n          mirrorActors: true,\n          bottomMarginAdj: 1,\n          useMaxWidth: true\n        },\n        gantt: {\n          titleTopMargin: 25,\n          barHeight: 20,\n          fontSize: 11,\n          gridLineStartPadding: 35,\n          leftPadding: 75,\n          topPadding: 50,\n          rightPadding: 25\n        },\n        class: {\n          useMaxWidth: true\n        },\n        git: {\n          useMaxWidth: true\n        },\n        pie: {\n          useMaxWidth: true\n        },\n        er: {\n          useMaxWidth: true\n        },\n        journey: {\n          useMaxWidth: true\n        }\n      });\n      setLoading(false);\n    } catch (err) {\n      console.error('Failed to initialize Mermaid:', err);\n      setError('图表渲染引擎初始化失败');\n      setLoading(false);\n    }\n  };\n  const renderDiagram = async () => {\n    if (!diagramRef.current || !definition) return;\n    setLoading(true);\n    setError(null);\n    try {\n      // 清空容器\n      diagramRef.current.innerHTML = '';\n\n      // 验证图表定义\n      const isValid = await mermaid.parse(definition);\n      if (!isValid) {\n        throw new Error('图表定义格式不正确');\n      }\n\n      // 渲染图表\n      const {\n        svg\n      } = await mermaid.render(diagramId, definition);\n      if (diagramRef.current) {\n        diagramRef.current.innerHTML = svg;\n\n        // 应用缩放\n        const svgElement = diagramRef.current.querySelector('svg');\n        if (svgElement) {\n          svgElement.style.transform = `scale(${zoom})`;\n          svgElement.style.transformOrigin = 'top left';\n          svgElement.style.maxWidth = '100%';\n          svgElement.style.height = 'auto';\n        }\n      }\n    } catch (err) {\n      console.error('Failed to render diagram:', err);\n      setError(err instanceof Error ? err.message : '图表渲染失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleZoomIn = () => {\n    const newZoom = Math.min(zoom + 0.2, 3);\n    setZoom(newZoom);\n    applyZoom(newZoom);\n  };\n  const handleZoomOut = () => {\n    const newZoom = Math.max(zoom - 0.2, 0.2);\n    setZoom(newZoom);\n    applyZoom(newZoom);\n  };\n  const handleResetZoom = () => {\n    setZoom(1);\n    applyZoom(1);\n  };\n  const applyZoom = zoomLevel => {\n    if (diagramRef.current) {\n      const svgElement = diagramRef.current.querySelector('svg');\n      if (svgElement) {\n        svgElement.style.transform = `scale(${zoomLevel})`;\n      }\n    }\n  };\n  const handleThemeChange = newTheme => {\n    const validTheme = newTheme;\n    setCurrentTheme(validTheme);\n    mermaid.initialize({\n      theme: validTheme,\n      startOnLoad: false\n    });\n  };\n  const handleDownload = () => {\n    if (!diagramRef.current) return;\n    const svgElement = diagramRef.current.querySelector('svg');\n    if (!svgElement) return;\n    try {\n      // 创建一个新的SVG元素用于下载\n      const svgClone = svgElement.cloneNode(true);\n      svgClone.style.transform = 'none'; // 移除缩放变换\n\n      const svgData = new XMLSerializer().serializeToString(svgClone);\n      const svgBlob = new Blob([svgData], {\n        type: 'image/svg+xml;charset=utf-8'\n      });\n      const svgUrl = URL.createObjectURL(svgBlob);\n      const downloadLink = document.createElement('a');\n      downloadLink.href = svgUrl;\n      downloadLink.download = `${title || 'diagram'}.svg`;\n      document.body.appendChild(downloadLink);\n      downloadLink.click();\n      document.body.removeChild(downloadLink);\n      URL.revokeObjectURL(svgUrl);\n      message.success('图表已下载');\n    } catch (err) {\n      console.error('Download failed:', err);\n      message.error('下载失败');\n    }\n    onDownload === null || onDownload === void 0 ? void 0 : onDownload();\n  };\n  const handleFullscreen = () => {\n    if (!diagramRef.current) return;\n    try {\n      if (diagramRef.current.requestFullscreen) {\n        diagramRef.current.requestFullscreen();\n      } else if (diagramRef.current.webkitRequestFullscreen) {\n        diagramRef.current.webkitRequestFullscreen();\n      } else if (diagramRef.current.msRequestFullscreen) {\n        diagramRef.current.msRequestFullscreen();\n      }\n    } catch (err) {\n      console.error('Fullscreen failed:', err);\n      message.error('全屏模式不支持');\n    }\n    onFullscreen === null || onFullscreen === void 0 ? void 0 : onFullscreen();\n  };\n  const renderControls = () => /*#__PURE__*/_jsxDEV(Space, {\n    children: [/*#__PURE__*/_jsxDEV(Button, {\n      icon: /*#__PURE__*/_jsxDEV(ZoomOutOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 236,\n        columnNumber: 15\n      }, this),\n      onClick: handleZoomOut,\n      disabled: zoom <= 0.2,\n      size: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 235,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        fontSize: '12px',\n        minWidth: '40px',\n        textAlign: 'center'\n      },\n      children: [Math.round(zoom * 100), \"%\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 241,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      icon: /*#__PURE__*/_jsxDEV(ZoomInOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 15\n      }, this),\n      onClick: handleZoomIn,\n      disabled: zoom >= 3,\n      size: \"small\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 251,\n        columnNumber: 15\n      }, this),\n      onClick: handleResetZoom,\n      size: \"small\",\n      title: \"\\u91CD\\u7F6E\\u7F29\\u653E\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 257,\n        columnNumber: 15\n      }, this),\n      onClick: () => setShowSettings(true),\n      size: \"small\",\n      title: \"\\u8BBE\\u7F6E\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 15\n      }, this),\n      onClick: handleDownload,\n      size: \"small\",\n      title: \"\\u4E0B\\u8F7DSVG\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      icon: /*#__PURE__*/_jsxDEV(FullscreenOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 269,\n        columnNumber: 15\n      }, this),\n      onClick: handleFullscreen,\n      size: \"small\",\n      title: \"\\u5168\\u5C4F\\u67E5\\u770B\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 234,\n    columnNumber: 5\n  }, this);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: title,\n      extra: renderControls(),\n      style: {\n        width: '100%'\n      },\n      children: [loading && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '50px',\n          minHeight: height\n        },\n        children: [/*#__PURE__*/_jsxDEV(Spin, {\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '16px'\n          },\n          children: \"\\u6B63\\u5728\\u6E32\\u67D3\\u56FE\\u8868...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 11\n      }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n        message: \"\\u56FE\\u8868\\u6E32\\u67D3\\u5931\\u8D25\",\n        description: error,\n        type: \"error\",\n        showIcon: true,\n        action: /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          onClick: renderDiagram,\n          children: \"\\u91CD\\u8BD5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 296,\n        columnNumber: 11\n      }, this), !loading && !error && /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: diagramRef,\n        style: {\n          minHeight: height,\n          overflow: 'auto',\n          border: '1px solid #f0f0f0',\n          borderRadius: '6px',\n          backgroundColor: '#fafafa',\n          padding: '16px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u56FE\\u8868\\u8BBE\\u7F6E\",\n      open: showSettings,\n      onCancel: () => setShowSettings(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setShowSettings(false),\n        children: \"\\u53D6\\u6D88\"\n      }, \"cancel\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 330,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        onClick: () => {\n          setShowSettings(false);\n          renderDiagram();\n        },\n        children: \"\\u5E94\\u7528\"\n      }, \"apply\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 333,\n        columnNumber: 11\n      }, this)],\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        style: {\n          width: '100%'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            children: \"\\u4E3B\\u9898:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            value: currentTheme,\n            onChange: handleThemeChange,\n            style: {\n              width: '100%',\n              marginTop: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"default\",\n              children: \"\\u9ED8\\u8BA4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"dark\",\n              children: \"\\u6DF1\\u8272\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"forest\",\n              children: \"\\u68EE\\u6797\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"neutral\",\n              children: \"\\u4E2D\\u6027\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(MermaidDiagram, \"g1D/QLYRDdHjrr+m6IvI60iC4Bo=\");\n_c = MermaidDiagram;\nexport default MermaidDiagram;\nvar _c;\n$RefreshReg$(_c, \"MermaidDiagram\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "useState", "Card", "<PERSON><PERSON>", "Space", "<PERSON><PERSON>", "Spin", "message", "Modal", "Select", "FullscreenOutlined", "DownloadOutlined", "ZoomInOutlined", "ZoomOutOutlined", "ReloadOutlined", "SettingOutlined", "mermaid", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Option", "MermaidDiagram", "definition", "title", "height", "theme", "onFullscreen", "onDownload", "_s", "diagramRef", "loading", "setLoading", "error", "setError", "zoom", "setZoom", "currentTheme", "setCurrentTheme", "showSettings", "setShowSettings", "diagramId", "Date", "now", "Math", "random", "toString", "substr", "initializeMermaid", "renderDiagram", "initialize", "startOnLoad", "securityLevel", "flowchart", "useMaxWidth", "htmlLabels", "curve", "sequence", "diagramMarginX", "diagramMarginY", "<PERSON><PERSON><PERSON><PERSON>", "width", "boxMargin", "boxTextMargin", "note<PERSON><PERSON><PERSON>", "messageMargin", "mirrorActors", "bottomMarginAdj", "gantt", "titleTopMargin", "barHeight", "fontSize", "gridLineStartPadding", "leftPadding", "topPadding", "rightPadding", "class", "git", "pie", "er", "journey", "err", "console", "current", "innerHTML", "<PERSON><PERSON><PERSON><PERSON>", "parse", "Error", "svg", "render", "svgElement", "querySelector", "style", "transform", "transform<PERSON><PERSON>in", "max<PERSON><PERSON><PERSON>", "handleZoomIn", "newZoom", "min", "applyZoom", "handleZoomOut", "max", "handleResetZoom", "zoomLevel", "handleThemeChange", "newTheme", "validTheme", "handleDownload", "svgClone", "cloneNode", "svgData", "XMLSerializer", "serializeToString", "svgBlob", "Blob", "type", "svgUrl", "URL", "createObjectURL", "downloadLink", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "success", "handleFullscreen", "requestFullscreen", "webkitRequestFullscreen", "msRequestFullscreen", "renderControls", "children", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "size", "min<PERSON><PERSON><PERSON>", "textAlign", "round", "extra", "padding", "minHeight", "marginTop", "description", "showIcon", "action", "ref", "overflow", "border", "borderRadius", "backgroundColor", "open", "onCancel", "footer", "direction", "value", "onChange", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/components/help/MermaidDiagram.tsx"], "sourcesContent": ["import React, { useEffect, useRef, useState } from 'react';\nimport { Card, Button, Space, Alert, Spin, message, Modal, Select } from 'antd';\nimport { \n  FullscreenOutlined, \n  DownloadOutlined, \n  ZoomInOutlined,\n  ZoomOutOutlined,\n  ReloadOutlined,\n  SettingOutlined\n} from '@ant-design/icons';\nimport mermaid from 'mermaid';\n\nconst { Option } = Select;\n\ninterface MermaidDiagramProps {\n  definition: string;\n  title?: string;\n  height?: number;\n  theme?: 'default' | 'dark' | 'forest' | 'neutral';\n  onFullscreen?: () => void;\n  onDownload?: () => void;\n}\n\nconst MermaidDiagram: React.FC<MermaidDiagramProps> = ({\n  definition,\n  title,\n  height = 400,\n  theme = 'default',\n  onFullscreen,\n  onDownload,\n}) => {\n  const diagramRef = useRef<HTMLDivElement>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [zoom, setZoom] = useState(1);\n  const [currentTheme, setCurrentTheme] = useState(theme);\n  const [showSettings, setShowSettings] = useState(false);\n  const [diagramId] = useState(`mermaid-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);\n\n  useEffect(() => {\n    initializeMermaid();\n  }, []);\n\n  useEffect(() => {\n    if (definition) {\n      renderDiagram();\n    }\n  }, [definition, currentTheme]);\n\n  const initializeMermaid = () => {\n    try {\n      mermaid.initialize({\n        startOnLoad: false,\n        theme: currentTheme,\n        securityLevel: 'loose',\n        flowchart: {\n          useMaxWidth: true,\n          htmlLabels: true,\n          curve: 'basis',\n        },\n        sequence: {\n          diagramMarginX: 50,\n          diagramMarginY: 10,\n          actorMargin: 50,\n          width: 150,\n          height: 65,\n          boxMargin: 10,\n          boxTextMargin: 5,\n          noteMargin: 10,\n          messageMargin: 35,\n          mirrorActors: true,\n          bottomMarginAdj: 1,\n          useMaxWidth: true,\n        },\n        gantt: {\n          titleTopMargin: 25,\n          barHeight: 20,\n          fontSize: 11,\n          gridLineStartPadding: 35,\n          leftPadding: 75,\n          topPadding: 50,\n          rightPadding: 25,\n        },\n        class: {\n          useMaxWidth: true,\n        },\n        git: {\n          useMaxWidth: true,\n        },\n        pie: {\n          useMaxWidth: true,\n        },\n        er: {\n          useMaxWidth: true,\n        },\n        journey: {\n          useMaxWidth: true,\n        },\n      });\n      setLoading(false);\n    } catch (err) {\n      console.error('Failed to initialize Mermaid:', err);\n      setError('图表渲染引擎初始化失败');\n      setLoading(false);\n    }\n  };\n\n  const renderDiagram = async () => {\n    if (!diagramRef.current || !definition) return;\n\n    setLoading(true);\n    setError(null);\n\n    try {\n      // 清空容器\n      diagramRef.current.innerHTML = '';\n\n      // 验证图表定义\n      const isValid = await mermaid.parse(definition);\n      if (!isValid) {\n        throw new Error('图表定义格式不正确');\n      }\n\n      // 渲染图表\n      const { svg } = await mermaid.render(diagramId, definition);\n      \n      if (diagramRef.current) {\n        diagramRef.current.innerHTML = svg;\n        \n        // 应用缩放\n        const svgElement = diagramRef.current.querySelector('svg');\n        if (svgElement) {\n          svgElement.style.transform = `scale(${zoom})`;\n          svgElement.style.transformOrigin = 'top left';\n          svgElement.style.maxWidth = '100%';\n          svgElement.style.height = 'auto';\n        }\n      }\n    } catch (err) {\n      console.error('Failed to render diagram:', err);\n      setError(err instanceof Error ? err.message : '图表渲染失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleZoomIn = () => {\n    const newZoom = Math.min(zoom + 0.2, 3);\n    setZoom(newZoom);\n    applyZoom(newZoom);\n  };\n\n  const handleZoomOut = () => {\n    const newZoom = Math.max(zoom - 0.2, 0.2);\n    setZoom(newZoom);\n    applyZoom(newZoom);\n  };\n\n  const handleResetZoom = () => {\n    setZoom(1);\n    applyZoom(1);\n  };\n\n  const applyZoom = (zoomLevel: number) => {\n    if (diagramRef.current) {\n      const svgElement = diagramRef.current.querySelector('svg');\n      if (svgElement) {\n        svgElement.style.transform = `scale(${zoomLevel})`;\n      }\n    }\n  };\n\n  const handleThemeChange = (newTheme: string) => {\n    const validTheme = newTheme as 'default' | 'dark' | 'forest' | 'neutral';\n    setCurrentTheme(validTheme);\n    mermaid.initialize({\n      theme: validTheme,\n      startOnLoad: false,\n    });\n  };\n\n  const handleDownload = () => {\n    if (!diagramRef.current) return;\n\n    const svgElement = diagramRef.current.querySelector('svg');\n    if (!svgElement) return;\n\n    try {\n      // 创建一个新的SVG元素用于下载\n      const svgClone = svgElement.cloneNode(true) as SVGElement;\n      svgClone.style.transform = 'none'; // 移除缩放变换\n      \n      const svgData = new XMLSerializer().serializeToString(svgClone);\n      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });\n      const svgUrl = URL.createObjectURL(svgBlob);\n      \n      const downloadLink = document.createElement('a');\n      downloadLink.href = svgUrl;\n      downloadLink.download = `${title || 'diagram'}.svg`;\n      document.body.appendChild(downloadLink);\n      downloadLink.click();\n      document.body.removeChild(downloadLink);\n      URL.revokeObjectURL(svgUrl);\n      \n      message.success('图表已下载');\n    } catch (err) {\n      console.error('Download failed:', err);\n      message.error('下载失败');\n    }\n\n    onDownload?.();\n  };\n\n  const handleFullscreen = () => {\n    if (!diagramRef.current) return;\n\n    try {\n      if (diagramRef.current.requestFullscreen) {\n        diagramRef.current.requestFullscreen();\n      } else if ((diagramRef.current as any).webkitRequestFullscreen) {\n        (diagramRef.current as any).webkitRequestFullscreen();\n      } else if ((diagramRef.current as any).msRequestFullscreen) {\n        (diagramRef.current as any).msRequestFullscreen();\n      }\n    } catch (err) {\n      console.error('Fullscreen failed:', err);\n      message.error('全屏模式不支持');\n    }\n\n    onFullscreen?.();\n  };\n\n  const renderControls = () => (\n    <Space>\n      <Button \n        icon={<ZoomOutOutlined />} \n        onClick={handleZoomOut}\n        disabled={zoom <= 0.2}\n        size=\"small\"\n      />\n      <span style={{ fontSize: '12px', minWidth: '40px', textAlign: 'center' }}>\n        {Math.round(zoom * 100)}%\n      </span>\n      <Button \n        icon={<ZoomInOutlined />} \n        onClick={handleZoomIn}\n        disabled={zoom >= 3}\n        size=\"small\"\n      />\n      <Button \n        icon={<ReloadOutlined />} \n        onClick={handleResetZoom}\n        size=\"small\"\n        title=\"重置缩放\"\n      />\n      <Button \n        icon={<SettingOutlined />} \n        onClick={() => setShowSettings(true)}\n        size=\"small\"\n        title=\"设置\"\n      />\n      <Button \n        icon={<DownloadOutlined />} \n        onClick={handleDownload}\n        size=\"small\"\n        title=\"下载SVG\"\n      />\n      <Button \n        icon={<FullscreenOutlined />} \n        onClick={handleFullscreen}\n        size=\"small\"\n        title=\"全屏查看\"\n      />\n    </Space>\n  );\n\n  return (\n    <>\n      <Card\n        title={title}\n        extra={renderControls()}\n        style={{ width: '100%' }}\n      >\n        {loading && (\n          <div style={{ \n            textAlign: 'center', \n            padding: '50px',\n            minHeight: height \n          }}>\n            <Spin size=\"large\" />\n            <div style={{ marginTop: '16px' }}>正在渲染图表...</div>\n          </div>\n        )}\n\n        {error && (\n          <Alert\n            message=\"图表渲染失败\"\n            description={error}\n            type=\"error\"\n            showIcon\n            action={\n              <Button size=\"small\" onClick={renderDiagram}>\n                重试\n              </Button>\n            }\n          />\n        )}\n\n        {!loading && !error && (\n          <div\n            ref={diagramRef}\n            style={{\n              minHeight: height,\n              overflow: 'auto',\n              border: '1px solid #f0f0f0',\n              borderRadius: '6px',\n              backgroundColor: '#fafafa',\n              padding: '16px',\n            }}\n          />\n        )}\n      </Card>\n\n      {/* 设置弹窗 */}\n      <Modal\n        title=\"图表设置\"\n        open={showSettings}\n        onCancel={() => setShowSettings(false)}\n        footer={[\n          <Button key=\"cancel\" onClick={() => setShowSettings(false)}>\n            取消\n          </Button>,\n          <Button \n            key=\"apply\" \n            type=\"primary\" \n            onClick={() => {\n              setShowSettings(false);\n              renderDiagram();\n            }}\n          >\n            应用\n          </Button>,\n        ]}\n      >\n        <Space direction=\"vertical\" style={{ width: '100%' }}>\n          <div>\n            <label>主题:</label>\n            <Select\n              value={currentTheme}\n              onChange={handleThemeChange}\n              style={{ width: '100%', marginTop: '8px' }}\n            >\n              <Option value=\"default\">默认</Option>\n              <Option value=\"dark\">深色</Option>\n              <Option value=\"forest\">森林</Option>\n              <Option value=\"neutral\">中性</Option>\n            </Select>\n          </div>\n        </Space>\n      </Modal>\n    </>\n  );\n};\n\nexport default MermaidDiagram;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAC/E,SACEC,kBAAkB,EAClBC,gBAAgB,EAChBC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,eAAe,QACV,mBAAmB;AAC1B,OAAOC,OAAO,MAAM,SAAS;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE9B,MAAM;EAAEC;AAAO,CAAC,GAAGZ,MAAM;AAWzB,MAAMa,cAA6C,GAAGA,CAAC;EACrDC,UAAU;EACVC,KAAK;EACLC,MAAM,GAAG,GAAG;EACZC,KAAK,GAAG,SAAS;EACjBC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,UAAU,GAAG9B,MAAM,CAAiB,IAAI,CAAC;EAC/C,MAAM,CAAC+B,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACgC,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACkC,IAAI,EAAEC,OAAO,CAAC,GAAGnC,QAAQ,CAAC,CAAC,CAAC;EACnC,MAAM,CAACoC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAACyB,KAAK,CAAC;EACvD,MAAM,CAACa,YAAY,EAAEC,eAAe,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACwC,SAAS,CAAC,GAAGxC,QAAQ,CAAC,WAAWyC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;EAEhGhD,SAAS,CAAC,MAAM;IACdiD,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAENjD,SAAS,CAAC,MAAM;IACd,IAAIwB,UAAU,EAAE;MACd0B,aAAa,CAAC,CAAC;IACjB;EACF,CAAC,EAAE,CAAC1B,UAAU,EAAEc,YAAY,CAAC,CAAC;EAE9B,MAAMW,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAI;MACFhC,OAAO,CAACkC,UAAU,CAAC;QACjBC,WAAW,EAAE,KAAK;QAClBzB,KAAK,EAAEW,YAAY;QACnBe,aAAa,EAAE,OAAO;QACtBC,SAAS,EAAE;UACTC,WAAW,EAAE,IAAI;UACjBC,UAAU,EAAE,IAAI;UAChBC,KAAK,EAAE;QACT,CAAC;QACDC,QAAQ,EAAE;UACRC,cAAc,EAAE,EAAE;UAClBC,cAAc,EAAE,EAAE;UAClBC,WAAW,EAAE,EAAE;UACfC,KAAK,EAAE,GAAG;UACVpC,MAAM,EAAE,EAAE;UACVqC,SAAS,EAAE,EAAE;UACbC,aAAa,EAAE,CAAC;UAChBC,UAAU,EAAE,EAAE;UACdC,aAAa,EAAE,EAAE;UACjBC,YAAY,EAAE,IAAI;UAClBC,eAAe,EAAE,CAAC;UAClBb,WAAW,EAAE;QACf,CAAC;QACDc,KAAK,EAAE;UACLC,cAAc,EAAE,EAAE;UAClBC,SAAS,EAAE,EAAE;UACbC,QAAQ,EAAE,EAAE;UACZC,oBAAoB,EAAE,EAAE;UACxBC,WAAW,EAAE,EAAE;UACfC,UAAU,EAAE,EAAE;UACdC,YAAY,EAAE;QAChB,CAAC;QACDC,KAAK,EAAE;UACLtB,WAAW,EAAE;QACf,CAAC;QACDuB,GAAG,EAAE;UACHvB,WAAW,EAAE;QACf,CAAC;QACDwB,GAAG,EAAE;UACHxB,WAAW,EAAE;QACf,CAAC;QACDyB,EAAE,EAAE;UACFzB,WAAW,EAAE;QACf,CAAC;QACD0B,OAAO,EAAE;UACP1B,WAAW,EAAE;QACf;MACF,CAAC,CAAC;MACFtB,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOiD,GAAG,EAAE;MACZC,OAAO,CAACjD,KAAK,CAAC,+BAA+B,EAAEgD,GAAG,CAAC;MACnD/C,QAAQ,CAAC,aAAa,CAAC;MACvBF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI,CAACnB,UAAU,CAACqD,OAAO,IAAI,CAAC5D,UAAU,EAAE;IAExCS,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACAJ,UAAU,CAACqD,OAAO,CAACC,SAAS,GAAG,EAAE;;MAEjC;MACA,MAAMC,OAAO,GAAG,MAAMrE,OAAO,CAACsE,KAAK,CAAC/D,UAAU,CAAC;MAC/C,IAAI,CAAC8D,OAAO,EAAE;QACZ,MAAM,IAAIE,KAAK,CAAC,WAAW,CAAC;MAC9B;;MAEA;MACA,MAAM;QAAEC;MAAI,CAAC,GAAG,MAAMxE,OAAO,CAACyE,MAAM,CAAChD,SAAS,EAAElB,UAAU,CAAC;MAE3D,IAAIO,UAAU,CAACqD,OAAO,EAAE;QACtBrD,UAAU,CAACqD,OAAO,CAACC,SAAS,GAAGI,GAAG;;QAElC;QACA,MAAME,UAAU,GAAG5D,UAAU,CAACqD,OAAO,CAACQ,aAAa,CAAC,KAAK,CAAC;QAC1D,IAAID,UAAU,EAAE;UACdA,UAAU,CAACE,KAAK,CAACC,SAAS,GAAG,SAAS1D,IAAI,GAAG;UAC7CuD,UAAU,CAACE,KAAK,CAACE,eAAe,GAAG,UAAU;UAC7CJ,UAAU,CAACE,KAAK,CAACG,QAAQ,GAAG,MAAM;UAClCL,UAAU,CAACE,KAAK,CAACnE,MAAM,GAAG,MAAM;QAClC;MACF;IACF,CAAC,CAAC,OAAOwD,GAAG,EAAE;MACZC,OAAO,CAACjD,KAAK,CAAC,2BAA2B,EAAEgD,GAAG,CAAC;MAC/C/C,QAAQ,CAAC+C,GAAG,YAAYM,KAAK,GAAGN,GAAG,CAAC1E,OAAO,GAAG,QAAQ,CAAC;IACzD,CAAC,SAAS;MACRyB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgE,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,OAAO,GAAGrD,IAAI,CAACsD,GAAG,CAAC/D,IAAI,GAAG,GAAG,EAAE,CAAC,CAAC;IACvCC,OAAO,CAAC6D,OAAO,CAAC;IAChBE,SAAS,CAACF,OAAO,CAAC;EACpB,CAAC;EAED,MAAMG,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMH,OAAO,GAAGrD,IAAI,CAACyD,GAAG,CAAClE,IAAI,GAAG,GAAG,EAAE,GAAG,CAAC;IACzCC,OAAO,CAAC6D,OAAO,CAAC;IAChBE,SAAS,CAACF,OAAO,CAAC;EACpB,CAAC;EAED,MAAMK,eAAe,GAAGA,CAAA,KAAM;IAC5BlE,OAAO,CAAC,CAAC,CAAC;IACV+D,SAAS,CAAC,CAAC,CAAC;EACd,CAAC;EAED,MAAMA,SAAS,GAAII,SAAiB,IAAK;IACvC,IAAIzE,UAAU,CAACqD,OAAO,EAAE;MACtB,MAAMO,UAAU,GAAG5D,UAAU,CAACqD,OAAO,CAACQ,aAAa,CAAC,KAAK,CAAC;MAC1D,IAAID,UAAU,EAAE;QACdA,UAAU,CAACE,KAAK,CAACC,SAAS,GAAG,SAASU,SAAS,GAAG;MACpD;IACF;EACF,CAAC;EAED,MAAMC,iBAAiB,GAAIC,QAAgB,IAAK;IAC9C,MAAMC,UAAU,GAAGD,QAAqD;IACxEnE,eAAe,CAACoE,UAAU,CAAC;IAC3B1F,OAAO,CAACkC,UAAU,CAAC;MACjBxB,KAAK,EAAEgF,UAAU;MACjBvD,WAAW,EAAE;IACf,CAAC,CAAC;EACJ,CAAC;EAED,MAAMwD,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,CAAC7E,UAAU,CAACqD,OAAO,EAAE;IAEzB,MAAMO,UAAU,GAAG5D,UAAU,CAACqD,OAAO,CAACQ,aAAa,CAAC,KAAK,CAAC;IAC1D,IAAI,CAACD,UAAU,EAAE;IAEjB,IAAI;MACF;MACA,MAAMkB,QAAQ,GAAGlB,UAAU,CAACmB,SAAS,CAAC,IAAI,CAAe;MACzDD,QAAQ,CAAChB,KAAK,CAACC,SAAS,GAAG,MAAM,CAAC,CAAC;;MAEnC,MAAMiB,OAAO,GAAG,IAAIC,aAAa,CAAC,CAAC,CAACC,iBAAiB,CAACJ,QAAQ,CAAC;MAC/D,MAAMK,OAAO,GAAG,IAAIC,IAAI,CAAC,CAACJ,OAAO,CAAC,EAAE;QAAEK,IAAI,EAAE;MAA8B,CAAC,CAAC;MAC5E,MAAMC,MAAM,GAAGC,GAAG,CAACC,eAAe,CAACL,OAAO,CAAC;MAE3C,MAAMM,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MAChDF,YAAY,CAACG,IAAI,GAAGN,MAAM;MAC1BG,YAAY,CAACI,QAAQ,GAAG,GAAGnG,KAAK,IAAI,SAAS,MAAM;MACnDgG,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,YAAY,CAAC;MACvCA,YAAY,CAACO,KAAK,CAAC,CAAC;MACpBN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,YAAY,CAAC;MACvCF,GAAG,CAACW,eAAe,CAACZ,MAAM,CAAC;MAE3B7G,OAAO,CAAC0H,OAAO,CAAC,OAAO,CAAC;IAC1B,CAAC,CAAC,OAAOhD,GAAG,EAAE;MACZC,OAAO,CAACjD,KAAK,CAAC,kBAAkB,EAAEgD,GAAG,CAAC;MACtC1E,OAAO,CAAC0B,KAAK,CAAC,MAAM,CAAC;IACvB;IAEAL,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAG,CAAC;EAChB,CAAC;EAED,MAAMsG,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,IAAI,CAACpG,UAAU,CAACqD,OAAO,EAAE;IAEzB,IAAI;MACF,IAAIrD,UAAU,CAACqD,OAAO,CAACgD,iBAAiB,EAAE;QACxCrG,UAAU,CAACqD,OAAO,CAACgD,iBAAiB,CAAC,CAAC;MACxC,CAAC,MAAM,IAAKrG,UAAU,CAACqD,OAAO,CAASiD,uBAAuB,EAAE;QAC7DtG,UAAU,CAACqD,OAAO,CAASiD,uBAAuB,CAAC,CAAC;MACvD,CAAC,MAAM,IAAKtG,UAAU,CAACqD,OAAO,CAASkD,mBAAmB,EAAE;QACzDvG,UAAU,CAACqD,OAAO,CAASkD,mBAAmB,CAAC,CAAC;MACnD;IACF,CAAC,CAAC,OAAOpD,GAAG,EAAE;MACZC,OAAO,CAACjD,KAAK,CAAC,oBAAoB,EAAEgD,GAAG,CAAC;MACxC1E,OAAO,CAAC0B,KAAK,CAAC,SAAS,CAAC;IAC1B;IAEAN,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAG,CAAC;EAClB,CAAC;EAED,MAAM2G,cAAc,GAAGA,CAAA,kBACrBpH,OAAA,CAACd,KAAK;IAAAmI,QAAA,gBACJrH,OAAA,CAACf,MAAM;MACLqI,IAAI,eAAEtH,OAAA,CAACL,eAAe;QAAA4H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC1BC,OAAO,EAAEzC,aAAc;MACvB0C,QAAQ,EAAE3G,IAAI,IAAI,GAAI;MACtB4G,IAAI,EAAC;IAAO;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,eACF1H,OAAA;MAAM0E,KAAK,EAAE;QAAErB,QAAQ,EAAE,MAAM;QAAEyE,QAAQ,EAAE,MAAM;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAV,QAAA,GACtE3F,IAAI,CAACsG,KAAK,CAAC/G,IAAI,GAAG,GAAG,CAAC,EAAC,GAC1B;IAAA;MAAAsG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACP1H,OAAA,CAACf,MAAM;MACLqI,IAAI,eAAEtH,OAAA,CAACN,cAAc;QAAA6H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACzBC,OAAO,EAAE7C,YAAa;MACtB8C,QAAQ,EAAE3G,IAAI,IAAI,CAAE;MACpB4G,IAAI,EAAC;IAAO;MAAAN,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,eACF1H,OAAA,CAACf,MAAM;MACLqI,IAAI,eAAEtH,OAAA,CAACJ,cAAc;QAAA2H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACzBC,OAAO,EAAEvC,eAAgB;MACzByC,IAAI,EAAC,OAAO;MACZvH,KAAK,EAAC;IAAM;MAAAiH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC,eACF1H,OAAA,CAACf,MAAM;MACLqI,IAAI,eAAEtH,OAAA,CAACH,eAAe;QAAA0H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC1BC,OAAO,EAAEA,CAAA,KAAMrG,eAAe,CAAC,IAAI,CAAE;MACrCuG,IAAI,EAAC,OAAO;MACZvH,KAAK,EAAC;IAAI;MAAAiH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACX,CAAC,eACF1H,OAAA,CAACf,MAAM;MACLqI,IAAI,eAAEtH,OAAA,CAACP,gBAAgB;QAAA8H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC3BC,OAAO,EAAElC,cAAe;MACxBoC,IAAI,EAAC,OAAO;MACZvH,KAAK,EAAC;IAAO;MAAAiH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC,eACF1H,OAAA,CAACf,MAAM;MACLqI,IAAI,eAAEtH,OAAA,CAACR,kBAAkB;QAAA+H,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MAC7BC,OAAO,EAAEX,gBAAiB;MAC1Ba,IAAI,EAAC,OAAO;MACZvH,KAAK,EAAC;IAAM;MAAAiH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CACR;EAED,oBACE1H,OAAA,CAAAE,SAAA;IAAAmH,QAAA,gBACErH,OAAA,CAAChB,IAAI;MACHsB,KAAK,EAAEA,KAAM;MACb2H,KAAK,EAAEb,cAAc,CAAC,CAAE;MACxB1C,KAAK,EAAE;QAAE/B,KAAK,EAAE;MAAO,CAAE;MAAA0E,QAAA,GAExBxG,OAAO,iBACNb,OAAA;QAAK0E,KAAK,EAAE;UACVqD,SAAS,EAAE,QAAQ;UACnBG,OAAO,EAAE,MAAM;UACfC,SAAS,EAAE5H;QACb,CAAE;QAAA8G,QAAA,gBACArH,OAAA,CAACZ,IAAI;UAACyI,IAAI,EAAC;QAAO;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrB1H,OAAA;UAAK0E,KAAK,EAAE;YAAE0D,SAAS,EAAE;UAAO,CAAE;UAAAf,QAAA,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CACN,EAEA3G,KAAK,iBACJf,OAAA,CAACb,KAAK;QACJE,OAAO,EAAC,sCAAQ;QAChBgJ,WAAW,EAAEtH,KAAM;QACnBkF,IAAI,EAAC,OAAO;QACZqC,QAAQ;QACRC,MAAM,eACJvI,OAAA,CAACf,MAAM;UAAC4I,IAAI,EAAC,OAAO;UAACF,OAAO,EAAE5F,aAAc;UAAAsF,QAAA,EAAC;QAE7C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MACT;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACF,EAEA,CAAC7G,OAAO,IAAI,CAACE,KAAK,iBACjBf,OAAA;QACEwI,GAAG,EAAE5H,UAAW;QAChB8D,KAAK,EAAE;UACLyD,SAAS,EAAE5H,MAAM;UACjBkI,QAAQ,EAAE,MAAM;UAChBC,MAAM,EAAE,mBAAmB;UAC3BC,YAAY,EAAE,KAAK;UACnBC,eAAe,EAAE,SAAS;UAC1BV,OAAO,EAAE;QACX;MAAE;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eAGP1H,OAAA,CAACV,KAAK;MACJgB,KAAK,EAAC,0BAAM;MACZuI,IAAI,EAAExH,YAAa;MACnByH,QAAQ,EAAEA,CAAA,KAAMxH,eAAe,CAAC,KAAK,CAAE;MACvCyH,MAAM,EAAE,cACN/I,OAAA,CAACf,MAAM;QAAc0I,OAAO,EAAEA,CAAA,KAAMrG,eAAe,CAAC,KAAK,CAAE;QAAA+F,QAAA,EAAC;MAE5D,GAFY,QAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEZ,CAAC,eACT1H,OAAA,CAACf,MAAM;QAELgH,IAAI,EAAC,SAAS;QACd0B,OAAO,EAAEA,CAAA,KAAM;UACbrG,eAAe,CAAC,KAAK,CAAC;UACtBS,aAAa,CAAC,CAAC;QACjB,CAAE;QAAAsF,QAAA,EACH;MAED,GARM,OAAO;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAQL,CAAC,CACT;MAAAL,QAAA,eAEFrH,OAAA,CAACd,KAAK;QAAC8J,SAAS,EAAC,UAAU;QAACtE,KAAK,EAAE;UAAE/B,KAAK,EAAE;QAAO,CAAE;QAAA0E,QAAA,eACnDrH,OAAA;UAAAqH,QAAA,gBACErH,OAAA;YAAAqH,QAAA,EAAO;UAAG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClB1H,OAAA,CAACT,MAAM;YACL0J,KAAK,EAAE9H,YAAa;YACpB+H,QAAQ,EAAE5D,iBAAkB;YAC5BZ,KAAK,EAAE;cAAE/B,KAAK,EAAE,MAAM;cAAEyF,SAAS,EAAE;YAAM,CAAE;YAAAf,QAAA,gBAE3CrH,OAAA,CAACG,MAAM;cAAC8I,KAAK,EAAC,SAAS;cAAA5B,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACnC1H,OAAA,CAACG,MAAM;cAAC8I,KAAK,EAAC,MAAM;cAAA5B,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChC1H,OAAA,CAACG,MAAM;cAAC8I,KAAK,EAAC,QAAQ;cAAA5B,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAClC1H,OAAA,CAACG,MAAM;cAAC8I,KAAK,EAAC,SAAS;cAAA5B,QAAA,EAAC;YAAE;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7B,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACR,CAAC;AAEP,CAAC;AAAC/G,EAAA,CAnVIP,cAA6C;AAAA+I,EAAA,GAA7C/I,cAA6C;AAqVnD,eAAeA,cAAc;AAAC,IAAA+I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}