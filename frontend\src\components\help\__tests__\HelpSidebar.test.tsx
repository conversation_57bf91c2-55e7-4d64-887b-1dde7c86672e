import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';
import HelpSidebar from '../HelpSidebar';
import { HelpCategory, HelpTag } from '../../../types/help';

// Mock store
const mockStore = configureStore({
  reducer: {
    help: (state = {}, action) => state
  }
});

// Mock data
const mockCategories: HelpCategory[] = [
  {
    id: 1,
    name: '功能指南',
    description: '系统功能指南',
    level: 0,
    sortOrder: 1,
    visible: true,
    enabled: true,
    deleted: false,
    createdAt: '2024-08-20T10:00:00Z',
    updatedAt: '2024-08-20T10:00:00Z',
    children: [
      {
        id: 2,
        name: 'BOM管理',
        description: 'BOM创建和管理',
        parentId: 1,
        level: 1,
        sortOrder: 1,
        visible: true,
        enabled: true,
        deleted: false,
        createdAt: '2024-08-20T10:00:00Z',
        updatedAt: '2024-08-20T10:00:00Z',
        contentCount: 5
      }
    ]
  },
  {
    id: 3,
    name: '常见问题',
    description: '用户常见问题',
    level: 0,
    sortOrder: 2,
    visible: true,
    enabled: true,
    deleted: false,
    createdAt: '2024-08-20T10:00:00Z',
    updatedAt: '2024-08-20T10:00:00Z'
  }
];

const mockTags: HelpTag[] = [
  {
    id: 1,
    name: '新手入门',
    description: '适合新用户',
    type: 'SYSTEM',
    color: 'green',
    usageCount: 10,
    enabled: true,
    deleted: false,
    createdAt: '2024-08-20T10:00:00Z',
    updatedAt: '2024-08-20T10:00:00Z'
  },
  {
    id: 2,
    name: '高级功能',
    description: '高级功能说明',
    type: 'SYSTEM',
    color: 'blue',
    usageCount: 8,
    enabled: true,
    deleted: false,
    createdAt: '2024-08-20T10:00:00Z',
    updatedAt: '2024-08-20T10:00:00Z'
  }
];

// Mock props
const defaultProps = {
  categories: mockCategories,
  popularTags: mockTags,
  collapsed: false,
  selectedCategoryId: null,
  onCategorySelect: jest.fn(),
  onSearch: jest.fn()
};

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <Provider store={mockStore}>
      {component}
    </Provider>
  );
};

describe('HelpSidebar', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders sidebar with categories', () => {
    renderWithProvider(<HelpSidebar {...defaultProps} />);
    
    expect(screen.getByText('帮助手册')).toBeInTheDocument();
    expect(screen.getByText('功能指南')).toBeInTheDocument();
    expect(screen.getByText('常见问题')).toBeInTheDocument();
  });

  test('renders search input when not collapsed', () => {
    renderWithProvider(<HelpSidebar {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('搜索帮助内容...');
    expect(searchInput).toBeInTheDocument();
  });

  test('does not render search input when collapsed', () => {
    renderWithProvider(<HelpSidebar {...defaultProps} collapsed={true} />);
    
    const searchInput = screen.queryByPlaceholderText('搜索帮助内容...');
    expect(searchInput).not.toBeInTheDocument();
  });

  test('calls onSearch when search is performed', async () => {
    const mockOnSearch = jest.fn();
    renderWithProvider(
      <HelpSidebar {...defaultProps} onSearch={mockOnSearch} />
    );
    
    const searchInput = screen.getByPlaceholderText('搜索帮助内容...');
    fireEvent.change(searchInput, { target: { value: 'BOM' } });
    
    const searchButton = screen.getByRole('button', { name: /search/i });
    fireEvent.click(searchButton);
    
    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith('BOM');
    });
  });

  test('calls onCategorySelect when category is clicked', async () => {
    const mockOnCategorySelect = jest.fn();
    renderWithProvider(
      <HelpSidebar {...defaultProps} onCategorySelect={mockOnCategorySelect} />
    );
    
    // 查找分类树节点
    const categoryNode = screen.getByText('功能指南');
    fireEvent.click(categoryNode);
    
    await waitFor(() => {
      expect(mockOnCategorySelect).toHaveBeenCalledWith(1);
    });
  });

  test('renders popular tags when not collapsed', () => {
    renderWithProvider(<HelpSidebar {...defaultProps} />);
    
    expect(screen.getByText('热门标签')).toBeInTheDocument();
    expect(screen.getByText('新手入门')).toBeInTheDocument();
    expect(screen.getByText('高级功能')).toBeInTheDocument();
  });

  test('does not render popular tags when collapsed', () => {
    renderWithProvider(<HelpSidebar {...defaultProps} collapsed={true} />);
    
    expect(screen.queryByText('热门标签')).not.toBeInTheDocument();
  });

  test('shows content count for categories', () => {
    renderWithProvider(<HelpSidebar {...defaultProps} />);
    
    // BOM管理分类应该显示内容数量
    expect(screen.getByText('5')).toBeInTheDocument();
  });

  test('renders quick menu items', () => {
    renderWithProvider(<HelpSidebar {...defaultProps} />);
    
    expect(screen.getByText('首页')).toBeInTheDocument();
    expect(screen.getByText('功能指南')).toBeInTheDocument();
    expect(screen.getByText('常见问题')).toBeInTheDocument();
    expect(screen.getByText('系统设置')).toBeInTheDocument();
  });

  test('handles tag click for search', async () => {
    const mockOnSearch = jest.fn();
    renderWithProvider(
      <HelpSidebar {...defaultProps} onSearch={mockOnSearch} />
    );
    
    const tagElement = screen.getByText('新手入门');
    fireEvent.click(tagElement);
    
    await waitFor(() => {
      expect(mockOnSearch).toHaveBeenCalledWith('tag:新手入门');
    });
  });

  test('shows usage count for tags', () => {
    renderWithProvider(<HelpSidebar {...defaultProps} />);
    
    // 标签应该显示使用次数
    expect(screen.getByText('(10)')).toBeInTheDocument();
    expect(screen.getByText('(8)')).toBeInTheDocument();
  });

  test('renders help and settings links', () => {
    renderWithProvider(<HelpSidebar {...defaultProps} />);
    
    expect(screen.getByText('使用帮助')).toBeInTheDocument();
    expect(screen.getByText('设置')).toBeInTheDocument();
  });

  test('handles empty categories gracefully', () => {
    renderWithProvider(
      <HelpSidebar {...defaultProps} categories={[]} />
    );
    
    expect(screen.getByText('帮助手册')).toBeInTheDocument();
    expect(screen.getByText('内容分类')).toBeInTheDocument();
  });

  test('handles empty tags gracefully', () => {
    renderWithProvider(
      <HelpSidebar {...defaultProps} popularTags={[]} />
    );
    
    expect(screen.getByText('帮助手册')).toBeInTheDocument();
    // 热门标签部分不应该显示
    expect(screen.queryByText('热门标签')).not.toBeInTheDocument();
  });

  test('updates search input value', () => {
    renderWithProvider(<HelpSidebar {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('搜索帮助内容...') as HTMLInputElement;
    fireEvent.change(searchInput, { target: { value: '测试搜索' } });
    
    expect(searchInput.value).toBe('测试搜索');
  });

  test('clears search input when clear button is clicked', () => {
    renderWithProvider(<HelpSidebar {...defaultProps} />);
    
    const searchInput = screen.getByPlaceholderText('搜索帮助内容...') as HTMLInputElement;
    fireEvent.change(searchInput, { target: { value: '测试搜索' } });
    
    // 查找清除按钮并点击
    const clearButton = screen.getByRole('button', { name: /clear/i });
    fireEvent.click(clearButton);
    
    expect(searchInput.value).toBe('');
  });

  test('shows tooltip when collapsed', () => {
    renderWithProvider(<HelpSidebar {...defaultProps} collapsed={true} />);
    
    // 在折叠状态下，logo应该有tooltip
    const logoIcon = screen.getByRole('img');
    expect(logoIcon).toBeInTheDocument();
  });
});
