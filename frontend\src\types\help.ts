// 帮助手册相关类型定义

export interface HelpCategory {
  id: number;
  name: string;
  description?: string;
  parentId?: number;
  path?: string;
  level: number;
  sortOrder: number;
  icon?: string;
  visible: boolean;
  enabled: boolean;
  deleted: boolean;
  creator?: string;
  creatorId?: number;
  updater?: string;
  updaterId?: number;
  createdAt: string;
  updatedAt: string;
  children?: HelpCategory[];
  contentCount?: number;
}

export interface HelpContent {
  id: number;
  title: string;
  content: string;
  summary?: string;
  type: ContentType;
  categoryId: number;
  parentId?: number;
  sortOrder: number;
  status: ContentStatus;
  viewCount: number;
  likeCount: number;
  dislikeCount: number;
  author?: string;
  authorId?: number;
  reviewer?: string;
  reviewerId?: number;
  reviewedAt?: string;
  version: string;
  featured: boolean;
  published: boolean;
  deleted: boolean;
  publishAt?: string;
  expireAt?: string;
  keywords?: string;
  metaDescription?: string;
  createdAt: string;
  updatedAt: string;
  tags?: HelpTag[];
  category?: HelpCategory;
  attachments?: HelpAttachment[];
}

export interface HelpTag {
  id: number;
  name: string;
  description?: string;
  type: TagType;
  color?: string;
  usageCount: number;
  enabled: boolean;
  deleted: boolean;
  creator?: string;
  creatorId?: number;
  createdAt: string;
  updatedAt: string;
}

export interface HelpAttachment {
  id: number;
  contentId: number;
  fileName: string;
  originalName: string;
  filePath: string;
  contentType?: string;
  fileSize?: number;
  type: AttachmentType;
  description?: string;
  downloadCount: number;
  deleted: boolean;
  uploader?: string;
  uploaderId?: number;
  createdAt: string;
  updatedAt: string;
}

export interface HelpFeedback {
  id: number;
  contentId: number;
  type: FeedbackType;
  rating?: number;
  content?: string;
  contact?: string;
  status: FeedbackStatus;
  reply?: string;
  replier?: string;
  replierId?: number;
  repliedAt?: string;
  anonymous: boolean;
  deleted: boolean;
  submitter?: string;
  submitterId?: number;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  updatedAt: string;
}

// 枚举类型
export enum ContentType {
  FUNCTION_GUIDE = 'FUNCTION_GUIDE',
  ROLE_PERMISSION = 'ROLE_PERMISSION',
  INDUSTRY_KNOWLEDGE = 'INDUSTRY_KNOWLEDGE',
  WORKFLOW = 'WORKFLOW',
  BUSINESS_PROCESS = 'BUSINESS_PROCESS',
  FAQ = 'FAQ',
  TUTORIAL = 'TUTORIAL',
  ANNOUNCEMENT = 'ANNOUNCEMENT',
  DIAGRAM = 'DIAGRAM',
  VIDEO = 'VIDEO'
}

export enum ContentStatus {
  DRAFT = 'DRAFT',
  PENDING_REVIEW = 'PENDING_REVIEW',
  APPROVED = 'APPROVED',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED',
  REJECTED = 'REJECTED'
}

export enum TagType {
  CONTENT = 'CONTENT',
  CATEGORY = 'CATEGORY',
  SYSTEM = 'SYSTEM',
  CUSTOM = 'CUSTOM'
}

export enum AttachmentType {
  IMAGE = 'IMAGE',
  DOCUMENT = 'DOCUMENT',
  VIDEO = 'VIDEO',
  AUDIO = 'AUDIO',
  ARCHIVE = 'ARCHIVE',
  OTHER = 'OTHER'
}

export enum FeedbackType {
  LIKE = 'LIKE',
  DISLIKE = 'DISLIKE',
  COMMENT = 'COMMENT',
  SUGGESTION = 'SUGGESTION',
  BUG_REPORT = 'BUG_REPORT',
  CONTENT_ERROR = 'CONTENT_ERROR',
  IMPROVEMENT = 'IMPROVEMENT'
}

export enum FeedbackStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  RESOLVED = 'RESOLVED',
  REJECTED = 'REJECTED',
  CLOSED = 'CLOSED'
}

// 搜索相关类型
export interface SearchParams {
  keyword?: string;
  type?: ContentType;
  categoryId?: number;
  authorId?: number;
  status?: ContentStatus;
  featured?: boolean;
  startDate?: string;
  endDate?: string;
  tags?: string[];
  page?: number;
  size?: number;
  sortBy?: string;
  sortDir?: 'asc' | 'desc';
}

export interface SearchResult {
  content: HelpContent[];
  totalElements: number;
  totalPages: number;
  currentPage: number;
  size: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

// 统计相关类型
export interface HelpStatistics {
  totalCategories: number;
  totalContent: number;
  totalTags: number;
  totalAttachments: number;
  totalFeedback: number;
  publishedContent: number;
  featuredContent: number;
  averageRating: number;
  popularCategories: HelpCategory[];
  popularContent: HelpContent[];
  recentContent: HelpContent[];
  contentByType: { [key in ContentType]?: number };
  contentByStatus: { [key in ContentStatus]?: number };
  feedbackByType: { [key in FeedbackType]?: number };
  feedbackByStatus: { [key in FeedbackStatus]?: number };
}

// 导航相关类型
export interface HelpNavigation {
  categories: HelpCategory[];
  quickLinks: QuickLink[];
  featuredContent: HelpContent[];
  popularTags: HelpTag[];
}

export interface QuickLink {
  id: string;
  title: string;
  description: string;
  url: string;
  icon?: string;
  category: string;
}

// 流程图相关类型
export interface WorkflowDiagram {
  id: string;
  title: string;
  description: string;
  type: 'workflow' | 'business_process' | 'system_flow';
  diagramData: string; // Mermaid 图表定义
  category: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

// 表单相关类型
export interface CategoryForm {
  name: string;
  description?: string;
  parentId?: number;
  icon?: string;
  visible?: boolean;
  enabled?: boolean;
  sortOrder?: number;
}

export interface ContentForm {
  title: string;
  content: string;
  summary?: string;
  type: ContentType;
  categoryId: number;
  keywords?: string;
  metaDescription?: string;
  featured?: boolean;
  tags?: string[];
  expireAt?: string;
}

export interface FeedbackForm {
  contentId: number;
  type: FeedbackType;
  rating?: number;
  content?: string;
  contact?: string;
  anonymous?: boolean;
}

// API 响应类型
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  code?: string;
  timestamp: string;
}

export interface PageResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
  empty: boolean;
}

// 权限相关类型
export interface HelpPermission {
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canPublish: boolean;
  canReview: boolean;
  canManageCategory: boolean;
  canManageTag: boolean;
  canManageFeedback: boolean;
  canViewStatistics: boolean;
}

// 配置相关类型
export interface HelpConfig {
  enableComments: boolean;
  enableRating: boolean;
  enableAnonymousFeedback: boolean;
  autoPublish: boolean;
  requireReview: boolean;
  maxFileSize: number;
  allowedFileTypes: string[];
  defaultLanguage: string;
  enableSearch: boolean;
  enableTagCloud: boolean;
  itemsPerPage: number;
}

// 主题相关类型
export interface HelpTheme {
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  textColor: string;
  linkColor: string;
  borderColor: string;
  fontFamily: string;
  fontSize: string;
  borderRadius: string;
}

// 导出/导入相关类型
export interface ExportOptions {
  format: 'json' | 'xml' | 'csv' | 'pdf';
  includeAttachments: boolean;
  includeComments: boolean;
  dateRange?: {
    startDate: string;
    endDate: string;
  };
  categories?: number[];
  types?: ContentType[];
}

export interface ImportResult {
  success: boolean;
  imported: number;
  failed: number;
  errors: string[];
  warnings: string[];
}
