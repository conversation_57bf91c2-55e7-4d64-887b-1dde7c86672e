import React from 'react';
import {
  Row,
  Col,
  Card,
  Typography,
  Space,
  Button,
  List,
  Avatar,
  Tag,
  Statistic,
  Divider,
  Input,
  Badge
} from 'antd';
import {
  BookOutlined,
  UserOutlined,
  DatabaseOutlined,
  ToolOutlined,
  QuestionCircleOutlined,
  FileTextOutlined,
  EyeOutlined,
  LikeOutlined,
  ClockCircleOutlined,
  FireOutlined,
  SearchOutlined,
  RightOutlined
} from '@ant-design/icons';
import { HelpNavigation, HelpStatistics, ContentType } from '../../types/help';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;

interface HelpHomeProps {
  navigation?: HelpNavigation | null;
  statistics?: HelpStatistics | null;
  onCategorySelect: (categoryId: number) => void;
  onContentSelect: (contentId: number) => void;
  onSearch: (keyword: string) => void;
}

/**
 * 帮助手册首页组件
 */
const HelpHome: React.FC<HelpHomeProps> = ({
  navigation,
  statistics,
  onCategorySelect,
  onContentSelect,
  onSearch
}) => {
  // 快速入口配置
  const quickEntries = [
    {
      key: 'functions',
      title: '功能指南',
      description: '系统各功能模块的详细操作指南',
      icon: <BookOutlined style={{ fontSize: '24px', color: '#1890ff' }} />,
      categoryId: 1,
      color: '#e6f7ff'
    },
    {
      key: 'roles',
      title: '角色权限',
      description: '各用户角色的权限说明和管理',
      icon: <UserOutlined style={{ fontSize: '24px', color: '#52c41a' }} />,
      categoryId: 2,
      color: '#f6ffed'
    },
    {
      key: 'knowledge',
      title: '行业知识',
      description: '天线行业相关的专业知识库',
      icon: <DatabaseOutlined style={{ fontSize: '24px', color: '#722ed1' }} />,
      categoryId: 3,
      color: '#f9f0ff'
    },
    {
      key: 'workflows',
      title: '业务流程',
      description: '完整的业务操作流程指导',
      icon: <ToolOutlined style={{ fontSize: '24px', color: '#fa8c16' }} />,
      categoryId: 4,
      color: '#fff7e6'
    },
    {
      key: 'faq',
      title: '常见问题',
      description: '用户常遇问题的解答集合',
      icon: <QuestionCircleOutlined style={{ fontSize: '24px', color: '#eb2f96' }} />,
      categoryId: 5,
      color: '#fff0f6'
    },
    {
      key: 'tutorials',
      title: '视频教程',
      description: '直观的视频操作教程',
      icon: <FileTextOutlined style={{ fontSize: '24px', color: '#13c2c2' }} />,
      categoryId: 6,
      color: '#e6fffb'
    }
  ];

  // 渲染统计卡片
  const renderStatistics = () => {
    if (!statistics) return null;

    return (
      <Row gutter={[16, 16]} style={{ marginBottom: '32px' }}>
        <Col xs={12} sm={8} md={6}>
          <Card>
            <Statistic
              title="总内容数"
              value={statistics.totalContent}
              prefix={<FileTextOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6}>
          <Card>
            <Statistic
              title="分类数量"
              value={statistics.totalCategories}
              prefix={<BookOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6}>
          <Card>
            <Statistic
              title="平均评分"
              value={statistics.averageRating}
              precision={1}
              prefix={<LikeOutlined />}
              suffix="/ 5"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={12} sm={8} md={6}>
          <Card>
            <Statistic
              title="总标签数"
              value={statistics.totalTags}
              prefix={<Tag />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染快速入口
  const renderQuickEntries = () => {
    return (
      <div style={{ marginBottom: '32px' }}>
        <Title level={3} style={{ marginBottom: '24px' }}>
          <BookOutlined /> 快速入口
        </Title>
        <Row gutter={[16, 16]}>
          {quickEntries.map((entry) => (
            <Col xs={24} sm={12} md={8} lg={6} key={entry.key}>
              <Card
                hoverable
                style={{ 
                  height: '140px',
                  background: entry.color,
                  border: 'none'
                }}
                bodyStyle={{ padding: '20px' }}
                onClick={() => onCategorySelect(entry.categoryId)}
              >
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center' }}>
                    {entry.icon}
                  </div>
                  <Title level={5} style={{ margin: 0, textAlign: 'center' }}>
                    {entry.title}
                  </Title>
                  <Text type="secondary" style={{ fontSize: '12px', textAlign: 'center' }}>
                    {entry.description}
                  </Text>
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    );
  };

  // 渲染特色内容
  const renderFeaturedContent = () => {
    if (!navigation?.featuredContent || navigation.featuredContent.length === 0) {
      return null;
    }

    return (
      <Card
        title={
          <Space>
            <FireOutlined style={{ color: '#fa541c' }} />
            <span>特色内容</span>
          </Space>
        }
        extra={
          <Button type="link" icon={<RightOutlined />}>
            查看更多
          </Button>
        }
        style={{ marginBottom: '24px' }}
      >
        <List
          itemLayout="horizontal"
          dataSource={navigation.featuredContent.slice(0, 5)}
          renderItem={(item) => (
            <List.Item
              style={{ cursor: 'pointer' }}
              onClick={() => onContentSelect(item.id)}
              actions={[
                <Space key="stats">
                  <EyeOutlined /> {item.viewCount}
                  <LikeOutlined /> {item.likeCount}
                </Space>
              ]}
            >
              <List.Item.Meta
                avatar={
                  <Avatar
                    icon={<FileTextOutlined />}
                    style={{ backgroundColor: '#1890ff' }}
                  />
                }
                title={
                  <Space>
                    <span>{item.title}</span>
                    {item.featured && <Badge status="processing" text="特色" />}
                  </Space>
                }
                description={
                  <Space direction="vertical" size="small">
                    <Text ellipsis style={{ maxWidth: '400px' }}>
                      {item.summary || item.content.substring(0, 100) + '...'}
                    </Text>
                    <Space size="small">
                      <Tag color="blue">{item.type}</Tag>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        <ClockCircleOutlined /> {item.updatedAt}
                      </Text>
                    </Space>
                  </Space>
                }
              />
            </List.Item>
          )}
        />
      </Card>
    );
  };

  // 渲染最新内容
  const renderRecentContent = () => {
    if (!statistics?.recentContent || statistics.recentContent.length === 0) {
      return null;
    }

    return (
      <Card
        title={
          <Space>
            <ClockCircleOutlined style={{ color: '#52c41a' }} />
            <span>最新更新</span>
          </Space>
        }
        extra={
          <Button type="link" icon={<RightOutlined />}>
            查看更多
          </Button>
        }
        style={{ marginBottom: '24px' }}
      >
        <List
          size="small"
          dataSource={statistics.recentContent.slice(0, 8)}
          renderItem={(item) => (
            <List.Item
              style={{ cursor: 'pointer' }}
              onClick={() => onContentSelect(item.id)}
            >
              <List.Item.Meta
                avatar={<Avatar size="small" icon={<FileTextOutlined />} />}
                title={
                  <Text ellipsis style={{ maxWidth: '300px' }}>
                    {item.title}
                  </Text>
                }
                description={
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {item.updatedAt}
                  </Text>
                }
              />
            </List.Item>
          )}
        />
      </Card>
    );
  };

  // 渲染热门标签
  const renderPopularTags = () => {
    if (!navigation?.popularTags || navigation.popularTags.length === 0) {
      return null;
    }

    return (
      <Card
        title="热门标签"
        style={{ marginBottom: '24px' }}
      >
        <Space wrap>
          {navigation.popularTags.slice(0, 20).map((tag) => (
            <Tag
              key={tag.id}
              color={tag.color || 'blue'}
              style={{ cursor: 'pointer' }}
              onClick={() => onSearch(`tag:${tag.name}`)}
            >
              {tag.name}
              {tag.usageCount > 0 && (
                <span style={{ marginLeft: '4px', opacity: 0.7 }}>
                  ({tag.usageCount})
                </span>
              )}
            </Tag>
          ))}
        </Space>
      </Card>
    );
  };

  return (
    <div>
      {/* 欢迎区域 */}
      <div style={{ textAlign: 'center', marginBottom: '40px' }}>
        <Title level={2}>
          <BookOutlined style={{ color: '#1890ff', marginRight: '8px' }} />
          Link-BOM-S 帮助手册
        </Title>
        <Paragraph style={{ fontSize: '16px', color: '#666', maxWidth: '600px', margin: '0 auto' }}>
          欢迎使用 Link-BOM-S 系统帮助手册。这里提供了完整的功能指南、操作说明和常见问题解答，
          帮助您快速掌握系统的使用方法。
        </Paragraph>
        
        {/* 搜索框 */}
        <div style={{ maxWidth: '500px', margin: '24px auto' }}>
          <Search
            placeholder="搜索您需要的帮助内容..."
            allowClear
            enterButton={
              <Button type="primary" icon={<SearchOutlined />}>
                搜索
              </Button>
            }
            size="large"
            onSearch={onSearch}
          />
        </div>
      </div>

      {/* 统计信息 */}
      {renderStatistics()}

      {/* 快速入口 */}
      {renderQuickEntries()}

      {/* 内容区域 */}
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={16}>
          {/* 特色内容 */}
          {renderFeaturedContent()}
        </Col>
        
        <Col xs={24} lg={8}>
          {/* 最新内容 */}
          {renderRecentContent()}
          
          {/* 热门标签 */}
          {renderPopularTags()}
        </Col>
      </Row>
    </div>
  );
};

export default HelpHome;
