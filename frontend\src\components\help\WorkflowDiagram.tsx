import React from 'react';
import { Card, Typography, Space, Tag, Divider } from 'antd';
import {
  ApartmentOutlined,
  ClockCircleOutlined,
  UserOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import MermaidDiagram from './MermaidDiagram';
import { WorkflowDiagram as WorkflowDiagramType } from '../../types/help';

const { Title, Text, Paragraph } = Typography;

interface WorkflowDiagramProps {
  workflow: WorkflowDiagramType;
  showDetails?: boolean;
}

/**
 * 工作流程图组件
 */
const WorkflowDiagram: React.FC<WorkflowDiagramProps> = ({
  workflow,
  showDetails = true
}) => {
  // 预定义的工作流程图
  const predefinedWorkflows = {
    // BOM创建流程
    bomCreation: `
      graph TD
        A[开始] --> B[创建BOM]
        B --> C{选择BOM类型}
        C -->|产品BOM| D[添加产品信息]
        C -->|工程BOM| E[添加工程信息]
        C -->|制造BOM| F[添加制造信息]
        D --> G[添加物料清单]
        E --> G
        F --> G
        G --> H[设置物料属性]
        H --> I[配置替代料]
        I --> J[设置用量]
        J --> K{验证BOM}
        K -->|验证失败| L[修正错误]
        L --> K
        K -->|验证成功| M[提交审核]
        M --> N{审核结果}
        N -->|审核通过| O[发布BOM]
        N -->|审核拒绝| P[修改BOM]
        P --> M
        O --> Q[结束]
        
        style A fill:#e1f5fe
        style Q fill:#e8f5e8
        style K fill:#fff3e0
        style N fill:#fff3e0
    `,
    
    // 采购流程
    procurement: `
      graph TD
        A[采购需求] --> B[创建采购申请]
        B --> C[部门主管审批]
        C -->|拒绝| D[修改申请]
        D --> C
        C -->|通过| E[采购部门审核]
        E -->|拒绝| F[重新申请]
        F --> B
        E -->|通过| G[供应商询价]
        G --> H[比价分析]
        H --> I[选择供应商]
        I --> J[创建采购订单]
        J --> K[财务审批]
        K -->|拒绝| L[修改订单]
        L --> K
        K -->|通过| M[发送订单]
        M --> N[供应商确认]
        N --> O[生产/发货]
        O --> P[收货验收]
        P --> Q{质检结果}
        Q -->|合格| R[入库]
        Q -->|不合格| S[退货/换货]
        S --> O
        R --> T[付款]
        T --> U[结束]
        
        style A fill:#e1f5fe
        style U fill:#e8f5e8
        style Q fill:#fff3e0
    `,
    
    // 库存管理流程
    inventory: `
      graph TD
        A[库存监控] --> B{库存预警}
        B -->|正常| A
        B -->|低库存| C[生成补货建议]
        B -->|高库存| D[生成调拨建议]
        C --> E[创建采购申请]
        D --> F[创建调拨单]
        E --> G[采购流程]
        F --> H[调拨流程]
        G --> I[收货入库]
        H --> J[出库转移]
        I --> K[更新库存]
        J --> K
        K --> L[库存盘点]
        L --> M{盘点差异}
        M -->|无差异| A
        M -->|有差异| N[差异分析]
        N --> O[调整库存]
        O --> A
        
        style A fill:#e1f5fe
        style B fill:#fff3e0
        style M fill:#fff3e0
    `,
    
    // 质量控制流程
    qualityControl: `
      graph TD
        A[物料到货] --> B[质检申请]
        B --> C[分配质检员]
        C --> D[执行检验]
        D --> E{检验结果}
        E -->|合格| F[开具合格证]
        E -->|不合格| G[开具不合格报告]
        F --> H[允许入库]
        G --> I{处理方式}
        I -->|退货| J[联系供应商退货]
        I -->|返工| K[返工处理]
        I -->|让步接收| L[特采审批]
        J --> M[退货处理]
        K --> D
        L --> N{特采结果}
        N -->|批准| F
        N -->|拒绝| G
        H --> O[入库完成]
        M --> P[采购重新下单]
        O --> Q[结束]
        P --> Q
        
        style A fill:#e1f5fe
        style Q fill:#e8f5e8
        style E fill:#fff3e0
        style I fill:#fff3e0
        style N fill:#fff3e0
    `
  };

  // 获取图表定义
  const getDiagramDefinition = () => {
    if (workflow.diagramData) {
      return workflow.diagramData;
    }
    
    // 根据类型返回预定义的图表
    switch (workflow.type) {
      case 'workflow':
        return predefinedWorkflows.bomCreation;
      case 'business_process':
        return predefinedWorkflows.procurement;
      case 'system_flow':
        return predefinedWorkflows.inventory;
      default:
        return predefinedWorkflows.qualityControl;
    }
  };

  // 获取类型标签颜色
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'workflow':
        return 'blue';
      case 'business_process':
        return 'green';
      case 'system_flow':
        return 'orange';
      default:
        return 'purple';
    }
  };

  // 获取类型描述
  const getTypeDescription = (type: string) => {
    switch (type) {
      case 'workflow':
        return '工作流程';
      case 'business_process':
        return '业务流程';
      case 'system_flow':
        return '系统流程';
      default:
        return '其他流程';
    }
  };

  return (
    <div>
      {showDetails && (
        <Card style={{ marginBottom: '16px' }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Title level={4} style={{ marginBottom: '8px' }}>
                <ApartmentOutlined style={{ marginRight: '8px', color: '#1890ff' }} />
                {workflow.title}
              </Title>
              
              <Space wrap>
                <Tag color={getTypeColor(workflow.type)}>
                  {getTypeDescription(workflow.type)}
                </Tag>
                <Tag color="default">{workflow.category}</Tag>
                {workflow.tags.map((tag, index) => (
                  <Tag key={index} color="blue">
                    {tag}
                  </Tag>
                ))}
              </Space>
            </div>
            
            {workflow.description && (
              <Paragraph style={{ margin: 0 }}>
                {workflow.description}
              </Paragraph>
            )}
            
            <Space wrap>
              <Space>
                <ClockCircleOutlined />
                <Text type="secondary">创建时间: {workflow.createdAt}</Text>
              </Space>
              <Space>
                <FileTextOutlined />
                <Text type="secondary">更新时间: {workflow.updatedAt}</Text>
              </Space>
            </Space>
          </Space>
        </Card>
      )}
      
      <MermaidDiagram
        definition={getDiagramDefinition()}
        title={showDetails ? undefined : workflow.title}
        height={500}
        showControls={true}
      />
      
      {showDetails && (
        <Card title="流程说明" style={{ marginTop: '16px' }}>
          <div>
            <Title level={5}>流程步骤说明：</Title>
            <ul style={{ paddingLeft: '20px' }}>
              {workflow.type === 'workflow' && (
                <>
                  <li>创建BOM：选择合适的BOM类型（产品BOM、工程BOM、制造BOM）</li>
                  <li>添加信息：根据BOM类型添加相应的产品、工程或制造信息</li>
                  <li>物料管理：添加物料清单，设置物料属性和替代料</li>
                  <li>验证审核：验证BOM完整性，提交审核流程</li>
                  <li>发布使用：审核通过后发布BOM供生产使用</li>
                </>
              )}
              {workflow.type === 'business_process' && (
                <>
                  <li>需求申请：根据生产计划或库存需求创建采购申请</li>
                  <li>审批流程：部门主管和采购部门的多级审批</li>
                  <li>供应商管理：询价、比价、选择合适的供应商</li>
                  <li>订单管理：创建采购订单，财务审批后发送</li>
                  <li>收货验收：供应商发货后进行收货和质量验收</li>
                  <li>入库付款：合格物料入库，按约定付款</li>
                </>
              )}
              {workflow.type === 'system_flow' && (
                <>
                  <li>库存监控：实时监控库存水平，设置预警阈值</li>
                  <li>补货管理：低库存时自动生成补货建议</li>
                  <li>调拨管理：高库存时建议调拨到其他仓库</li>
                  <li>盘点管理：定期进行库存盘点，处理差异</li>
                </>
              )}
            </ul>
            
            <Divider />
            
            <Title level={5}>注意事项：</Title>
            <ul style={{ paddingLeft: '20px' }}>
              <li>所有流程节点都需要相应权限才能操作</li>
              <li>关键决策点需要多人审批确认</li>
              <li>系统会自动记录所有操作日志</li>
              <li>异常情况下可以启动应急处理流程</li>
            </ul>
          </div>
        </Card>
      )}
    </div>
  );
};

export default WorkflowDiagram;
