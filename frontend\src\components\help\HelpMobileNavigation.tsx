import React from 'react';
import {
  Drawer,
  <PERSON>u,
  Typo<PERSON>,
  Space,
  Button,
  Divider,
  Input
} from 'antd';
import {
  HomeOutlined,
  SearchOutlined,
  BookOutlined,
  UserOutlined,
  DatabaseOutlined,
  ToolOutlined,
  QuestionCircleOutlined,
  FileTextOutlined,
  CloseOutlined,
  FolderOutlined,
  FolderOpenOutlined
} from '@ant-design/icons';
import { HelpCategory } from '../../types/help';

const { Title } = Typography;
const { Search } = Input;

interface HelpMobileNavigationProps {
  visible: boolean;
  categories: HelpCategory[];
  onCategorySelect: (categoryId: number) => void;
  onClose: () => void;
}

/**
 * 移动端帮助导航组件
 */
const HelpMobileNavigation: React.FC<HelpMobileNavigationProps> = ({
  visible,
  categories,
  onCategorySelect,
  onClose
}) => {
  // 构建菜单项
  const buildMenuItems = (categories: HelpCategory[]): any[] => {
    return categories.map((category) => ({
      key: category.id.toString(),
      icon: category.children && category.children.length > 0 
        ? <FolderOutlined /> 
        : <FileTextOutlined />,
      label: category.name,
      children: category.children && category.children.length > 0 
        ? buildMenuItems(category.children)
        : undefined,
      onClick: () => {
        onCategorySelect(category.id);
        onClose();
      }
    }));
  };

  // 快捷菜单项
  const quickMenuItems = [
    {
      key: 'home',
      icon: <HomeOutlined />,
      label: '首页',
      onClick: () => {
        window.location.href = '/help';
        onClose();
      }
    },
    {
      key: 'functions',
      icon: <BookOutlined />,
      label: '功能指南',
      onClick: () => {
        onCategorySelect(1);
        onClose();
      }
    },
    {
      key: 'roles',
      icon: <UserOutlined />,
      label: '角色权限',
      onClick: () => {
        onCategorySelect(2);
        onClose();
      }
    },
    {
      key: 'knowledge',
      icon: <DatabaseOutlined />,
      label: '行业知识',
      onClick: () => {
        onCategorySelect(3);
        onClose();
      }
    },
    {
      key: 'workflows',
      icon: <ToolOutlined />,
      label: '业务流程',
      onClick: () => {
        onCategorySelect(4);
        onClose();
      }
    },
    {
      key: 'faq',
      icon: <QuestionCircleOutlined />,
      label: '常见问题',
      onClick: () => {
        onCategorySelect(5);
        onClose();
      }
    }
  ];

  return (
    <Drawer
      title={
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Space>
            <BookOutlined style={{ color: '#1890ff' }} />
            <Title level={4} style={{ margin: 0 }}>
              帮助手册
            </Title>
          </Space>
          <Button
            type="text"
            icon={<CloseOutlined />}
            onClick={onClose}
          />
        </Space>
      }
      placement="left"
      onClose={onClose}
      open={visible}
      width={280}
      closable={false}
      bodyStyle={{ padding: 0 }}
    >
      <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* 搜索框 */}
        <div style={{ padding: '16px' }}>
          <Search
            placeholder="搜索帮助内容..."
            allowClear
            enterButton={<SearchOutlined />}
            onSearch={(value) => {
              if (value.trim()) {
                window.location.href = `/help/search?q=${encodeURIComponent(value)}`;
                onClose();
              }
            }}
          />
        </div>

        <Divider style={{ margin: 0 }} />

        {/* 快捷菜单 */}
        <div style={{ padding: '8px 0' }}>
          <Menu
            mode="inline"
            style={{ border: 'none' }}
            items={quickMenuItems}
          />
        </div>

        <Divider style={{ margin: 0 }} />

        {/* 分类菜单 */}
        <div style={{ flex: 1, overflow: 'auto' }}>
          <div style={{ padding: '16px 16px 8px 16px' }}>
            <Title level={5} style={{ margin: 0 }}>
              内容分类
            </Title>
          </div>
          
          <Menu
            mode="inline"
            style={{ border: 'none' }}
            items={buildMenuItems(categories)}
          />
        </div>

        {/* 底部操作 */}
        <div style={{ padding: '16px', borderTop: '1px solid #f0f0f0' }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button
              type="link"
              icon={<QuestionCircleOutlined />}
              style={{ padding: 0, height: 'auto', justifyContent: 'flex-start' }}
              onClick={() => {
                window.location.href = '/help/search?q=帮助';
                onClose();
              }}
            >
              使用帮助
            </Button>
            <Button
              type="link"
              icon={<ToolOutlined />}
              style={{ padding: 0, height: 'auto', justifyContent: 'flex-start' }}
            >
              设置
            </Button>
          </Space>
        </div>
      </div>
    </Drawer>
  );
};

export default HelpMobileNavigation;
