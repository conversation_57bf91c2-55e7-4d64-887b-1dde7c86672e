import React, { useState } from 'react';
import {
  Space,
  Input,
  Button,
  Breadcrumb,
  Typography,
  Dropdown,
  Avatar,
  Badge,
  Tooltip,
  Menu
} from 'antd';
import {
  SearchOutlined,
  MenuOutlined,
  HomeOutlined,
  UserOutlined,
  BellOutlined,
  SettingOutlined,
  QuestionCircleOutlined,
  PrinterOutlined,
  ShareAltOutlined,
  BookmarkOutlined
} from '@ant-design/icons';
import { HelpContent, HelpCategory } from '../../types/help';

const { Search } = Input;
const { Text, Title } = Typography;

interface HelpHeaderProps {
  currentView: 'home' | 'search' | 'content' | 'category';
  currentContent?: HelpContent | null;
  currentCategory?: HelpCategory | null;
  isMobile: boolean;
  onSearch: (keyword: string, filters?: any) => void;
  onGoHome: () => void;
  onMobileMenuToggle: () => void;
}

/**
 * 帮助手册头部组件
 */
const HelpHeader: React.FC<HelpHeaderProps> = ({
  currentView,
  currentContent,
  currentCategory,
  isMobile,
  onSearch,
  onGoHome,
  onMobileMenuToggle
}) => {
  const [searchKeyword, setSearchKeyword] = useState('');

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
    if (value.trim()) {
      onSearch(value.trim());
    }
  };

  // 生成面包屑
  const generateBreadcrumb = () => {
    const items = [
      {
        title: (
          <Button
            type="link"
            icon={<HomeOutlined />}
            onClick={onGoHome}
            style={{ padding: 0 }}
          >
            首页
          </Button>
        )
      }
    ];

    switch (currentView) {
      case 'content':
        if (currentContent) {
          if (currentContent.category) {
            items.push({
              title: (
                <Button
                  type="link"
                  onClick={() => {/* 跳转到分类页面 */}}
                  style={{ padding: 0 }}
                >
                  {currentContent.category.name}
                </Button>
              )
            });
          }
          items.push({
            title: <Text>{currentContent.title}</Text>
          });
        }
        break;

      case 'category':
        if (currentCategory) {
          items.push({
            title: <Text>{currentCategory.name}</Text>
          });
        }
        break;

      case 'search':
        items.push({
          title: <Text>搜索结果</Text>
        });
        break;

      default:
        break;
    }

    return items;
  };

  // 用户菜单
  const userMenu = (
    <Menu
      items={[
        {
          key: 'profile',
          icon: <UserOutlined />,
          label: '个人资料'
        },
        {
          key: 'bookmarks',
          icon: <BookmarkOutlined />,
          label: '我的收藏'
        },
        {
          key: 'settings',
          icon: <SettingOutlined />,
          label: '设置'
        },
        {
          type: 'divider'
        },
        {
          key: 'help',
          icon: <QuestionCircleOutlined />,
          label: '使用帮助'
        }
      ]}
    />
  );

  // 操作菜单（针对当前内容）
  const actionMenu = currentContent ? (
    <Menu
      items={[
        {
          key: 'print',
          icon: <PrinterOutlined />,
          label: '打印',
          onClick: () => window.print()
        },
        {
          key: 'share',
          icon: <ShareAltOutlined />,
          label: '分享',
          onClick: () => {
            if (navigator.share) {
              navigator.share({
                title: currentContent.title,
                url: window.location.href
              });
            } else {
              navigator.clipboard.writeText(window.location.href);
            }
          }
        },
        {
          key: 'bookmark',
          icon: <BookmarkOutlined />,
          label: '收藏'
        }
      ]}
    />
  ) : null;

  return (
    <div style={{ 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'space-between',
      height: '64px'
    }}>
      {/* 左侧：移动端菜单按钮 + 面包屑/标题 */}
      <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
        {isMobile && (
          <Button
            type="text"
            icon={<MenuOutlined />}
            onClick={onMobileMenuToggle}
            style={{ marginRight: '16px' }}
          />
        )}

        {isMobile ? (
          // 移动端显示简化标题
          <div>
            {currentView === 'content' && currentContent && (
              <Title level={5} style={{ margin: 0 }} ellipsis>
                {currentContent.title}
              </Title>
            )}
            {currentView === 'category' && currentCategory && (
              <Title level={5} style={{ margin: 0 }}>
                {currentCategory.name}
              </Title>
            )}
            {currentView === 'search' && (
              <Title level={5} style={{ margin: 0 }}>
                搜索结果
              </Title>
            )}
            {currentView === 'home' && (
              <Title level={5} style={{ margin: 0 }}>
                帮助手册
              </Title>
            )}
          </div>
        ) : (
          // 桌面端显示面包屑
          <Breadcrumb items={generateBreadcrumb()} />
        )}
      </div>

      {/* 中间：搜索框（桌面端） */}
      {!isMobile && (
        <div style={{ flex: 1, maxWidth: '400px', margin: '0 24px' }}>
          <Search
            placeholder="搜索帮助内容..."
            allowClear
            enterButton={<SearchOutlined />}
            value={searchKeyword}
            onChange={(e) => setSearchKeyword(e.target.value)}
            onSearch={handleSearch}
            style={{ width: '100%' }}
          />
        </div>
      )}

      {/* 右侧：操作按钮 */}
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Space size="middle">
          {/* 移动端搜索按钮 */}
          {isMobile && (
            <Button
              type="text"
              icon={<SearchOutlined />}
              onClick={() => {
                // 打开搜索模态框或跳转到搜索页面
              }}
            />
          )}

          {/* 内容操作菜单 */}
          {actionMenu && (
            <Dropdown overlay={actionMenu} placement="bottomRight">
              <Button type="text" icon={<SettingOutlined />} />
            </Dropdown>
          )}

          {/* 通知 */}
          <Tooltip title="通知">
            <Badge count={0} size="small">
              <Button type="text" icon={<BellOutlined />} />
            </Badge>
          </Tooltip>

          {/* 用户菜单 */}
          <Dropdown overlay={userMenu} placement="bottomRight">
            <Button type="text" style={{ padding: '4px' }}>
              <Avatar size="small" icon={<UserOutlined />} />
            </Button>
          </Dropdown>
        </Space>
      </div>
    </div>
  );
};

export default HelpHeader;
