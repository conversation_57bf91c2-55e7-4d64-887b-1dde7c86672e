# Link-BOM-S 帮助手册模块

## 概述

帮助手册模块是Link-BOM-S系统的重要组成部分，为用户提供完整的系统使用指南、操作说明和问题解答。该模块支持多种内容类型，包括功能指南、业务流程、常见问题、视频教程等。

## 功能特性

### 1. 内容管理
- **多类型内容支持**：功能指南、角色权限、行业知识、工作流程、业务流程、FAQ、教程、公告、流程图、视频教程
- **富文本编辑**：支持HTML格式的内容编辑和展示
- **版本管理**：支持内容版本控制和历史记录
- **审核流程**：完整的内容审核和发布流程
- **特色内容**：支持设置特色内容进行重点推荐

### 2. 分类管理
- **树形结构**：支持多级分类的树形组织结构
- **分类权限**：可控制分类的可见性和启用状态
- **排序管理**：支持分类和内容的自定义排序
- **路径管理**：自动维护分类路径信息

### 3. 标签系统
- **多类型标签**：内容标签、分类标签、系统标签、自定义标签
- **标签云**：展示热门标签和使用统计
- **智能推荐**：基于内容自动推荐相关标签

### 4. 搜索功能
- **全文搜索**：支持标题、内容、关键词的全文搜索
- **高级筛选**：按类型、分类、作者、状态等条件筛选
- **搜索建议**：提供搜索关键词建议
- **结果高亮**：搜索结果中关键词高亮显示

### 5. 附件管理
- **多格式支持**：图片、文档、视频、音频、压缩包等
- **文件上传**：支持拖拽上传和批量上传
- **预览功能**：支持常见格式的在线预览
- **下载统计**：记录附件下载次数

### 6. 反馈系统
- **多种反馈类型**：点赞、点踩、评论、建议、错误报告等
- **评分系统**：支持1-5星评分
- **匿名反馈**：支持匿名提交反馈
- **反馈处理**：完整的反馈处理和回复流程

### 7. 流程图支持
- **Mermaid集成**：支持Mermaid语法的流程图展示
- **交互控制**：缩放、全屏、下载等交互功能
- **预定义模板**：提供常用业务流程模板

### 8. 移动端适配
- **响应式设计**：完美适配各种屏幕尺寸
- **移动端导航**：专门的移动端导航界面
- **触摸优化**：针对触摸操作进行优化

## 技术架构

### 后端技术栈
- **Spring Boot 3.x**：主框架
- **Spring Data JPA**：数据访问层
- **PostgreSQL**：数据库
- **Spring Security**：安全框架
- **Swagger/OpenAPI**：API文档

### 前端技术栈
- **React 18**：UI框架
- **TypeScript**：类型安全
- **Ant Design**：UI组件库
- **Redux Toolkit**：状态管理
- **React Router**：路由管理
- **Mermaid**：流程图渲染

## 数据库设计

### 核心表结构

#### 1. help_category（帮助分类表）
```sql
- id: 主键
- name: 分类名称
- description: 分类描述
- parent_id: 父分类ID
- category_path: 分类路径
- level: 分类层级
- sort_order: 排序号
- icon: 图标
- is_visible: 是否可见
- is_enabled: 是否启用
- is_deleted: 是否删除
```

#### 2. help_content（帮助内容表）
```sql
- id: 主键
- title: 标题
- content: 内容
- summary: 摘要
- type: 内容类型
- category_id: 分类ID
- status: 状态
- view_count: 浏览量
- like_count: 点赞数
- is_featured: 是否特色
- is_published: 是否发布
```

#### 3. help_tag（帮助标签表）
```sql
- id: 主键
- name: 标签名称
- description: 标签描述
- type: 标签类型
- color: 标签颜色
- usage_count: 使用次数
```

#### 4. help_attachment（帮助附件表）
```sql
- id: 主键
- content_id: 内容ID
- file_name: 文件名
- original_name: 原始文件名
- file_path: 文件路径
- content_type: 文件类型
- file_size: 文件大小
- download_count: 下载次数
```

#### 5. help_feedback（帮助反馈表）
```sql
- id: 主键
- content_id: 内容ID
- type: 反馈类型
- rating: 评分
- content: 反馈内容
- status: 处理状态
- reply: 回复内容
```

## API接口

### 分类管理
- `GET /help/categories/tree` - 获取分类树
- `GET /help/categories/{id}` - 获取分类详情
- `POST /help/categories` - 创建分类
- `PUT /help/categories/{id}` - 更新分类
- `DELETE /help/categories/{id}` - 删除分类

### 内容管理
- `GET /help/content` - 获取内容列表
- `GET /help/content/{id}` - 获取内容详情
- `POST /help/content` - 创建内容
- `PUT /help/content/{id}` - 更新内容
- `DELETE /help/content/{id}` - 删除内容
- `GET /help/content/search` - 搜索内容

### 标签管理
- `GET /help/tags` - 获取标签列表
- `GET /help/tags/popular` - 获取热门标签
- `POST /help/tags` - 创建标签
- `PUT /help/tags/{id}` - 更新标签

### 附件管理
- `POST /help/attachments/upload` - 上传附件
- `GET /help/attachments/{id}/download` - 下载附件
- `DELETE /help/attachments/{id}` - 删除附件

### 反馈管理
- `POST /help/feedback` - 提交反馈
- `GET /help/feedback/content/{id}` - 获取内容反馈
- `PUT /help/feedback/{id}/reply` - 回复反馈

## 部署说明

### 环境要求
- Java 17+
- Node.js 16+
- PostgreSQL 13+
- Redis 6+（可选，用于缓存）

### 配置文件
```yaml
# application.yml
spring:
  datasource:
    url: *****************************************
    username: ${DB_USERNAME:linkboms}
    password: ${DB_PASSWORD:password}
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
  
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

help:
  file:
    upload-path: ${UPLOAD_PATH:/var/linkboms/uploads}
    max-size: 10485760  # 10MB
  content:
    auto-publish: false
    require-review: true
```

### 数据库初始化
```bash
# 执行数据库迁移脚本
flyway migrate

# 或使用Liquibase
liquibase update
```

### 前端构建
```bash
# 安装依赖
npm install

# 构建生产版本
npm run build

# 启动开发服务器
npm run dev
```

## 使用指南

### 管理员操作
1. **分类管理**：创建和维护帮助分类体系
2. **内容审核**：审核用户提交的帮助内容
3. **权限配置**：设置用户角色和权限
4. **系统配置**：配置系统参数和选项

### 内容编辑者操作
1. **创建内容**：编写帮助文档和教程
2. **上传附件**：添加相关的图片、文档等附件
3. **设置标签**：为内容添加合适的标签
4. **提交审核**：完成内容后提交审核

### 普通用户操作
1. **浏览内容**：按分类浏览帮助内容
2. **搜索功能**：使用关键词搜索相关内容
3. **提交反馈**：对内容进行评价和反馈
4. **收藏内容**：收藏有用的帮助内容

## 最佳实践

### 内容编写
1. **标题明确**：使用清晰、描述性的标题
2. **结构清晰**：使用标题、列表、表格等组织内容
3. **图文并茂**：适当添加截图和图表说明
4. **及时更新**：定期更新过时的内容

### 分类管理
1. **层次合理**：避免分类层次过深
2. **命名规范**：使用统一的命名规范
3. **定期整理**：定期清理无用的分类

### 标签使用
1. **标签统一**：使用统一的标签命名
2. **数量适中**：每个内容使用3-5个标签
3. **定期清理**：清理无用的标签

## 故障排除

### 常见问题
1. **文件上传失败**：检查文件大小和格式限制
2. **搜索结果不准确**：重建搜索索引
3. **图片显示异常**：检查文件路径配置
4. **权限错误**：检查用户角色和权限设置

### 性能优化
1. **数据库索引**：确保关键字段有适当的索引
2. **缓存策略**：使用Redis缓存热门内容
3. **CDN加速**：使用CDN加速静态资源
4. **图片压缩**：自动压缩上传的图片

## 更新日志

### v1.0.0 (2024-08-20)
- 初始版本发布
- 完整的帮助手册功能
- 支持多种内容类型
- 移动端适配
- Mermaid流程图支持

## 联系支持

如有问题或建议，请联系：
- 邮箱：<EMAIL>
- 电话：400-123-4567
- 在线支持：系统内反馈功能
