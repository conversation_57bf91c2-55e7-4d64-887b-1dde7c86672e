package com.linkboms.repository.help;

import com.linkboms.entity.help.HelpFeedback;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 帮助反馈Repository接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface HelpFeedbackRepository extends JpaRepository<HelpFeedback, Long> {

    /**
     * 根据内容ID查找反馈
     */
    Page<HelpFeedback> findByContentIdAndDeletedFalseOrderByCreatedAtDesc(
        Long contentId, Pageable pageable
    );

    /**
     * 根据用户ID查找反馈
     */
    Page<HelpFeedback> findByUserIdAndDeletedFalseOrderByCreatedAtDesc(
        Long userId, Pageable pageable
    );

    /**
     * 根据状态查找反馈
     */
    Page<HelpFeedback> findByStatusAndDeletedFalseOrderByCreatedAtAsc(
        HelpFeedback.FeedbackStatus status, Pageable pageable
    );

    /**
     * 根据类型查找反馈
     */
    Page<HelpFeedback> findByTypeAndDeletedFalseOrderByCreatedAtDesc(
        HelpFeedback.FeedbackType type, Pageable pageable
    );

    /**
     * 查找待处理的反馈
     */
    List<HelpFeedback> findByStatusAndDeletedFalseOrderByCreatedAtAsc(
        HelpFeedback.FeedbackStatus status
    );

    /**
     * 查找指定内容的平均评分
     */
    @Query("SELECT AVG(f.rating) FROM HelpFeedback f WHERE f.contentId = :contentId AND f.rating IS NOT NULL AND f.deleted = false")
    Double findAverageRatingByContentId(@Param("contentId") Long contentId);

    /**
     * 统计指定内容的评分分布
     */
    @Query("SELECT f.rating, COUNT(f) FROM HelpFeedback f WHERE f.contentId = :contentId AND f.rating IS NOT NULL AND f.deleted = false GROUP BY f.rating ORDER BY f.rating")
    List<Object[]> findRatingDistributionByContentId(@Param("contentId") Long contentId);

    /**
     * 统计指定内容的反馈数量
     */
    Long countByContentIdAndDeletedFalse(Long contentId);

    /**
     * 统计指定用户的反馈数量
     */
    Long countByUserIdAndDeletedFalse(Long userId);

    /**
     * 查找正面反馈
     */
    @Query("SELECT f FROM HelpFeedback f WHERE f.rating >= 4 AND f.deleted = false ORDER BY f.createdAt DESC")
    List<HelpFeedback> findPositiveFeedback(Pageable pageable);

    /**
     * 查找负面反馈
     */
    @Query("SELECT f FROM HelpFeedback f WHERE f.rating <= 2 AND f.deleted = false ORDER BY f.createdAt DESC")
    List<HelpFeedback> findNegativeFeedback(Pageable pageable);

    /**
     * 查找有用的反馈
     */
    List<HelpFeedback> findByHelpfulTrueAndDeletedFalseOrderByCreatedAtDesc(Pageable pageable);

    /**
     * 查找包含建议的反馈
     */
    @Query("SELECT f FROM HelpFeedback f WHERE f.suggestions IS NOT NULL AND f.suggestions != '' AND f.deleted = false ORDER BY f.createdAt DESC")
    List<HelpFeedback> findFeedbackWithSuggestions(Pageable pageable);

    /**
     * 根据管理员查找已回复的反馈
     */
    Page<HelpFeedback> findByAdminIdAndDeletedFalseOrderByRepliedAtDesc(
        Long adminId, Pageable pageable
    );

    /**
     * 查找已回复的反馈
     */
    @Query("SELECT f FROM HelpFeedback f WHERE f.adminReply IS NOT NULL AND f.adminReply != '' AND f.deleted = false ORDER BY f.repliedAt DESC")
    Page<HelpFeedback> findRepliedFeedback(Pageable pageable);

    /**
     * 查找未回复的反馈
     */
    @Query("SELECT f FROM HelpFeedback f WHERE (f.adminReply IS NULL OR f.adminReply = '') AND f.deleted = false ORDER BY f.createdAt ASC")
    Page<HelpFeedback> findUnrepliedFeedback(Pageable pageable);

    /**
     * 统计各状态的反馈数量
     */
    @Query("SELECT f.status, COUNT(f) FROM HelpFeedback f WHERE f.deleted = false GROUP BY f.status")
    List<Object[]> countByStatus();

    /**
     * 统计各类型的反馈数量
     */
    @Query("SELECT f.type, COUNT(f) FROM HelpFeedback f WHERE f.deleted = false GROUP BY f.type")
    List<Object[]> countByType();

    /**
     * 统计各评分的反馈数量
     */
    @Query("SELECT f.rating, COUNT(f) FROM HelpFeedback f WHERE f.rating IS NOT NULL AND f.deleted = false GROUP BY f.rating ORDER BY f.rating")
    List<Object[]> countByRating();

    /**
     * 查找指定时间范围内的反馈
     */
    @Query("SELECT f FROM HelpFeedback f WHERE f.createdAt BETWEEN :startDate AND :endDate AND f.deleted = false ORDER BY f.createdAt DESC")
    List<HelpFeedback> findByDateRange(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );

    /**
     * 查找指定IP地址的反馈
     */
    List<HelpFeedback> findByIpAddressAndDeletedFalseOrderByCreatedAtDesc(String ipAddress);

    /**
     * 查找匿名反馈
     */
    Page<HelpFeedback> findByAnonymousTrueAndDeletedFalseOrderByCreatedAtDesc(Pageable pageable);

    /**
     * 软删除反馈
     */
    @Modifying
    @Query("UPDATE HelpFeedback f SET f.deleted = true WHERE f.id = :id")
    void softDelete(@Param("id") Long id);

    /**
     * 批量软删除
     */
    @Modifying
    @Query("UPDATE HelpFeedback f SET f.deleted = true WHERE f.id IN :ids")
    void batchSoftDelete(@Param("ids") List<Long> ids);

    /**
     * 恢复已删除的反馈
     */
    @Modifying
    @Query("UPDATE HelpFeedback f SET f.deleted = false WHERE f.id = :id")
    void restore(@Param("id") Long id);

    /**
     * 更新反馈状态
     */
    @Modifying
    @Query("UPDATE HelpFeedback f SET f.status = :status WHERE f.id = :id")
    void updateStatus(@Param("id") Long id, @Param("status") HelpFeedback.FeedbackStatus status);

    /**
     * 批量更新反馈状态
     */
    @Modifying
    @Query("UPDATE HelpFeedback f SET f.status = :status WHERE f.id IN :ids")
    void batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") HelpFeedback.FeedbackStatus status);

    /**
     * 添加管理员回复
     */
    @Modifying
    @Query("UPDATE HelpFeedback f SET f.adminReply = :reply, f.adminId = :adminId, f.adminName = :adminName, f.repliedAt = :repliedAt, f.status = :status WHERE f.id = :id")
    void addAdminReply(
        @Param("id") Long id,
        @Param("reply") String reply,
        @Param("adminId") Long adminId,
        @Param("adminName") String adminName,
        @Param("repliedAt") LocalDateTime repliedAt,
        @Param("status") HelpFeedback.FeedbackStatus status
    );

    /**
     * 检查用户是否已对指定内容提交反馈
     */
    boolean existsByContentIdAndUserIdAndDeletedFalse(Long contentId, Long userId);

    /**
     * 查找用户对指定内容的反馈
     */
    List<HelpFeedback> findByContentIdAndUserIdAndDeletedFalse(Long contentId, Long userId);

    /**
     * 根据关键词搜索反馈
     */
    @Query("SELECT f FROM HelpFeedback f WHERE " +
           "(LOWER(f.comment) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(f.suggestions) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(f.adminReply) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "f.deleted = false ORDER BY f.createdAt DESC")
    Page<HelpFeedback> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 查找最近的反馈
     */
    List<HelpFeedback> findTop10ByDeletedFalseOrderByCreatedAtDesc();

    /**
     * 统计指定时间范围内的反馈数量
     */
    @Query("SELECT COUNT(f) FROM HelpFeedback f WHERE f.createdAt BETWEEN :startDate AND :endDate AND f.deleted = false")
    Long countByDateRange(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );

    /**
     * 统计指定时间范围内的平均评分
     */
    @Query("SELECT AVG(f.rating) FROM HelpFeedback f WHERE f.createdAt BETWEEN :startDate AND :endDate AND f.rating IS NOT NULL AND f.deleted = false")
    Double findAverageRatingByDateRange(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );
}