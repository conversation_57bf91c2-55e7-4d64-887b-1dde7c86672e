package com.linkboms.repository.help;

import com.linkboms.entity.help.HelpFeedback;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 帮助反馈Repository接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface HelpFeedbackRepository extends JpaRepository<HelpFeedback, Long> {

    /**
     * 根据内容ID查找反馈
     */
    Page<HelpFeedback> findByContentIdAndDeletedFalseOrderByCreatedAtDesc(Long contentId, Pageable pageable);

    /**
     * 根据类型查找反馈
     */
    Page<HelpFeedback> findByTypeAndDeletedFalseOrderByCreatedAtDesc(HelpFeedback.FeedbackType type, Pageable pageable);

    /**
     * 根据状态查找反馈
     */
    Page<HelpFeedback> findByStatusAndDeletedFalseOrderByCreatedAtDesc(HelpFeedback.FeedbackStatus status, Pageable pageable);

    /**
     * 查找待处理的反馈
     */
    Page<HelpFeedback> findByStatusInAndDeletedFalseOrderByCreatedAtAsc(
        List<HelpFeedback.FeedbackStatus> statuses, Pageable pageable);

    /**
     * 根据提交者查找反馈
     */
    Page<HelpFeedback> findBySubmitterIdAndDeletedFalseOrderByCreatedAtDesc(Long submitterId, Pageable pageable);

    /**
     * 根据回复者查找反馈
     */
    Page<HelpFeedback> findByReplierIdAndDeletedFalseOrderByCreatedAtDesc(Long replierId, Pageable pageable);

    /**
     * 查找匿名反馈
     */
    Page<HelpFeedback> findByAnonymousTrueAndDeletedFalseOrderByCreatedAtDesc(Pageable pageable);

    /**
     * 查找已回复的反馈
     */
    Page<HelpFeedback> findByReplyIsNotNullAndDeletedFalseOrderByRepliedAtDesc(Pageable pageable);

    /**
     * 查找未回复的反馈
     */
    Page<HelpFeedback> findByReplyIsNullAndStatusNotAndDeletedFalseOrderByCreatedAtAsc(
        HelpFeedback.FeedbackStatus excludeStatus, Pageable pageable);

    /**
     * 搜索反馈
     */
    @Query("SELECT f FROM HelpFeedback f WHERE " +
           "(LOWER(f.content) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(f.reply) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "(:type IS NULL OR f.type = :type) AND " +
           "(:status IS NULL OR f.status = :status) AND " +
           "f.deleted = false ORDER BY f.createdAt DESC")
    Page<HelpFeedback> searchFeedback(
        @Param("keyword") String keyword,
        @Param("type") HelpFeedback.FeedbackType type,
        @Param("status") HelpFeedback.FeedbackStatus status,
        Pageable pageable
    );

    /**
     * 高级搜索反馈
     */
    @Query("SELECT f FROM HelpFeedback f WHERE " +
           "(:keyword IS NULL OR " +
           "LOWER(f.content) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(f.reply) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "(:type IS NULL OR f.type = :type) AND " +
           "(:status IS NULL OR f.status = :status) AND " +
           "(:contentId IS NULL OR f.contentId = :contentId) AND " +
           "(:submitterId IS NULL OR f.submitterId = :submitterId) AND " +
           "(:replierId IS NULL OR f.replierId = :replierId) AND " +
           "(:startDate IS NULL OR f.createdAt >= :startDate) AND " +
           "(:endDate IS NULL OR f.createdAt <= :endDate) AND " +
           "(:minRating IS NULL OR f.rating >= :minRating) AND " +
           "(:maxRating IS NULL OR f.rating <= :maxRating) AND " +
           "f.deleted = false ORDER BY f.createdAt DESC")
    Page<HelpFeedback> advancedSearchFeedback(
        @Param("keyword") String keyword,
        @Param("type") HelpFeedback.FeedbackType type,
        @Param("status") HelpFeedback.FeedbackStatus status,
        @Param("contentId") Long contentId,
        @Param("submitterId") Long submitterId,
        @Param("replierId") Long replierId,
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate,
        @Param("minRating") Integer minRating,
        @Param("maxRating") Integer maxRating,
        Pageable pageable
    );

    /**
     * 查找指定评分范围的反馈
     */
    Page<HelpFeedback> findByRatingBetweenAndDeletedFalseOrderByCreatedAtDesc(
        Integer minRating, Integer maxRating, Pageable pageable);

    /**
     * 查找高评分反馈
     */
    @Query("SELECT f FROM HelpFeedback f WHERE f.rating >= :minRating AND f.deleted = false " +
           "ORDER BY f.rating DESC, f.createdAt DESC")
    List<HelpFeedback> findHighRatingFeedback(@Param("minRating") Integer minRating, Pageable pageable);

    /**
     * 查找低评分反馈
     */
    @Query("SELECT f FROM HelpFeedback f WHERE f.rating <= :maxRating AND f.deleted = false " +
           "ORDER BY f.rating ASC, f.createdAt DESC")
    List<HelpFeedback> findLowRatingFeedback(@Param("maxRating") Integer maxRating, Pageable pageable);

    /**
     * 查找指定时间段内的反馈
     */
    @Query("SELECT f FROM HelpFeedback f WHERE " +
           "f.createdAt >= :startDate AND f.createdAt <= :endDate AND f.deleted = false " +
           "ORDER BY f.createdAt DESC")
    List<HelpFeedback> findByDateRange(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate,
        Pageable pageable
    );

    /**
     * 更新反馈状态
     */
    @Modifying
    @Query("UPDATE HelpFeedback f SET f.status = :status, f.updatedAt = CURRENT_TIMESTAMP WHERE f.id = :id")
    void updateStatus(@Param("id") Long id, @Param("status") HelpFeedback.FeedbackStatus status);

    /**
     * 批量更新状态
     */
    @Modifying
    @Query("UPDATE HelpFeedback f SET f.status = :status, f.updatedAt = CURRENT_TIMESTAMP WHERE f.id IN :ids")
    void batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") HelpFeedback.FeedbackStatus status);

    /**
     * 批量软删除
     */
    @Modifying
    @Query("UPDATE HelpFeedback f SET f.deleted = true, f.updatedAt = CURRENT_TIMESTAMP WHERE f.id IN :ids")
    void batchSoftDelete(@Param("ids") List<Long> ids);

    /**
     * 根据内容ID批量软删除
     */
    @Modifying
    @Query("UPDATE HelpFeedback f SET f.deleted = true, f.updatedAt = CURRENT_TIMESTAMP WHERE f.contentId = :contentId")
    void softDeleteByContentId(@Param("contentId") Long contentId);

    /**
     * 统计指定内容的反馈数量
     */
    Long countByContentIdAndDeletedFalse(Long contentId);

    /**
     * 统计指定类型的反馈数量
     */
    Long countByTypeAndDeletedFalse(HelpFeedback.FeedbackType type);

    /**
     * 统计指定状态的反馈数量
     */
    Long countByStatusAndDeletedFalse(HelpFeedback.FeedbackStatus status);

    /**
     * 统计各状态的反馈数量
     */
    @Query("SELECT f.status, COUNT(f) FROM HelpFeedback f WHERE f.deleted = false GROUP BY f.status")
    List<Object[]> countByStatus();

    /**
     * 统计各类型的反馈数量
     */
    @Query("SELECT f.type, COUNT(f) FROM HelpFeedback f WHERE f.deleted = false GROUP BY f.type")
    List<Object[]> countByType();

    /**
     * 统计各评分的反馈数量
     */
    @Query("SELECT f.rating, COUNT(f) FROM HelpFeedback f WHERE f.rating IS NOT NULL AND f.deleted = false GROUP BY f.rating ORDER BY f.rating")
    List<Object[]> countByRating();

    /**
     * 计算平均评分
     */
    @Query("SELECT AVG(f.rating) FROM HelpFeedback f WHERE f.rating IS NOT NULL AND f.deleted = false")
    Double calculateAverageRating();

    /**
     * 计算指定内容的平均评分
     */
    @Query("SELECT AVG(f.rating) FROM HelpFeedback f WHERE f.contentId = :contentId AND f.rating IS NOT NULL AND f.deleted = false")
    Double calculateAverageRatingByContent(@Param("contentId") Long contentId);

    /**
     * 查找需要关注的反馈（低评分或负面反馈）
     */
    @Query("SELECT f FROM HelpFeedback f WHERE " +
           "(f.rating IS NOT NULL AND f.rating <= 2) OR " +
           "f.type IN ('DISLIKE', 'BUG_REPORT', 'CONTENT_ERROR') AND " +
           "f.status IN ('PENDING', 'PROCESSING') AND f.deleted = false " +
           "ORDER BY f.createdAt ASC")
    List<HelpFeedback> findCriticalFeedback(Pageable pageable);

    /**
     * 查找活跃用户的反馈
     */
    @Query("SELECT f FROM HelpFeedback f WHERE f.submitterId IN " +
           "(SELECT f2.submitterId FROM HelpFeedback f2 WHERE f2.submitterId IS NOT NULL AND f2.deleted = false " +
           "GROUP BY f2.submitterId HAVING COUNT(f2) >= :minFeedbackCount) " +
           "AND f.deleted = false ORDER BY f.createdAt DESC")
    List<HelpFeedback> findFeedbackFromActiveUsers(@Param("minFeedbackCount") Long minFeedbackCount, Pageable pageable);

    /**
     * 查找超时未回复的反馈
     */
    @Query("SELECT f FROM HelpFeedback f WHERE " +
           "f.reply IS NULL AND f.status IN ('PENDING', 'PROCESSING') AND " +
           "f.createdAt < :timeoutDate AND f.deleted = false " +
           "ORDER BY f.createdAt ASC")
    List<HelpFeedback> findOverdueFeedback(@Param("timeoutDate") LocalDateTime timeoutDate);

    /**
     * 统计指定时间段内的反馈趋势
     */
    @Query("SELECT DATE(f.createdAt), COUNT(f) FROM HelpFeedback f WHERE " +
           "f.createdAt >= :startDate AND f.createdAt <= :endDate AND f.deleted = false " +
           "GROUP BY DATE(f.createdAt) ORDER BY DATE(f.createdAt)")
    List<Object[]> getFeedbackTrend(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
}
