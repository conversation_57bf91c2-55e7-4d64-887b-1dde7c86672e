package com.linkboms.entity.help;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 帮助分类实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Table(name = "help_category")
@EntityListeners(AuditingEntityListener.class)
@Data
@EqualsAndHashCode(callSuper = false)
public class HelpCategory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "分类名称不能为空")
    @Size(max = 100, message = "分类名称长度不能超过100个字符")
    @Column(nullable = false, length = 100)
    private String name;

    @Size(max = 500, message = "分类描述长度不能超过500个字符")
    @Column(length = 500)
    private String description;

    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "category_path", length = 500)
    private String path;

    @Column(name = "level", nullable = false)
    private Integer level = 0;

    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;

    @Size(max = 50, message = "图标名称长度不能超过50个字符")
    @Column(length = 50)
    private String icon;

    @Column(name = "is_visible", nullable = false)
    private Boolean visible = true;

    @Column(name = "is_enabled", nullable = false)
    private Boolean enabled = true;

    @Column(name = "is_deleted", nullable = false)
    private Boolean deleted = false;

    @Size(max = 100, message = "创建者名称长度不能超过100个字符")
    @Column(length = 100)
    private String creator;

    @Column(name = "creator_id")
    private Long creatorId;

    @Size(max = 100, message = "更新者名称长度不能超过100个字符")
    @Column(length = 100)
    private String updater;

    @Column(name = "updater_id")
    private Long updaterId;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // 子分类列表（用于构建树形结构）
    @Transient
    private List<HelpCategory> children = new ArrayList<>();

    // 内容数量（统计信息）
    @Transient
    private Long contentCount = 0L;

    /**
     * 添加子分类
     */
    public void addChild(HelpCategory child) {
        if (children == null) {
            children = new ArrayList<>();
        }
        children.add(child);
        child.setParentId(this.id);
        child.setLevel(this.level + 1);
    }

    /**
     * 移除子分类
     */
    public void removeChild(HelpCategory child) {
        if (children != null) {
            children.remove(child);
            child.setParentId(null);
        }
    }

    /**
     * 是否为根分类
     */
    public boolean isRoot() {
        return parentId == null;
    }

    /**
     * 是否有子分类
     */
    public boolean hasChildren() {
        return children != null && !children.isEmpty();
    }

    /**
     * 获取完整路径
     */
    public String getFullPath() {
        if (path == null) {
            return name;
        }
        return path;
    }

    /**
     * 软删除
     */
    public void softDelete() {
        this.deleted = true;
        this.enabled = false;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 恢复删除
     */
    public void restore() {
        this.deleted = false;
        this.enabled = true;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 启用分类
     */
    public void enable() {
        this.enabled = true;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 禁用分类
     */
    public void disable() {
        this.enabled = false;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 显示分类
     */
    public void show() {
        this.visible = true;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 隐藏分类
     */
    public void hide() {
        this.visible = false;
        this.updatedAt = LocalDateTime.now();
    }
}
