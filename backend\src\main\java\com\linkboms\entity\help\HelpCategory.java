package com.linkboms.entity.help;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 帮助分类实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Table(name = "help_category")
@EntityListeners(AuditingEntityListener.class)
public class HelpCategory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotBlank(message = "分类名称不能为空")
    @Size(max = 100, message = "分类名称长度不能超过100个字符")
    @Column(nullable = false, length = 100)
    private String name;

    @Size(max = 500, message = "分类描述长度不能超过500个字符")
    @Column(length = 500)
    private String description;

    @Size(max = 50, message = "图标名称长度不能超过50个字符")
    @Column(length = 50)
    private String icon;

    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;

    @Column(name = "level", nullable = false)
    private Integer level = 1;

    @Column(name = "path", length = 500)
    private String path;

    @Column(name = "is_enabled", nullable = false)
    private Boolean enabled = true;

    @Column(name = "is_visible", nullable = false)
    private Boolean visible = true;

    @Column(name = "content_count", nullable = false)
    private Long contentCount = 0L;

    @Size(max = 200, message = "SEO标题长度不能超过200个字符")
    @Column(name = "seo_title", length = 200)
    private String seoTitle;

    @Size(max = 500, message = "SEO描述长度不能超过500个字符")
    @Column(name = "seo_description", length = 500)
    private String seoDescription;

    @Size(max = 200, message = "SEO关键词长度不能超过200个字符")
    @Column(name = "seo_keywords", length = 200)
    private String seoKeywords;

    @ElementCollection
    @CollectionTable(name = "help_category_permissions", joinColumns = @JoinColumn(name = "category_id"))
    @Column(name = "permission")
    private List<String> permissions;

    @Column(name = "deleted", nullable = false)
    private Boolean deleted = false;

    @CreatedDate
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // 关联的子分类（仅用于查询，不持久化）
    @Transient
    private List<HelpCategory> children;

    // 关联的内容（仅用于查询，不持久化）
    @Transient
    private List<HelpContent> contents;

    // Constructors
    public HelpCategory() {}

    public HelpCategory(String name, String description) {
        this.name = name;
        this.description = description;
    }

    public HelpCategory(String name, String description, Long parentId) {
        this.name = name;
        this.description = description;
        this.parentId = parentId;
    }

    // Business Methods
    
    /**
     * 是否为根分类
     */
    public boolean isRoot() {
        return parentId == null;
    }

    /**
     * 是否为叶子分类
     */
    public boolean isLeaf() {
        return children == null || children.isEmpty();
    }

    /**
     * 获取完整路径
     */
    public String getFullPath() {
        if (path == null) {
            return String.valueOf(id);
        }
        return path + "/" + id;
    }

    /**
     * 增加内容数量
     */
    public void incrementContentCount() {
        this.contentCount++;
    }

    /**
     * 减少内容数量
     */
    public void decrementContentCount() {
        if (this.contentCount > 0) {
            this.contentCount--;
        }
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public Boolean getVisible() {
        return visible;
    }

    public void setVisible(Boolean visible) {
        this.visible = visible;
    }

    public Long getContentCount() {
        return contentCount;
    }

    public void setContentCount(Long contentCount) {
        this.contentCount = contentCount;
    }

    public String getSeoTitle() {
        return seoTitle;
    }

    public void setSeoTitle(String seoTitle) {
        this.seoTitle = seoTitle;
    }

    public String getSeoDescription() {
        return seoDescription;
    }

    public void setSeoDescription(String seoDescription) {
        this.seoDescription = seoDescription;
    }

    public String getSeoKeywords() {
        return seoKeywords;
    }

    public void setSeoKeywords(String seoKeywords) {
        this.seoKeywords = seoKeywords;
    }

    public List<String> getPermissions() {
        return permissions;
    }

    public void setPermissions(List<String> permissions) {
        this.permissions = permissions;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<HelpCategory> getChildren() {
        return children;
    }

    public void setChildren(List<HelpCategory> children) {
        this.children = children;
    }

    public List<HelpContent> getContents() {
        return contents;
    }

    public void setContents(List<HelpContent> contents) {
        this.contents = contents;
    }

    @Override
    public String toString() {
        return "HelpCategory{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", parentId=" + parentId +
                ", level=" + level +
                ", sortOrder=" + sortOrder +
                ", enabled=" + enabled +
                '}';
    }
}