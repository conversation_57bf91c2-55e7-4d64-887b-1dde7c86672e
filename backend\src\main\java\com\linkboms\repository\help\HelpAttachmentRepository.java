package com.linkboms.repository.help;

import com.linkboms.entity.help.HelpAttachment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 帮助附件Repository接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface HelpAttachmentRepository extends JpaRepository<HelpAttachment, Long> {

    /**
     * 根据内容ID查找附件
     */
    List<HelpAttachment> findByContentIdAndDeletedFalseOrderByCreatedAtAsc(Long contentId);

    /**
     * 根据内容ID分页查找附件
     */
    Page<HelpAttachment> findByContentIdAndDeletedFalseOrderByCreatedAtAsc(Long contentId, Pageable pageable);

    /**
     * 根据类型查找附件
     */
    Page<HelpAttachment> findByTypeAndDeletedFalseOrderByCreatedAtDesc(HelpAttachment.AttachmentType type, Pageable pageable);

    /**
     * 根据文件路径查找附件
     */
    Optional<HelpAttachment> findByFilePathAndDeletedFalse(String filePath);

    /**
     * 根据原始文件名查找附件
     */
    List<HelpAttachment> findByOriginalNameAndDeletedFalse(String originalName);

    /**
     * 查找指定内容的图片附件
     */
    List<HelpAttachment> findByContentIdAndTypeAndDeletedFalseOrderByCreatedAtAsc(
        Long contentId, HelpAttachment.AttachmentType type);

    /**
     * 查找大文件（超过指定大小）
     */
    @Query("SELECT a FROM HelpAttachment a WHERE a.fileSize > :size AND a.deleted = false ORDER BY a.fileSize DESC")
    List<HelpAttachment> findLargeFiles(@Param("size") Long size, Pageable pageable);

    /**
     * 查找小文件（小于指定大小）
     */
    @Query("SELECT a FROM HelpAttachment a WHERE a.fileSize < :size AND a.deleted = false ORDER BY a.fileSize ASC")
    List<HelpAttachment> findSmallFiles(@Param("size") Long size, Pageable pageable);

    /**
     * 搜索附件
     */
    @Query("SELECT a FROM HelpAttachment a WHERE " +
           "(LOWER(a.fileName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(a.originalName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(a.description) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "(:type IS NULL OR a.type = :type) AND " +
           "a.deleted = false ORDER BY a.createdAt DESC")
    Page<HelpAttachment> searchAttachments(
        @Param("keyword") String keyword,
        @Param("type") HelpAttachment.AttachmentType type,
        Pageable pageable
    );

    /**
     * 查找热门下载附件
     */
    @Query("SELECT a FROM HelpAttachment a WHERE a.deleted = false ORDER BY a.downloadCount DESC")
    List<HelpAttachment> findPopularDownloads(Pageable pageable);

    /**
     * 查找未使用的附件（没有关联内容）
     */
    @Query("SELECT a FROM HelpAttachment a WHERE " +
           "NOT EXISTS (SELECT 1 FROM HelpContent c WHERE c.id = a.contentId AND c.deleted = false) " +
           "AND a.deleted = false")
    List<HelpAttachment> findOrphanedAttachments();

    /**
     * 增加下载次数
     */
    @Modifying
    @Query("UPDATE HelpAttachment a SET a.downloadCount = a.downloadCount + 1, a.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE a.id = :id")
    void incrementDownloadCount(@Param("id") Long id);

    /**
     * 批量软删除
     */
    @Modifying
    @Query("UPDATE HelpAttachment a SET a.deleted = true, a.updatedAt = CURRENT_TIMESTAMP WHERE a.id IN :ids")
    void batchSoftDelete(@Param("ids") List<Long> ids);

    /**
     * 根据内容ID批量软删除
     */
    @Modifying
    @Query("UPDATE HelpAttachment a SET a.deleted = true, a.updatedAt = CURRENT_TIMESTAMP WHERE a.contentId = :contentId")
    void softDeleteByContentId(@Param("contentId") Long contentId);

    /**
     * 统计指定内容的附件数量
     */
    Long countByContentIdAndDeletedFalse(Long contentId);

    /**
     * 统计指定类型的附件数量
     */
    Long countByTypeAndDeletedFalse(HelpAttachment.AttachmentType type);

    /**
     * 统计各类型的附件数量
     */
    @Query("SELECT a.type, COUNT(a) FROM HelpAttachment a WHERE a.deleted = false GROUP BY a.type")
    List<Object[]> countByType();

    /**
     * 统计总文件大小
     */
    @Query("SELECT SUM(a.fileSize) FROM HelpAttachment a WHERE a.deleted = false")
    Long sumFileSize();

    /**
     * 统计指定类型的总文件大小
     */
    @Query("SELECT SUM(a.fileSize) FROM HelpAttachment a WHERE a.type = :type AND a.deleted = false")
    Long sumFileSizeByType(@Param("type") HelpAttachment.AttachmentType type);

    /**
     * 统计总下载次数
     */
    @Query("SELECT SUM(a.downloadCount) FROM HelpAttachment a WHERE a.deleted = false")
    Long sumDownloadCount();

    /**
     * 检查文件路径是否存在
     */
    boolean existsByFilePathAndDeletedFalse(String filePath);

    /**
     * 检查文件名是否存在（同一内容下）
     */
    boolean existsByContentIdAndFileNameAndDeletedFalse(Long contentId, String fileName);

    /**
     * 查找指定扩展名的附件
     */
    @Query("SELECT a FROM HelpAttachment a WHERE " +
           "LOWER(a.originalName) LIKE LOWER(CONCAT('%.', :extension)) AND a.deleted = false " +
           "ORDER BY a.createdAt DESC")
    List<HelpAttachment> findByFileExtension(@Param("extension") String extension, Pageable pageable);

    /**
     * 查找指定上传者的附件
     */
    Page<HelpAttachment> findByUploaderIdAndDeletedFalseOrderByCreatedAtDesc(Long uploaderId, Pageable pageable);

    /**
     * 查找指定时间段内上传的附件
     */
    @Query("SELECT a FROM HelpAttachment a WHERE " +
           "a.createdAt >= :startDate AND a.createdAt <= :endDate AND a.deleted = false " +
           "ORDER BY a.createdAt DESC")
    List<HelpAttachment> findByDateRange(
        @Param("startDate") java.time.LocalDateTime startDate,
        @Param("endDate") java.time.LocalDateTime endDate,
        Pageable pageable
    );

    /**
     * 查找重复文件（基于文件大小和原始名称）
     */
    @Query("SELECT a FROM HelpAttachment a WHERE " +
           "EXISTS (SELECT a2 FROM HelpAttachment a2 WHERE " +
           "a2.id != a.id AND a2.originalName = a.originalName AND a2.fileSize = a.fileSize " +
           "AND a2.deleted = false) AND a.deleted = false " +
           "ORDER BY a.originalName, a.createdAt")
    List<HelpAttachment> findDuplicateFiles();

    /**
     * 清理过期的临时文件
     */
    @Query("SELECT a FROM HelpAttachment a WHERE " +
           "a.description LIKE '%临时%' AND " +
           "a.createdAt < :expireDate AND a.deleted = false")
    List<HelpAttachment> findExpiredTempFiles(@Param("expireDate") java.time.LocalDateTime expireDate);
}
