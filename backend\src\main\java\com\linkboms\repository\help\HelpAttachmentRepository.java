package com.linkboms.repository.help;

import com.linkboms.entity.help.HelpAttachment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 帮助附件Repository接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface HelpAttachmentRepository extends JpaRepository<HelpAttachment, Long> {

    /**
     * 根据内容ID查找附件
     */
    List<HelpAttachment> findByContentIdAndDeletedFalseOrderBySortOrderAsc(Long contentId);

    /**
     * 根据内容ID和类型查找附件
     */
    List<HelpAttachment> findByContentIdAndTypeAndDeletedFalseOrderBySortOrderAsc(
        Long contentId, HelpAttachment.AttachmentType type
    );

    /**
     * 根据类型查找附件
     */
    Page<HelpAttachment> findByTypeAndDeletedFalseOrderByCreatedAtDesc(
        HelpAttachment.AttachmentType type, Pageable pageable
    );

    /**
     * 根据文件名查找附件
     */
    List<HelpAttachment> findByFileNameContainingIgnoreCaseAndDeletedFalse(String fileName);

    /**
     * 根据MIME类型查找附件
     */
    List<HelpAttachment> findByMimeTypeAndDeletedFalse(String mimeType);

    /**
     * 根据上传者查找附件
     */
    Page<HelpAttachment> findByUploaderIdAndDeletedFalseOrderByCreatedAtDesc(
        Long uploaderId, Pageable pageable
    );

    /**
     * 查找公开的附件
     */
    Page<HelpAttachment> findByPublicAccessTrueAndDeletedFalseOrderByCreatedAtDesc(Pageable pageable);

    /**
     * 查找有缩略图的附件
     */
    List<HelpAttachment> findByHasThumbnailTrueAndDeletedFalse();

    /**
     * 查找图片附件
     */
    @Query("SELECT a FROM HelpAttachment a WHERE a.type = 'IMAGE' AND a.deleted = false ORDER BY a.createdAt DESC")
    Page<HelpAttachment> findImageAttachments(Pageable pageable);

    /**
     * 查找视频附件
     */
    @Query("SELECT a FROM HelpAttachment a WHERE a.type = 'VIDEO' AND a.deleted = false ORDER BY a.createdAt DESC")
    Page<HelpAttachment> findVideoAttachments(Pageable pageable);

    /**
     * 查找文档附件
     */
    @Query("SELECT a FROM HelpAttachment a WHERE a.type = 'DOCUMENT' AND a.deleted = false ORDER BY a.createdAt DESC")
    Page<HelpAttachment> findDocumentAttachments(Pageable pageable);

    /**
     * 查找音频附件
     */
    @Query("SELECT a FROM HelpAttachment a WHERE a.type = 'AUDIO' AND a.deleted = false ORDER BY a.createdAt DESC")
    Page<HelpAttachment> findAudioAttachments(Pageable pageable);

    /**
     * 查找压缩包附件
     */
    @Query("SELECT a FROM HelpAttachment a WHERE a.type = 'ARCHIVE' AND a.deleted = false ORDER BY a.createdAt DESC")
    Page<HelpAttachment> findArchiveAttachments(Pageable pageable);

    /**
     * 查找其他类型附件
     */
    @Query("SELECT a FROM HelpAttachment a WHERE a.type = 'OTHER' AND a.deleted = false ORDER BY a.createdAt DESC")
    Page<HelpAttachment> findOtherAttachments(Pageable pageable);

    /**
     * 统计指定内容的附件数量
     */
    Long countByContentIdAndDeletedFalse(Long contentId);

    /**
     * 统计指定类型的附件数量
     */
    Long countByTypeAndDeletedFalse(HelpAttachment.AttachmentType type);

    /**
     * 统计指定上传者的附件数量
     */
    Long countByUploaderIdAndDeletedFalse(Long uploaderId);

    /**
     * 查找热门附件（下载次数多）
     */
    @Query("SELECT a FROM HelpAttachment a WHERE a.downloadCount >= 100 AND a.deleted = false ORDER BY a.downloadCount DESC")
    List<HelpAttachment> findPopularAttachments(Pageable pageable);

    /**
     * 查找最近上传的附件
     */
    List<HelpAttachment> findTop10ByDeletedFalseOrderByCreatedAtDesc();

    /**
     * 查找大文件（超过指定大小）
     */
    @Query("SELECT a FROM HelpAttachment a WHERE a.fileSize > :size AND a.deleted = false ORDER BY a.fileSize DESC")
    List<HelpAttachment> findLargeFiles(@Param("size") Long size, Pageable pageable);

    /**
     * 查找小文件（小于指定大小）
     */
    @Query("SELECT a FROM HelpAttachment a WHERE a.fileSize < :size AND a.deleted = false ORDER BY a.fileSize ASC")
    List<HelpAttachment> findSmallFiles(@Param("size") Long size, Pageable pageable);

    /**
     * 根据文件路径查找附件
     */
    HelpAttachment findByFilePathAndDeletedFalse(String filePath);

    /**
     * 检查文件路径是否存在
     */
    boolean existsByFilePathAndDeletedFalse(String filePath);

    /**
     * 检查文件名是否存在（同一内容下）
     */
    boolean existsByContentIdAndFileNameAndDeletedFalse(Long contentId, String fileName);

    /**
     * 增加下载次数
     */
    @Modifying
    @Query("UPDATE HelpAttachment a SET a.downloadCount = a.downloadCount + 1 WHERE a.id = :id")
    void incrementDownloadCount(@Param("id") Long id);

    /**
     * 批量增加下载次数
     */
    @Modifying
    @Query("UPDATE HelpAttachment a SET a.downloadCount = a.downloadCount + 1 WHERE a.id IN :ids")
    void batchIncrementDownloadCount(@Param("ids") List<Long> ids);

    /**
     * 更新附件排序
     */
    @Modifying
    @Query("UPDATE HelpAttachment a SET a.sortOrder = :sortOrder WHERE a.id = :id")
    void updateSortOrder(@Param("id") Long id, @Param("sortOrder") Integer sortOrder);

    /**
     * 软删除附件
     */
    @Modifying
    @Query("UPDATE HelpAttachment a SET a.deleted = true WHERE a.id = :id")
    void softDelete(@Param("id") Long id);

    /**
     * 批量软删除
     */
    @Modifying
    @Query("UPDATE HelpAttachment a SET a.deleted = true WHERE a.id IN :ids")
    void batchSoftDelete(@Param("ids") List<Long> ids);

    /**
     * 根据内容ID软删除所有附件
     */
    @Modifying
    @Query("UPDATE HelpAttachment a SET a.deleted = true WHERE a.contentId = :contentId")
    void softDeleteByContentId(@Param("contentId") Long contentId);

    /**
     * 恢复已删除的附件
     */
    @Modifying
    @Query("UPDATE HelpAttachment a SET a.deleted = false WHERE a.id = :id")
    void restore(@Param("id") Long id);

    /**
     * 更新公开访问权限
     */
    @Modifying
    @Query("UPDATE HelpAttachment a SET a.publicAccess = :publicAccess WHERE a.id = :id")
    void updatePublicAccess(@Param("id") Long id, @Param("publicAccess") Boolean publicAccess);

    /**
     * 批量更新公开访问权限
     */
    @Modifying
    @Query("UPDATE HelpAttachment a SET a.publicAccess = :publicAccess WHERE a.id IN :ids")
    void batchUpdatePublicAccess(@Param("ids") List<Long> ids, @Param("publicAccess") Boolean publicAccess);

    /**
     * 查找指定时间范围内的附件
     */
    @Query("SELECT a FROM HelpAttachment a WHERE a.createdAt BETWEEN :startDate AND :endDate AND a.deleted = false ORDER BY a.createdAt DESC")
    List<HelpAttachment> findByDateRange(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );

    /**
     * 统计各类型的附件数量
     */
    @Query("SELECT a.type, COUNT(a) FROM HelpAttachment a WHERE a.deleted = false GROUP BY a.type")
    List<Object[]> countByType();

    /**
     * 统计总文件大小
     */
    @Query("SELECT SUM(a.fileSize) FROM HelpAttachment a WHERE a.deleted = false")
    Long getTotalFileSize();

    /**
     * 统计指定类型的总文件大小
     */
    @Query("SELECT SUM(a.fileSize) FROM HelpAttachment a WHERE a.type = :type AND a.deleted = false")
    Long getTotalFileSizeByType(@Param("type") HelpAttachment.AttachmentType type);

    /**
     * 统计总下载次数
     */
    @Query("SELECT SUM(a.downloadCount) FROM HelpAttachment a WHERE a.deleted = false")
    Long getTotalDownloadCount();

    /**
     * 根据关键词搜索附件
     */
    @Query("SELECT a FROM HelpAttachment a WHERE " +
           "(LOWER(a.fileName) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(a.description) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(a.altText) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "a.deleted = false ORDER BY a.createdAt DESC")
    Page<HelpAttachment> searchByKeyword(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 查找孤儿附件（没有关联内容的附件）
     */
    @Query("SELECT a FROM HelpAttachment a WHERE a.contentId NOT IN (SELECT c.id FROM HelpContent c) AND a.deleted = false")
    List<HelpAttachment> findOrphanAttachments();

    /**
     * 查找指定内容的最大排序号
     */
    @Query("SELECT MAX(a.sortOrder) FROM HelpAttachment a WHERE a.contentId = :contentId AND a.deleted = false")
    Integer findMaxSortOrderByContentId(@Param("contentId") Long contentId);

    /**
     * 统计指定时间范围内的附件数量
     */
    @Query("SELECT COUNT(a) FROM HelpAttachment a WHERE a.createdAt BETWEEN :startDate AND :endDate AND a.deleted = false")
    Long countByDateRange(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );

    /**
     * 统计指定时间范围内的文件大小
     */
    @Query("SELECT SUM(a.fileSize) FROM HelpAttachment a WHERE a.createdAt BETWEEN :startDate AND :endDate AND a.deleted = false")
    Long getTotalFileSizeByDateRange(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );

    /**
     * 查找需要清理的附件（超过指定天数未被访问）
     */
    @Query("SELECT a FROM HelpAttachment a WHERE a.createdAt < :cutoffDate AND a.downloadCount = 0 AND a.deleted = false")
    List<HelpAttachment> findAttachmentsForCleanup(@Param("cutoffDate") LocalDateTime cutoffDate);
}