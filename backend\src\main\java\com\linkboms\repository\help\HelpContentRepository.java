package com.linkboms.repository.help;

import com.linkboms.entity.help.HelpContent;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 帮助内容Repository接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface HelpContentRepository extends JpaRepository<HelpContent, Long> {

    /**
     * 根据分类ID查找已发布的内容
     */
    Page<HelpContent> findByCategoryIdAndStatusAndDeletedFalseOrderBySortOrderAscCreatedAtDesc(
        Long categoryId, HelpContent.ContentStatus status, Pageable pageable);

    /**
     * 根据类型查找已发布的内容
     */
    Page<HelpContent> findByTypeAndStatusAndDeletedFalseOrderByCreatedAtDesc(
        HelpContent.ContentType type, HelpContent.ContentStatus status, Pageable pageable);

    /**
     * 查找特色内容
     */
    Page<HelpContent> findByFeaturedTrueAndStatusAndDeletedFalseOrderByCreatedAtDesc(
        HelpContent.ContentStatus status, Pageable pageable);

    /**
     * 查找最新发布的内容
     */
    Page<HelpContent> findByStatusAndDeletedFalseOrderByPublishAtDesc(
        HelpContent.ContentStatus status, Pageable pageable);

    /**
     * 查找热门内容（按浏览量排序）
     */
    Page<HelpContent> findByStatusAndDeletedFalseOrderByViewCountDesc(
        HelpContent.ContentStatus status, Pageable pageable);

    /**
     * 查找最受欢迎的内容（按点赞数排序）
     */
    Page<HelpContent> findByStatusAndDeletedFalseOrderByLikeCountDesc(
        HelpContent.ContentStatus status, Pageable pageable);

    /**
     * 根据作者查找内容
     */
    Page<HelpContent> findByAuthorIdAndDeletedFalseOrderByCreatedAtDesc(
        Long authorId, Pageable pageable);

    /**
     * 根据状态查找内容
     */
    Page<HelpContent> findByStatusAndDeletedFalseOrderByCreatedAtDesc(
        HelpContent.ContentStatus status, Pageable pageable);

    /**
     * 查找待审核的内容
     */
    Page<HelpContent> findByStatusInAndDeletedFalseOrderByCreatedAtAsc(
        List<HelpContent.ContentStatus> statuses, Pageable pageable);

    /**
     * 全文搜索
     */
    @Query("SELECT h FROM HelpContent h WHERE " +
           "(LOWER(h.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(h.content) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(h.summary) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(h.keywords) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "(:type IS NULL OR h.type = :type) AND " +
           "(:categoryId IS NULL OR h.categoryId = :categoryId) AND " +
           "h.status = :status AND h.deleted = false " +
           "ORDER BY h.viewCount DESC, h.createdAt DESC")
    Page<HelpContent> fullTextSearch(
        @Param("keyword") String keyword,
        @Param("type") HelpContent.ContentType type,
        @Param("categoryId") Long categoryId,
        @Param("status") HelpContent.ContentStatus status,
        Pageable pageable
    );

    /**
     * 高级搜索
     */
    @Query("SELECT h FROM HelpContent h WHERE " +
           "(:keyword IS NULL OR " +
           "LOWER(h.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(h.content) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(h.summary) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "(:type IS NULL OR h.type = :type) AND " +
           "(:categoryId IS NULL OR h.categoryId = :categoryId) AND " +
           "(:authorId IS NULL OR h.authorId = :authorId) AND " +
           "(:status IS NULL OR h.status = :status) AND " +
           "(:featured IS NULL OR h.featured = :featured) AND " +
           "(:startDate IS NULL OR h.createdAt >= :startDate) AND " +
           "(:endDate IS NULL OR h.createdAt <= :endDate) AND " +
           "h.deleted = false")
    Page<HelpContent> advancedSearch(
        @Param("keyword") String keyword,
        @Param("type") HelpContent.ContentType type,
        @Param("categoryId") Long categoryId,
        @Param("authorId") Long authorId,
        @Param("status") HelpContent.ContentStatus status,
        @Param("featured") Boolean featured,
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate,
        Pageable pageable
    );

    /**
     * 根据标签查找内容
     */
    @Query("SELECT DISTINCT h FROM HelpContent h JOIN h.tags t WHERE " +
           "t.name IN :tagNames AND h.status = :status AND h.deleted = false " +
           "ORDER BY h.createdAt DESC")
    Page<HelpContent> findByTagNames(
        @Param("tagNames") List<String> tagNames,
        @Param("status") HelpContent.ContentStatus status,
        Pageable pageable
    );

    /**
     * 查找相关内容（基于标签）
     */
    @Query("SELECT DISTINCT h FROM HelpContent h JOIN h.tags t WHERE " +
           "t.id IN (SELECT t2.id FROM HelpContent h2 JOIN h2.tags t2 WHERE h2.id = :contentId) AND " +
           "h.id != :contentId AND h.status = :status AND h.deleted = false " +
           "ORDER BY h.viewCount DESC")
    List<HelpContent> findRelatedContent(
        @Param("contentId") Long contentId,
        @Param("status") HelpContent.ContentStatus status,
        Pageable pageable
    );

    /**
     * 增加浏览量
     */
    @Modifying
    @Query("UPDATE HelpContent h SET h.viewCount = h.viewCount + 1, h.updatedAt = CURRENT_TIMESTAMP WHERE h.id = :id")
    void incrementViewCount(@Param("id") Long id);

    /**
     * 增加点赞数
     */
    @Modifying
    @Query("UPDATE HelpContent h SET h.likeCount = h.likeCount + 1, h.updatedAt = CURRENT_TIMESTAMP WHERE h.id = :id")
    void incrementLikeCount(@Param("id") Long id);

    /**
     * 减少点赞数
     */
    @Modifying
    @Query("UPDATE HelpContent h SET h.likeCount = CASE WHEN h.likeCount > 0 THEN h.likeCount - 1 ELSE 0 END, " +
           "h.updatedAt = CURRENT_TIMESTAMP WHERE h.id = :id")
    void decrementLikeCount(@Param("id") Long id);

    /**
     * 增加点踩数
     */
    @Modifying
    @Query("UPDATE HelpContent h SET h.dislikeCount = h.dislikeCount + 1, h.updatedAt = CURRENT_TIMESTAMP WHERE h.id = :id")
    void incrementDislikeCount(@Param("id") Long id);

    /**
     * 更新内容状态
     */
    @Modifying
    @Query("UPDATE HelpContent h SET h.status = :status, h.updatedAt = CURRENT_TIMESTAMP WHERE h.id = :id")
    void updateStatus(@Param("id") Long id, @Param("status") HelpContent.ContentStatus status);

    /**
     * 更新审核信息
     */
    @Modifying
    @Query("UPDATE HelpContent h SET h.reviewer = :reviewer, h.reviewerId = :reviewerId, " +
           "h.reviewedAt = :reviewedAt, h.updatedAt = CURRENT_TIMESTAMP WHERE h.id = :id")
    void updateReviewer(@Param("id") Long id, @Param("reviewer") String reviewer, 
                       @Param("reviewerId") Long reviewerId, @Param("reviewedAt") LocalDateTime reviewedAt);

    /**
     * 更新特色状态
     */
    @Modifying
    @Query("UPDATE HelpContent h SET h.featured = :featured, h.updatedAt = CURRENT_TIMESTAMP WHERE h.id = :id")
    void updateFeatured(@Param("id") Long id, @Param("featured") Boolean featured);

    /**
     * 更新发布状态
     */
    @Modifying
    @Query("UPDATE HelpContent h SET h.published = :published, h.publishAt = :publishAt, " +
           "h.updatedAt = CURRENT_TIMESTAMP WHERE h.id = :id")
    void updatePublished(@Param("id") Long id, @Param("published") Boolean published, 
                        @Param("publishAt") LocalDateTime publishAt);

    /**
     * 批量更新状态
     */
    @Modifying
    @Query("UPDATE HelpContent h SET h.status = :status, h.updatedAt = CURRENT_TIMESTAMP WHERE h.id IN :ids")
    void batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") HelpContent.ContentStatus status);

    /**
     * 批量软删除
     */
    @Modifying
    @Query("UPDATE HelpContent h SET h.deleted = true, h.published = false, h.updatedAt = CURRENT_TIMESTAMP " +
           "WHERE h.id IN :ids")
    void batchSoftDelete(@Param("ids") List<Long> ids);

    /**
     * 统计各状态的内容数量
     */
    @Query("SELECT h.status, COUNT(h) FROM HelpContent h WHERE h.deleted = false GROUP BY h.status")
    List<Object[]> countByStatus();

    /**
     * 统计各类型的内容数量
     */
    @Query("SELECT h.type, COUNT(h) FROM HelpContent h WHERE h.deleted = false AND h.status = :status GROUP BY h.type")
    List<Object[]> countByType(@Param("status") HelpContent.ContentStatus status);

    /**
     * 统计各分类的内容数量
     */
    @Query("SELECT h.categoryId, COUNT(h) FROM HelpContent h WHERE h.deleted = false AND h.status = :status GROUP BY h.categoryId")
    List<Object[]> countByCategory(@Param("status") HelpContent.ContentStatus status);

    /**
     * 检查标题是否已存在
     */
    boolean existsByTitleAndDeletedFalse(String title);

    /**
     * 检查标题是否已存在（排除指定ID）
     */
    boolean existsByTitleAndIdNotAndDeletedFalse(String title, Long id);

    /**
     * 查找即将过期的内容
     */
    @Query("SELECT h FROM HelpContent h WHERE h.expireAt IS NOT NULL AND h.expireAt <= :expireDate " +
           "AND h.status = :status AND h.deleted = false ORDER BY h.expireAt ASC")
    List<HelpContent> findExpiringContent(@Param("expireDate") LocalDateTime expireDate, 
                                         @Param("status") HelpContent.ContentStatus status);

    /**
     * 查找指定时间段内的热门内容
     */
    @Query("SELECT h FROM HelpContent h WHERE h.createdAt >= :startDate AND h.createdAt <= :endDate " +
           "AND h.status = :status AND h.deleted = false ORDER BY h.viewCount DESC, h.likeCount DESC")
    List<HelpContent> findPopularContentInPeriod(@Param("startDate") LocalDateTime startDate,
                                                 @Param("endDate") LocalDateTime endDate,
                                                 @Param("status") HelpContent.ContentStatus status,
                                                 Pageable pageable);
}
