package com.linkboms.repository.help;

import com.linkboms.entity.help.HelpContent;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 帮助内容Repository接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Repository
public interface HelpContentRepository extends JpaRepository<HelpContent, Long>, JpaSpecificationExecutor<HelpContent> {

    /**
     * 根据类型查找已发布的内容
     */
    Page<HelpContent> findByTypeAndStatusAndDeletedFalseOrderByPublishedAtDesc(
        HelpContent.ContentType type, 
        HelpContent.ContentStatus status, 
        Pageable pageable
    );

    /**
     * 根据分类ID查找已发布的内容
     */
    Page<HelpContent> findByCategoryIdAndStatusAndDeletedFalseOrderByPublishedAtDesc(
        Long categoryId, 
        HelpContent.ContentStatus status, 
        Pageable pageable
    );

    /**
     * 根据标题或内容搜索
     */
    @Query("SELECT h FROM HelpContent h WHERE " +
           "(LOWER(h.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(h.content) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "h.status = :status AND h.deleted = false")
    Page<HelpContent> searchByKeyword(
        @Param("keyword") String keyword, 
        @Param("status") HelpContent.ContentStatus status, 
        Pageable pageable
    );

    /**
     * 根据标签搜索内容
     */
    @Query("SELECT DISTINCT h FROM HelpContent h JOIN h.tags t WHERE " +
           "t.name IN :tagNames AND h.status = :status AND h.deleted = false")
    Page<HelpContent> findByTagsIn(
        @Param("tagNames") List<String> tagNames, 
        @Param("status") HelpContent.ContentStatus status, 
        Pageable pageable
    );

    /**
     * 查找热门内容（按浏览量排序）
     */
    @Query("SELECT h FROM HelpContent h WHERE " +
           "h.status = :status AND h.deleted = false AND h.viewCount > :minViews " +
           "ORDER BY h.viewCount DESC")
    List<HelpContent> findPopularContent(
        @Param("status") HelpContent.ContentStatus status, 
        @Param("minViews") Long minViews, 
        Pageable pageable
    );

    /**
     * 查找最新内容
     */
    List<HelpContent> findTop10ByStatusAndDeletedFalseOrderByPublishedAtDesc(
        HelpContent.ContentStatus status
    );

    /**
     * 查找特色内容
     */
    List<HelpContent> findByFeaturedTrueAndStatusAndDeletedFalseOrderBySortOrderAsc(
        HelpContent.ContentStatus status
    );

    /**
     * 查找置顶内容
     */
    List<HelpContent> findByPinnedTrueAndStatusAndDeletedFalseOrderBySortOrderAsc(
        HelpContent.ContentStatus status
    );

    /**
     * 根据作者查找内容
     */
    Page<HelpContent> findByAuthorAndDeletedFalseOrderByCreatedAtDesc(
        String author, 
        Pageable pageable
    );

    /**
     * 根据审核者查找内容
     */
    Page<HelpContent> findByReviewerAndDeletedFalseOrderByReviewedAtDesc(
        String reviewer, 
        Pageable pageable
    );

    /**
     * 查找待审核的内容
     */
    List<HelpContent> findByStatusAndDeletedFalseOrderByCreatedAtAsc(
        HelpContent.ContentStatus status
    );

    /**
     * 统计各状态的内容数量
     */
    @Query("SELECT h.status, COUNT(h) FROM HelpContent h WHERE h.deleted = false GROUP BY h.status")
    List<Object[]> countByStatus();

    /**
     * 统计各类型的内容数量
     */
    @Query("SELECT h.type, COUNT(h) FROM HelpContent h WHERE h.deleted = false GROUP BY h.type")
    List<Object[]> countByType();

    /**
     * 统计各分类的内容数量
     */
    @Query("SELECT h.categoryId, COUNT(h) FROM HelpContent h WHERE h.deleted = false GROUP BY h.categoryId")
    List<Object[]> countByCategory();

    /**
     * 增加浏览量
     */
    @Modifying
    @Query("UPDATE HelpContent h SET h.viewCount = h.viewCount + 1 WHERE h.id = :id")
    void incrementViewCount(@Param("id") Long id);

    /**
     * 增加点赞量
     */
    @Modifying
    @Query("UPDATE HelpContent h SET h.likeCount = h.likeCount + 1 WHERE h.id = :id")
    void incrementLikeCount(@Param("id") Long id);

    /**
     * 减少点赞量
     */
    @Modifying
    @Query("UPDATE HelpContent h SET h.likeCount = h.likeCount - 1 WHERE h.id = :id AND h.likeCount > 0")
    void decrementLikeCount(@Param("id") Long id);

    /**
     * 批量更新状态
     */
    @Modifying
    @Query("UPDATE HelpContent h SET h.status = :status, h.reviewer = :reviewer, " +
           "h.reviewedAt = :reviewedAt WHERE h.id IN :ids")
    void batchUpdateStatus(
        @Param("ids") List<Long> ids, 
        @Param("status") HelpContent.ContentStatus status,
        @Param("reviewer") String reviewer,
        @Param("reviewedAt") LocalDateTime reviewedAt
    );

    /**
     * 软删除内容
     */
    @Modifying
    @Query("UPDATE HelpContent h SET h.deleted = true WHERE h.id = :id")
    void softDelete(@Param("id") Long id);

    /**
     * 批量软删除
     */
    @Modifying
    @Query("UPDATE HelpContent h SET h.deleted = true WHERE h.id IN :ids")
    void batchSoftDelete(@Param("ids") List<Long> ids);

    /**
     * 恢复已删除的内容
     */
    @Modifying
    @Query("UPDATE HelpContent h SET h.deleted = false WHERE h.id = :id")
    void restore(@Param("id") Long id);

    /**
     * 查找相关内容（基于标签）
     */
    @Query("SELECT DISTINCT h FROM HelpContent h JOIN h.tags t WHERE " +
           "t.id IN (SELECT t2.id FROM HelpContent h2 JOIN h2.tags t2 WHERE h2.id = :contentId) AND " +
           "h.id != :contentId AND h.status = :status AND h.deleted = false " +
           "ORDER BY h.viewCount DESC")
    List<HelpContent> findRelatedContent(
        @Param("contentId") Long contentId, 
        @Param("status") HelpContent.ContentStatus status, 
        Pageable pageable
    );

    /**
     * 全文搜索（支持多字段）
     */
    @Query("SELECT h FROM HelpContent h WHERE " +
           "(LOWER(h.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(h.content) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(h.summary) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(h.author) LIKE LOWER(CONCAT('%', :keyword, '%'))) AND " +
           "(:type IS NULL OR h.type = :type) AND " +
           "(:categoryId IS NULL OR h.categoryId = :categoryId) AND " +
           "h.status = :status AND h.deleted = false")
    Page<HelpContent> fullTextSearch(
        @Param("keyword") String keyword,
        @Param("type") HelpContent.ContentType type,
        @Param("categoryId") Long categoryId,
        @Param("status") HelpContent.ContentStatus status,
        Pageable pageable
    );

    /**
     * 检查标题是否已存在
     */
    boolean existsByTitleAndDeletedFalse(String title);

    /**
     * 检查标题是否已存在（排除指定ID）
     */
    boolean existsByTitleAndIdNotAndDeletedFalse(String title, Long id);

    /**
     * 根据版本号查找内容
     */
    Optional<HelpContent> findByIdAndVersionAndDeletedFalse(Long id, String version);

    /**
     * 查找指定时间范围内的内容
     */
    @Query("SELECT h FROM HelpContent h WHERE " +
           "h.publishedAt BETWEEN :startDate AND :endDate AND " +
           "h.status = :status AND h.deleted = false " +
           "ORDER BY h.publishedAt DESC")
    List<HelpContent> findByDateRange(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate,
        @Param("status") HelpContent.ContentStatus status
    );
}