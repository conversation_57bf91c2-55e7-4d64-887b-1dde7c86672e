package com.linkboms.entity.help;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 帮助附件实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Table(name = "help_attachment", indexes = {
    @Index(name = "idx_help_attachment_content_id", columnList = "content_id"),
    @Index(name = "idx_help_attachment_type", columnList = "type"),
    @Index(name = "idx_help_attachment_file_path", columnList = "file_path")
})
@EntityListeners(AuditingEntityListener.class)
public class HelpAttachment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "内容ID不能为空")
    @Column(name = "content_id", nullable = false)
    private Long contentId;

    @NotBlank(message = "文件名不能为空")
    @Size(max = 255, message = "文件名长度不能超过255个字符")
    @Column(name = "file_name", nullable = false, length = 255)
    private String fileName;

    @NotBlank(message = "原始文件名不能为空")
    @Size(max = 255, message = "原始文件名长度不能超过255个字符")
    @Column(name = "original_name", nullable = false, length = 255)
    private String originalName;

    @NotBlank(message = "文件路径不能为空")
    @Size(max = 500, message = "文件路径长度不能超过500个字符")
    @Column(name = "file_path", nullable = false, length = 500)
    private String filePath;

    @Size(max = 100, message = "文件类型长度不能超过100个字符")
    @Column(name = "mime_type", length = 100)
    private String mimeType;

    @Column(name = "file_size")
    private Long fileSize;

    @Enumerated(EnumType.STRING)
    @Column(length = 20, nullable = false)
    private AttachmentType type;

    @Size(max = 500, message = "文件描述长度不能超过500个字符")
    @Column(columnDefinition = "TEXT")
    private String description;

    @Size(max = 200, message = "替代文本长度不能超过200个字符")
    @Column(name = "alt_text", length = 200)
    private String altText;

    @Column(name = "sort_order", nullable = false)
    private Integer sortOrder = 0;

    @Column(name = "download_count", nullable = false)
    private Integer downloadCount = 0;

    @Column(name = "is_public", nullable = false)
    private Boolean isPublic = true;

    @Column(name = "is_thumbnail", nullable = false)
    private Boolean isThumbnail = false;

    @Size(max = 500, message = "缩略图路径长度不能超过500个字符")
    @Column(name = "thumbnail_path", length = 500)
    private String thumbnailPath;

    // 图片/视频特有属性
    @Column(name = "width")
    private Integer width;

    @Column(name = "height")
    private Integer height;

    @Column(name = "duration")
    private Integer duration; // 视频时长（秒）

    @Size(max = 100, message = "上传者长度不能超过100个字符")
    @Column(name = "uploaded_by", length = 100)
    private String uploadedBy;

    @Column(name = "deleted", nullable = false)
    private Boolean deleted = false;

    @CreatedDate
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    // 多对一关系：附件属于某个帮助内容
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "content_id", insertable = false, updatable = false)
    private HelpContent content;

    // 附件类型枚举
    public enum AttachmentType {
        IMAGE("图片"),
        VIDEO("视频"),
        AUDIO("音频"),
        DOCUMENT("文档"),
        ARCHIVE("压缩包"),
        DIAGRAM("图表"),
        SCREENSHOT("截图"),
        ICON("图标"),
        OTHER("其他");

        private final String description;

        AttachmentType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    // Constructors
    public HelpAttachment() {}

    public HelpAttachment(Long contentId, String fileName, String originalName, String filePath, AttachmentType type) {
        this.contentId = contentId;
        this.fileName = fileName;
        this.originalName = originalName;
        this.filePath = filePath;
        this.type = type;
    }

    // Business Methods
    
    /**
     * 是否为图片类型
     */
    public boolean isImage() {
        return type == AttachmentType.IMAGE || type == AttachmentType.SCREENSHOT || type == AttachmentType.ICON;
    }

    /**
     * 是否为视频类型
     */
    public boolean isVideo() {
        return type == AttachmentType.VIDEO;
    }

    /**
     * 是否为文档类型
     */
    public boolean isDocument() {
        return type == AttachmentType.DOCUMENT;
    }

    /**
     * 是否为媒体文件
     */
    public boolean isMedia() {
        return isImage() || isVideo() || type == AttachmentType.AUDIO;
    }

    /**
     * 获取文件扩展名
     */
    public String getFileExtension() {
        if (fileName == null) {
            return "";
        }
        int lastDotIndex = fileName.lastIndexOf('.');
        return lastDotIndex > 0 ? fileName.substring(lastDotIndex + 1).toLowerCase() : "";
    }

    /**
     * 获取格式化的文件大小
     */
    public String getFormattedFileSize() {
        if (fileSize == null || fileSize == 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = fileSize.doubleValue();
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f %s", size, units[unitIndex]);
    }

    /**
     * 增加下载次数
     */
    public void incrementDownloadCount() {
        this.downloadCount++;
    }

    /**
     * 是否为热门附件
     */
    public boolean isPopular() {
        return downloadCount != null && downloadCount >= 100;
    }

    /**
     * 获取访问URL
     */
    public String getAccessUrl() {
        return "/api/help/attachments/" + id + "/download";
    }

    /**
     * 获取缩略图URL
     */
    public String getThumbnailUrl() {
        if (thumbnailPath != null && !thumbnailPath.trim().isEmpty()) {
            return "/api/help/attachments/" + id + "/thumbnail";
        }
        return null;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getContentId() {
        return contentId;
    }

    public void setContentId(Long contentId) {
        this.contentId = contentId;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getOriginalName() {
        return originalName;
    }

    public void setOriginalName(String originalName) {
        this.originalName = originalName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public AttachmentType getType() {
        return type;
    }

    public void setType(AttachmentType type) {
        this.type = type;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAltText() {
        return altText;
    }

    public void setAltText(String altText) {
        this.altText = altText;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Integer getDownloadCount() {
        return downloadCount;
    }

    public void setDownloadCount(Integer downloadCount) {
        this.downloadCount = downloadCount;
    }

    public Boolean getIsPublic() {
        return isPublic;
    }

    public void setIsPublic(Boolean isPublic) {
        this.isPublic = isPublic;
    }

    public Boolean getIsThumbnail() {
        return isThumbnail;
    }

    public void setIsThumbnail(Boolean isThumbnail) {
        this.isThumbnail = isThumbnail;
    }

    public String getThumbnailPath() {
        return thumbnailPath;
    }

    public void setThumbnailPath(String thumbnailPath) {
        this.thumbnailPath = thumbnailPath;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public String getUploadedBy() {
        return uploadedBy;
    }

    public void setUploadedBy(String uploadedBy) {
        this.uploadedBy = uploadedBy;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public HelpContent getContent() {
        return content;
    }

    public void setContent(HelpContent content) {
        this.content = content;
    }

    @Override
    public String toString() {
        return "HelpAttachment{" +
                "id=" + id +
                ", contentId=" + contentId +
                ", fileName='" + fileName + '\'' +
                ", type=" + type +
                ", fileSize=" + fileSize +
                ", downloadCount=" + downloadCount +
                '}';
    }
}