package com.linkboms.entity.help;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;

/**
 * 帮助附件实体类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Entity
@Table(name = "help_attachment")
@EntityListeners(AuditingEntityListener.class)
@Data
@EqualsAndHashCode(callSuper = false)
public class HelpAttachment {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "内容ID不能为空")
    @Column(name = "content_id", nullable = false)
    private Long contentId;

    @NotBlank(message = "文件名不能为空")
    @Size(max = 255, message = "文件名长度不能超过255个字符")
    @Column(name = "file_name", nullable = false, length = 255)
    private String fileName;

    @NotBlank(message = "原始文件名不能为空")
    @Size(max = 255, message = "原始文件名长度不能超过255个字符")
    @Column(name = "original_name", nullable = false, length = 255)
    private String originalName;

    @NotBlank(message = "文件路径不能为空")
    @Size(max = 500, message = "文件路径长度不能超过500个字符")
    @Column(name = "file_path", nullable = false, length = 500)
    private String filePath;

    @Size(max = 100, message = "文件类型长度不能超过100个字符")
    @Column(name = "content_type", length = 100)
    private String contentType;

    @Column(name = "file_size")
    private Long fileSize;

    @NotNull(message = "附件类型不能为空")
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private AttachmentType type;

    @Size(max = 500, message = "描述长度不能超过500个字符")
    @Column(length = 500)
    private String description;

    @Column(name = "download_count", nullable = false)
    private Long downloadCount = 0L;

    @Column(name = "is_deleted", nullable = false)
    private Boolean deleted = false;

    @Size(max = 100, message = "上传者名称长度不能超过100个字符")
    @Column(length = 100)
    private String uploader;

    @Column(name = "uploader_id")
    private Long uploaderId;

    @CreatedDate
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    // 附件类型枚举
    public enum AttachmentType {
        IMAGE("图片"),
        DOCUMENT("文档"),
        VIDEO("视频"),
        AUDIO("音频"),
        ARCHIVE("压缩包"),
        OTHER("其他");

        private final String description;

        AttachmentType(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 增加下载次数
     */
    public void incrementDownloadCount() {
        this.downloadCount++;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 软删除
     */
    public void softDelete() {
        this.deleted = true;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 恢复删除
     */
    public void restore() {
        this.deleted = false;
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * 获取文件扩展名
     */
    public String getFileExtension() {
        if (originalName != null && originalName.contains(".")) {
            return originalName.substring(originalName.lastIndexOf(".") + 1).toLowerCase();
        }
        return "";
    }

    /**
     * 获取格式化的文件大小
     */
    public String getFormattedFileSize() {
        if (fileSize == null) {
            return "未知";
        }
        
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 是否为图片文件
     */
    public boolean isImage() {
        String ext = getFileExtension();
        return ext.matches("jpg|jpeg|png|gif|bmp|webp|svg");
    }

    /**
     * 是否为文档文件
     */
    public boolean isDocument() {
        String ext = getFileExtension();
        return ext.matches("pdf|doc|docx|xls|xlsx|ppt|pptx|txt|md");
    }

    /**
     * 是否为视频文件
     */
    public boolean isVideo() {
        String ext = getFileExtension();
        return ext.matches("mp4|avi|mov|wmv|flv|mkv|webm");
    }

    /**
     * 是否为音频文件
     */
    public boolean isAudio() {
        String ext = getFileExtension();
        return ext.matches("mp3|wav|flac|aac|ogg|wma");
    }
}
