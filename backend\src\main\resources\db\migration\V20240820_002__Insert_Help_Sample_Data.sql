-- 插入帮助手册示例数据
-- 作者: Link-BOM-S Team
-- 版本: 1.0.0
-- 创建时间: 2024-08-20

-- 插入示例帮助内容

-- 1. BOM管理相关内容
INSERT INTO help_content (title, content, summary, type, category_id, status, is_featured, is_published, author, keywords, meta_description) VALUES
('如何创建新的BOM', 
'<h1>创建新BOM的完整指南</h1>
<h2>概述</h2>
<p>BOM（Bill of Materials）是产品制造的核心文档，详细列出了制造产品所需的所有物料、组件和子装配件。本指南将详细介绍如何在Link-BOM-S系统中创建新的BOM。</p>

<h2>前提条件</h2>
<ul>
<li>您需要具有BOM创建权限</li>
<li>相关物料信息已在系统中维护</li>
<li>产品基本信息已录入</li>
</ul>

<h2>操作步骤</h2>
<h3>步骤1：进入BOM管理模块</h3>
<p>1. 登录Link-BOM-S系统</p>
<p>2. 在主菜单中点击"BOM管理"</p>
<p>3. 选择"新建BOM"</p>

<h3>步骤2：选择BOM类型</h3>
<p>系统支持三种BOM类型：</p>
<ul>
<li><strong>产品BOM</strong>：用于销售和客户交付</li>
<li><strong>工程BOM</strong>：用于产品设计和工程变更</li>
<li><strong>制造BOM</strong>：用于生产制造</li>
</ul>

<h3>步骤3：填写基本信息</h3>
<p>根据选择的BOM类型，填写以下信息：</p>
<ul>
<li>BOM编号（系统可自动生成）</li>
<li>产品名称和型号</li>
<li>版本号</li>
<li>生效日期</li>
<li>描述信息</li>
</ul>

<h3>步骤4：添加物料清单</h3>
<p>1. 点击"添加物料"按钮</p>
<p>2. 从物料库中选择所需物料</p>
<p>3. 设置物料用量和单位</p>
<p>4. 配置替代料（如有）</p>
<p>5. 重复以上步骤直到添加完所有物料</p>

<h3>步骤5：验证和保存</h3>
<p>1. 使用系统验证功能检查BOM完整性</p>
<p>2. 确认所有信息无误后点击"保存"</p>
<p>3. 根据需要提交审核流程</p>

<h2>注意事项</h2>
<ul>
<li>BOM编号一旦保存不可修改</li>
<li>物料用量必须大于0</li>
<li>替代料的规格应与主料兼容</li>
<li>重要BOM建议启用审核流程</li>
</ul>

<h2>相关功能</h2>
<ul>
<li><a href="#bom-edit">BOM编辑</a></li>
<li><a href="#bom-copy">BOM复制</a></li>
<li><a href="#bom-version">BOM版本管理</a></li>
</ul>', 
'详细介绍如何在Link-BOM-S系统中创建新的BOM，包括操作步骤、注意事项和最佳实践。', 
'FUNCTION_GUIDE', 7, 'PUBLISHED', true, true, '系统管理员', 
'BOM创建,物料清单,产品BOM,工程BOM,制造BOM', 
'学习如何在Link-BOM-S系统中创建新的BOM，包含详细的操作步骤和注意事项'),

('BOM审核流程说明', 
'<h1>BOM审核流程详解</h1>
<h2>审核流程概述</h2>
<p>为确保BOM数据的准确性和完整性，Link-BOM-S系统提供了完善的审核流程。本文档详细说明了BOM审核的各个环节和操作方法。</p>

<h2>审核角色和权限</h2>
<h3>审核角色</h3>
<ul>
<li><strong>BOM创建者</strong>：负责创建和修改BOM</li>
<li><strong>技术审核员</strong>：负责技术层面的审核</li>
<li><strong>工艺审核员</strong>：负责工艺可行性审核</li>
<li><strong>最终审批人</strong>：负责最终审批和发布</li>
</ul>

<h2>审核流程步骤</h2>
<h3>1. 提交审核</h3>
<p>BOM创建完成后，创建者点击"提交审核"按钮，系统自动将BOM状态更改为"待审核"。</p>

<h3>2. 技术审核</h3>
<p>技术审核员收到审核通知后，需要检查：</p>
<ul>
<li>物料选型是否正确</li>
<li>技术参数是否匹配</li>
<li>替代料配置是否合理</li>
</ul>

<h3>3. 工艺审核</h3>
<p>工艺审核员主要关注：</p>
<ul>
<li>制造工艺的可行性</li>
<li>装配顺序的合理性</li>
<li>特殊工艺要求</li>
</ul>

<h3>4. 最终审批</h3>
<p>最终审批人综合考虑技术和工艺审核意见，做出最终决定。</p>

<h2>审核结果处理</h2>
<h3>审核通过</h3>
<p>BOM状态更改为"已审核"，可以发布使用。</p>

<h3>审核拒绝</h3>
<p>BOM退回给创建者，需要根据审核意见进行修改后重新提交。</p>

<h2>审核时效</h2>
<ul>
<li>技术审核：2个工作日</li>
<li>工艺审核：2个工作日</li>
<li>最终审批：1个工作日</li>
</ul>', 
'详细说明BOM审核流程的各个环节，包括审核角色、权限分配和操作步骤。', 
'WORKFLOW', 7, 'PUBLISHED', true, true, '系统管理员', 
'BOM审核,审核流程,权限管理,工作流', 
'了解BOM审核流程的完整操作指南，包括角色权限和审核步骤'),

('物料主数据维护指南', 
'<h1>物料主数据维护完整指南</h1>
<h2>物料主数据概述</h2>
<p>物料主数据是Link-BOM-S系统的基础数据，包含了物料的所有基本信息。准确完整的物料主数据是系统正常运行的前提。</p>

<h2>物料分类体系</h2>
<h3>一级分类</h3>
<ul>
<li>原材料</li>
<li>半成品</li>
<li>成品</li>
<li>包装材料</li>
<li>辅助材料</li>
</ul>

<h3>二级分类示例</h3>
<p>以原材料为例：</p>
<ul>
<li>金属材料</li>
<li>塑料材料</li>
<li>电子元器件</li>
<li>机械零件</li>
</ul>

<h2>物料编码规则</h2>
<p>物料编码采用分段式结构：</p>
<p><code>XX-XXXX-XXX-XX</code></p>
<ul>
<li>第1段（2位）：物料大类</li>
<li>第2段（4位）：物料小类</li>
<li>第3段（3位）：流水号</li>
<li>第4段（2位）：校验码</li>
</ul>

<h2>必填字段说明</h2>
<table border="1">
<tr><th>字段名称</th><th>说明</th><th>示例</th></tr>
<tr><td>物料编码</td><td>唯一标识</td><td>RM-0001-001-01</td></tr>
<tr><td>物料名称</td><td>标准名称</td><td>铝合金板材</td></tr>
<tr><td>规格型号</td><td>详细规格</td><td>6061-T6 2.0*1000*2000</td></tr>
<tr><td>基本单位</td><td>计量单位</td><td>张</td></tr>
<tr><td>物料分类</td><td>所属分类</td><td>原材料-金属材料</td></tr>
</table>

<h2>操作步骤</h2>
<h3>新增物料</h3>
<p>1. 进入"物料管理"模块</p>
<p>2. 点击"新增物料"</p>
<p>3. 选择物料分类</p>
<p>4. 填写基本信息</p>
<p>5. 设置采购信息</p>
<p>6. 配置库存参数</p>
<p>7. 保存并提交审核</p>

<h3>修改物料</h3>
<p>1. 搜索并选择要修改的物料</p>
<p>2. 点击"编辑"按钮</p>
<p>3. 修改相关信息</p>
<p>4. 保存修改</p>

<h2>质量控制</h2>
<ul>
<li>物料编码不允许重复</li>
<li>物料名称应规范统一</li>
<li>规格型号要详细准确</li>
<li>单位换算关系要正确</li>
</ul>', 
'全面介绍物料主数据的维护方法，包括分类体系、编码规则和操作流程。', 
'FUNCTION_GUIDE', 8, 'PUBLISHED', false, true, '系统管理员', 
'物料管理,主数据,编码规则,分类体系', 
'学习如何正确维护物料主数据，包括编码规则和分类体系');

-- 2. 角色权限相关内容
INSERT INTO help_content (title, content, summary, type, category_id, status, is_featured, is_published, author, keywords, meta_description) VALUES
('系统角色权限说明', 
'<h1>Link-BOM-S系统角色权限详解</h1>
<h2>角色体系概述</h2>
<p>Link-BOM-S系统采用基于角色的访问控制（RBAC）模型，通过角色来管理用户权限，确保系统安全和数据保护。</p>

<h2>系统预定义角色</h2>
<h3>1. 系统管理员（System Admin）</h3>
<p><strong>权限范围：</strong>系统最高权限</p>
<ul>
<li>用户管理：创建、修改、删除用户账户</li>
<li>角色管理：分配和修改用户角色</li>
<li>系统配置：修改系统参数和配置</li>
<li>数据备份：执行数据备份和恢复</li>
<li>日志查看：查看所有系统日志</li>
</ul>

<h3>2. BOM管理员（BOM Manager）</h3>
<p><strong>权限范围：</strong>BOM数据管理</p>
<ul>
<li>BOM创建：创建各类型BOM</li>
<li>BOM编辑：修改BOM结构和内容</li>
<li>BOM审核：审核其他用户创建的BOM</li>
<li>BOM发布：发布已审核的BOM</li>
<li>版本管理：管理BOM版本变更</li>
</ul>

<h3>3. 销售/PMC（Sales/PMC）</h3>
<p><strong>权限范围：</strong>销售和生产计划</p>
<ul>
<li>BOM查看：查看已发布的BOM</li>
<li>成本查询：查看产品成本信息</li>
<li>库存查询：查看库存状态</li>
<li>订单管理：创建和管理销售订单</li>
<li>生产计划：制定生产计划</li>
</ul>

<h3>4. 采购经理（Procurement Manager）</h3>
<p><strong>权限范围：</strong>采购业务管理</p>
<ul>
<li>供应商管理：维护供应商信息</li>
<li>采购申请：创建和审批采购申请</li>
<li>采购订单：创建和管理采购订单</li>
<li>价格管理：维护物料价格信息</li>
<li>收货管理：处理收货和验收</li>
</ul>

<h3>5. 仓库经理（Warehouse Manager）</h3>
<p><strong>权限范围：</strong>库存和仓储管理</p>
<ul>
<li>入库管理：处理物料入库</li>
<li>出库管理：处理物料出库</li>
<li>库存盘点：执行库存盘点</li>
<li>库存调拨：处理库存调拨</li>
<li>库存报表：生成库存报表</li>
</ul>

<h3>6. 财务经理（Finance Manager）</h3>
<p><strong>权限范围：</strong>财务和成本管理</p>
<ul>
<li>成本分析：查看和分析成本数据</li>
<li>价格审核：审核物料价格变更</li>
<li>财务报表：生成财务相关报表</li>
<li>预算管理：制定和监控预算</li>
<li>付款审批：审批付款申请</li>
</ul>

<h2>权限分配原则</h2>
<h3>最小权限原则</h3>
<p>用户只被授予完成其工作职责所必需的最小权限集合。</p>

<h3>职责分离原则</h3>
<p>关键业务流程需要多个角色协作完成，避免单一用户拥有过大权限。</p>

<h3>定期审查原则</h3>
<p>定期审查用户权限，及时回收不再需要的权限。</p>

<h2>权限申请流程</h2>
<p>1. 用户向直接主管提出权限申请</p>
<p>2. 主管审核申请的合理性</p>
<p>3. 系统管理员执行权限分配</p>
<p>4. 记录权限变更日志</p>', 
'详细说明Link-BOM-S系统中各角色的权限范围和管理原则。', 
'ROLE_PERMISSION', 2, 'PUBLISHED', true, true, '系统管理员', 
'角色权限,用户管理,访问控制,RBAC', 
'了解系统各角色的权限范围和权限管理的基本原则');

-- 3. 常见问题
INSERT INTO help_content (title, content, summary, type, category_id, status, is_featured, is_published, author, keywords, meta_description) VALUES
('登录问题解决方案', 
'<h1>登录问题常见解决方案</h1>
<h2>常见登录问题</h2>

<h3>问题1：忘记密码</h3>
<p><strong>解决方案：</strong></p>
<ol>
<li>在登录页面点击"忘记密码"链接</li>
<li>输入注册邮箱地址</li>
<li>查收密码重置邮件</li>
<li>按邮件指引重置密码</li>
</ol>

<h3>问题2：账户被锁定</h3>
<p><strong>原因：</strong>连续输入错误密码超过5次</p>
<p><strong>解决方案：</strong></p>
<ul>
<li>等待30分钟后自动解锁</li>
<li>或联系系统管理员手动解锁</li>
</ul>

<h3>问题3：浏览器兼容性问题</h3>
<p><strong>推荐浏览器：</strong></p>
<ul>
<li>Chrome 90+</li>
<li>Firefox 88+</li>
<li>Safari 14+</li>
<li>Edge 90+</li>
</ul>

<h3>问题4：网络连接问题</h3>
<p><strong>检查步骤：</strong></p>
<ol>
<li>确认网络连接正常</li>
<li>检查防火墙设置</li>
<li>清除浏览器缓存</li>
<li>尝试使用其他网络</li>
</ol>

<h2>预防措施</h2>
<ul>
<li>定期更换密码</li>
<li>使用强密码</li>
<li>及时更新浏览器</li>
<li>避免在公共网络登录</li>
</ul>', 
'解决用户登录过程中遇到的各种常见问题。', 
'FAQ', 5, 'PUBLISHED', false, true, '技术支持', 
'登录问题,密码重置,账户锁定,浏览器兼容', 
'快速解决登录过程中遇到的常见问题');

-- 关联标签
INSERT INTO help_content_tag (content_id, tag_id) VALUES
(1, 1), (1, 3), -- BOM创建：新手入门、常用操作
(2, 2), (2, 5), -- BOM审核：高级功能、最佳实践
(3, 1), (3, 3), -- 物料管理：新手入门、常用操作
(4, 2), (4, 5), -- 角色权限：高级功能、最佳实践
(5, 4); -- 登录问题：故障排除

COMMIT;
