package com.linkboms.service.help;

import com.linkboms.entity.help.HelpCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 帮助分类服务接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface HelpCategoryService {

    /**
     * 创建分类
     */
    HelpCategory createCategory(HelpCategory category);

    /**
     * 更新分类
     */
    HelpCategory updateCategory(Long id, HelpCategory category);

    /**
     * 根据ID获取分类
     */
    HelpCategory getCategoryById(Long id);

    /**
     * 根据ID获取分类（可选）
     */
    Optional<HelpCategory> findCategoryById(Long id);

    /**
     * 根据名称获取分类
     */
    Optional<HelpCategory> findCategoryByName(String name);

    /**
     * 根据路径获取分类
     */
    Optional<HelpCategory> findCategoryByPath(String path);

    /**
     * 删除分类
     */
    void deleteCategory(Long id);

    /**
     * 软删除分类
     */
    void softDeleteCategory(Long id);

    /**
     * 恢复分类
     */
    void restoreCategory(Long id);

    /**
     * 启用分类
     */
    void enableCategory(Long id);

    /**
     * 禁用分类
     */
    void disableCategory(Long id);

    /**
     * 显示分类
     */
    void showCategory(Long id);

    /**
     * 隐藏分类
     */
    void hideCategory(Long id);

    /**
     * 获取所有分类（分页）
     */
    Page<HelpCategory> getAllCategories(Pageable pageable);

    /**
     * 获取所有启用的分类
     */
    List<HelpCategory> getAllEnabledCategories();

    /**
     * 获取所有可见的分类
     */
    List<HelpCategory> getAllVisibleCategories();

    /**
     * 获取根分类列表
     */
    List<HelpCategory> getRootCategories();

    /**
     * 获取子分类列表
     */
    List<HelpCategory> getChildCategories(Long parentId);

    /**
     * 获取分类树
     */
    List<HelpCategory> getCategoryTree();

    /**
     * 获取指定分类的完整树
     */
    HelpCategory getCategoryTreeById(Long id);

    /**
     * 获取分类路径
     */
    List<HelpCategory> getCategoryPath(Long id);

    /**
     * 搜索分类
     */
    List<HelpCategory> searchCategories(String keyword);

    /**
     * 获取热门分类
     */
    List<HelpCategory> getPopularCategories(int limit);

    /**
     * 移动分类
     */
    void moveCategory(Long id, Long newParentId, Integer newSortOrder);

    /**
     * 调整分类排序
     */
    void adjustCategorySortOrder(Long id, Integer newSortOrder);

    /**
     * 批量更新分类状态
     */
    void batchUpdateCategoryStatus(List<Long> ids, boolean enabled);

    /**
     * 批量删除分类
     */
    void batchDeleteCategories(List<Long> ids);

    /**
     * 重建分类路径
     */
    void rebuildCategoryPaths();

    /**
     * 重建单个分类路径
     */
    void rebuildCategoryPath(Long id);

    /**
     * 验证分类数据
     */
    void validateCategory(HelpCategory category);

    /**
     * 检查分类名称是否存在
     */
    boolean isCategoryNameExists(String name);

    /**
     * 检查分类名称是否存在（排除指定ID）
     */
    boolean isCategoryNameExists(String name, Long excludeId);

    /**
     * 检查分类路径是否存在
     */
    boolean isCategoryPathExists(String path);

    /**
     * 检查分类路径是否存在（排除指定ID）
     */
    boolean isCategoryPathExists(String path, Long excludeId);

    /**
     * 获取分类统计信息
     */
    Object getCategoryStatistics();

    /**
     * 获取分类内容数量
     */
    Long getCategoryContentCount(Long categoryId);

    /**
     * 获取分类树内容数量
     */
    Long getCategoryTreeContentCount(Long categoryId);

    /**
     * 导出分类数据
     */
    byte[] exportCategories();

    /**
     * 导入分类数据
     */
    void importCategories(byte[] data);
}
