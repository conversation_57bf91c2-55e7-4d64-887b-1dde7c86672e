package com.linkboms.service.help;

import com.linkboms.entity.help.HelpCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;

/**
 * 帮助分类服务接口
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface HelpCategoryService {

    /**
     * 创建分类
     */
    HelpCategory createCategory(HelpCategory category);

    /**
     * 更新分类
     */
    HelpCategory updateCategory(Long id, HelpCategory category);

    /**
     * 根据ID获取分类
     */
    HelpCategory getCategoryById(Long id);

    /**
     * 根据ID获取分类（包含已删除）
     */
    HelpCategory getCategoryByIdIncludeDeleted(Long id);

    /**
     * 删除分类（软删除）
     */
    void deleteCategory(Long id);

    /**
     * 批量删除分类
     */
    void batchDeleteCategory(List<Long> ids);

    /**
     * 恢复已删除的分类
     */
    void restoreCategory(Long id);

    /**
     * 永久删除分类
     */
    void permanentDeleteCategory(Long id);

    /**
     * 获取所有分类
     */
    List<HelpCategory> getAllCategories();

    /**
     * 分页查询分类
     */
    Page<HelpCategory> getCategoryList(Pageable pageable);

    /**
     * 获取根分类
     */
    List<HelpCategory> getRootCategories();

    /**
     * 获取子分类
     */
    List<HelpCategory> getChildCategories(Long parentId);

    /**
     * 获取所有子孙分类
     */
    List<HelpCategory> getAllDescendantCategories(Long parentId);

    /**
     * 获取分类树
     */
    List<HelpCategory> getCategoryTree();

    /**
     * 获取分类树（指定根节点）
     */
    List<HelpCategory> getCategoryTree(Long rootId);

    /**
     * 获取可见分类
     */
    List<HelpCategory> getVisibleCategories();

    /**
     * 获取启用的分类
     */
    List<HelpCategory> getEnabledCategories();

    /**
     * 获取叶子分类
     */
    List<HelpCategory> getLeafCategories();

    /**
     * 获取有内容的分类
     */
    List<HelpCategory> getCategoriesWithContent();

    /**
     * 根据名称查找分类
     */
    HelpCategory getCategoryByName(String name);

    /**
     * 根据路径查找分类
     */
    HelpCategory getCategoryByPath(String path);

    /**
     * 搜索分类
     */
    List<HelpCategory> searchCategories(String keyword);

    /**
     * 移动分类
     */
    void moveCategory(Long categoryId, Long newParentId);

    /**
     * 批量移动分类
     */
    void batchMoveCategories(List<Long> categoryIds, Long newParentId);

    /**
     * 更新分类排序
     */
    void updateCategorySortOrder(Long id, Integer sortOrder);

    /**
     * 批量更新分类排序
     */
    void batchUpdateCategorySortOrder(Map<Long, Integer> sortOrders);

    /**
     * 启用分类
     */
    void enableCategory(Long id);

    /**
     * 禁用分类
     */
    void disableCategory(Long id);

    /**
     * 批量启用分类
     */
    void batchEnableCategories(List<Long> ids);

    /**
     * 批量禁用分类
     */
    void batchDisableCategories(List<Long> ids);

    /**
     * 设置分类可见性
     */
    void setCategoryVisibility(Long id, boolean visible);

    /**
     * 批量设置分类可见性
     */
    void batchSetCategoryVisibility(List<Long> ids, boolean visible);

    /**
     * 增加内容数量
     */
    void incrementContentCount(Long id);

    /**
     * 减少内容数量
     */
    void decrementContentCount(Long id);

    /**
     * 更新内容数量
     */
    void updateContentCount(Long id, Long count);

    /**
     * 重新计算内容数量
     */
    void recalculateContentCount(Long id);

    /**
     * 重新计算所有分类的内容数量
     */
    void recalculateAllContentCounts();

    /**
     * 复制分类
     */
    HelpCategory copyCategory(Long id, String newName, Long newParentId);

    /**
     * 复制分类树
     */
    HelpCategory copyCategoryTree(Long id, String newName, Long newParentId);

    /**
     * 获取分类路径
     */
    String getCategoryPath(Long id);

    /**
     * 获取分类面包屑
     */
    List<HelpCategory> getCategoryBreadcrumb(Long id);

    /**
     * 获取父分类链
     */
    List<HelpCategory> getParentChain(Long id);

    /**
     * 检查是否为子分类
     */
    boolean isChildCategory(Long parentId, Long childId);

    /**
     * 检查是否为祖先分类
     */
    boolean isAncestorCategory(Long ancestorId, Long descendantId);

    /**
     * 检查分类名称是否存在
     */
    boolean existsByName(String name);

    /**
     * 检查分类名称是否存在（排除指定ID）
     */
    boolean existsByNameAndIdNot(String name, Long id);

    /**
     * 检查分类路径是否存在
     */
    boolean existsByPath(String path);

    /**
     * 检查分类路径是否存在（排除指定ID）
     */
    boolean existsByPathAndIdNot(String path, Long id);

    /**
     * 检查分类是否存在
     */
    boolean existsById(Long id);

    /**
     * 验证分类
     */
    List<String> validateCategory(HelpCategory category);

    /**
     * 验证分类移动
     */
    List<String> validateCategoryMove(Long categoryId, Long newParentId);

    /**
     * 获取分类统计信息
     */
    Map<String, Object> getCategoryStatistics();

    /**
     * 获取分类统计信息（按层级）
     */
    Map<Integer, Long> getCategoryStatisticsByLevel();

    /**
     * 获取分类统计信息（按状态）
     */
    Map<String, Long> getCategoryStatisticsByStatus();

    /**
     * 重建分类路径
     */
    void rebuildCategoryPaths();

    /**
     * 重建分类层级
     */
    void rebuildCategoryLevels();

    /**
     * 清理空分类
     */
    void cleanupEmptyCategories();

    /**
     * 导出分类
     */
    byte[] exportCategories(String format);

    /**
     * 导入分类
     */
    List<HelpCategory> importCategories(byte[] data, String format);

    /**
     * 获取分类的最大排序号
     */
    Integer getMaxSortOrder(Long parentId);

    /**
     * 获取下一个排序号
     */
    Integer getNextSortOrder(Long parentId);

    /**
     * 调整分类排序
     */
    void adjustCategorySortOrder(Long parentId);

    /**
     * 获取同级分类
     */
    List<HelpCategory> getSiblingCategories(Long id);

    /**
     * 获取分类的深度
     */
    Integer getCategoryDepth(Long id);

    /**
     * 获取分类树的最大深度
     */
    Integer getMaxCategoryDepth();

    /**
     * 限制分类深度
     */
    boolean isDepthLimitExceeded(Long parentId, int maxDepth);
}