{"ast": null, "code": "var _jsxFileName = \"D:\\\\customerDemo\\\\Link-BOM-S\\\\frontend\\\\src\\\\components\\\\help\\\\AdvancedSearch.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Card, Input, Select, DatePicker, Button, Space, Form, Row, Col, Collapse, Tag, Typography, Checkbox, AutoComplete } from 'antd';\nimport { SearchOutlined, FilterOutlined, ClearOutlined, HistoryOutlined, BookOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Search\n} = Input;\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  Panel\n} = Collapse;\nconst {\n  Text\n} = Typography;\nconst CheckboxGroup = Checkbox.Group;\nconst AdvancedSearch = ({\n  onSearch,\n  onSaveSearch,\n  savedSearches = [],\n  searchHistory = [],\n  availableCategories = [],\n  availableAuthors = [],\n  availableTags = []\n}) => {\n  _s();\n  const [form] = Form.useForm();\n  const [filters, setFilters] = useState({\n    query: '',\n    type: [],\n    category: [],\n    author: [],\n    dateRange: null,\n    tags: [],\n    difficulty: [],\n    hasImages: false,\n    hasVideos: false,\n    hasAttachments: false\n  });\n  const [showAdvanced, setShowAdvanced] = useState(false);\n  const [searchSuggestions, setSearchSuggestions] = useState([]);\n\n  // 搜索建议\n  const generateSuggestions = value => {\n    if (!value) {\n      setSearchSuggestions(searchHistory.slice(0, 5));\n      return;\n    }\n    const suggestions = [...searchHistory.filter(h => h.toLowerCase().includes(value.toLowerCase())), ...availableTags.filter(t => t.toLowerCase().includes(value.toLowerCase()))].slice(0, 8);\n    setSearchSuggestions(suggestions);\n  };\n  const handleSearch = () => {\n    onSearch(filters);\n  };\n  const handleQuickSearch = query => {\n    const newFilters = {\n      ...filters,\n      query\n    };\n    setFilters(newFilters);\n    onSearch(newFilters);\n  };\n  const handleClearFilters = () => {\n    const clearedFilters = {\n      query: '',\n      type: [],\n      category: [],\n      author: [],\n      dateRange: null,\n      tags: [],\n      difficulty: [],\n      hasImages: false,\n      hasVideos: false,\n      hasAttachments: false\n    };\n    setFilters(clearedFilters);\n    form.resetFields();\n  };\n  const handleSaveSearch = () => {\n    const name = prompt('请输入搜索名称:');\n    if (name && onSaveSearch) {\n      onSaveSearch(name, filters);\n    }\n  };\n  const handleLoadSavedSearch = savedFilters => {\n    setFilters(savedFilters);\n    form.setFieldsValue({\n      query: savedFilters.query,\n      type: savedFilters.type,\n      category: savedFilters.category,\n      author: savedFilters.author,\n      dateRange: savedFilters.dateRange,\n      tags: savedFilters.tags,\n      difficulty: savedFilters.difficulty,\n      hasImages: savedFilters.hasImages,\n      hasVideos: savedFilters.hasVideos,\n      hasAttachments: savedFilters.hasAttachments\n    });\n    onSearch(savedFilters);\n  };\n  const typeOptions = [{\n    label: '功能指南',\n    value: 'function'\n  }, {\n    label: '角色权限',\n    value: 'role'\n  }, {\n    label: '知识库',\n    value: 'knowledge'\n  }, {\n    label: '工作流程',\n    value: 'workflow'\n  }, {\n    label: '业务流程',\n    value: 'process'\n  }];\n  const difficultyOptions = [{\n    label: '初级',\n    value: 'beginner'\n  }, {\n    label: '中级',\n    value: 'intermediate'\n  }, {\n    label: '高级',\n    value: 'advanced'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u9AD8\\u7EA7\\u641C\\u7D22\",\n      extra: /*#__PURE__*/_jsxDEV(Space, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 170,\n            columnNumber: 19\n          }, this),\n          onClick: handleSaveSearch,\n          disabled: !filters.query && filters.type.length === 0,\n          children: \"\\u4FDD\\u5B58\\u641C\\u7D22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          icon: /*#__PURE__*/_jsxDEV(ClearOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 25\n          }, this),\n          onClick: handleClearFilters,\n          children: \"\\u6E05\\u7A7A\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this),\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onValuesChange: (_, allValues) => {\n          setFilters({\n            ...filters,\n            ...allValues\n          });\n        },\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          label: \"\\u641C\\u7D22\\u5173\\u952E\\u8BCD\",\n          name: \"query\",\n          children: /*#__PURE__*/_jsxDEV(AutoComplete, {\n            options: searchSuggestions.map(s => ({\n              value: s\n            })),\n            onSearch: generateSuggestions,\n            onSelect: handleQuickSearch,\n            children: /*#__PURE__*/_jsxDEV(Search, {\n              placeholder: \"\\u8F93\\u5165\\u5173\\u952E\\u8BCD\\u641C\\u7D22...\",\n              allowClear: true,\n              enterButton: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 30\n              }, this),\n              onSearch: handleSearch,\n              size: \"large\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), availableTags.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '16px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Text, {\n            type: \"secondary\",\n            children: \"\\u70ED\\u95E8\\u6807\\u7B7E:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: '8px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              wrap: true,\n              children: availableTags.slice(0, 10).map(tag => /*#__PURE__*/_jsxDEV(Tag, {\n                style: {\n                  cursor: 'pointer'\n                },\n                onClick: () => handleQuickSearch(tag),\n                children: tag\n              }, tag, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n          ghost: true,\n          activeKey: showAdvanced ? ['advanced'] : [],\n          onChange: keys => setShowAdvanced(keys.includes('advanced')),\n          children: /*#__PURE__*/_jsxDEV(Panel, {\n            header: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(FilterOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 234,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                children: \"\\u9AD8\\u7EA7\\u7B5B\\u9009\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u5185\\u5BB9\\u7C7B\\u578B\",\n                  name: \"type\",\n                  children: /*#__PURE__*/_jsxDEV(CheckboxGroup, {\n                    options: typeOptions\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 243,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u96BE\\u5EA6\\u7B49\\u7EA7\",\n                  name: \"difficulty\",\n                  children: /*#__PURE__*/_jsxDEV(CheckboxGroup, {\n                    options: difficultyOptions\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u5206\\u7C7B\",\n                  name: \"category\",\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    mode: \"multiple\",\n                    placeholder: \"\\u9009\\u62E9\\u5206\\u7C7B\",\n                    allowClear: true,\n                    children: availableCategories.map(cat => /*#__PURE__*/_jsxDEV(Option, {\n                      value: cat,\n                      children: cat\n                    }, cat, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 256,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u4F5C\\u8005\",\n                  name: \"author\",\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    mode: \"multiple\",\n                    placeholder: \"\\u9009\\u62E9\\u4F5C\\u8005\",\n                    allowClear: true,\n                    children: availableAuthors.map(author => /*#__PURE__*/_jsxDEV(Option, {\n                      value: author,\n                      children: author\n                    }, author, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 275,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Row, {\n              gutter: 16,\n              children: [/*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u66F4\\u65B0\\u65F6\\u95F4\",\n                  name: \"dateRange\",\n                  children: /*#__PURE__*/_jsxDEV(RangePicker, {\n                    style: {\n                      width: '100%'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 285,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 284,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Col, {\n                span: 12,\n                children: /*#__PURE__*/_jsxDEV(Form.Item, {\n                  label: \"\\u6807\\u7B7E\",\n                  name: \"tags\",\n                  children: /*#__PURE__*/_jsxDEV(Select, {\n                    mode: \"tags\",\n                    placeholder: \"\\u8F93\\u5165\\u6216\\u9009\\u62E9\\u6807\\u7B7E\",\n                    allowClear: true,\n                    children: availableTags.map(tag => /*#__PURE__*/_jsxDEV(Option, {\n                      value: tag,\n                      children: tag\n                    }, tag, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 296,\n                      columnNumber: 25\n                    }, this))\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 290,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 282,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u5A92\\u4F53\\u5185\\u5BB9\",\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"hasImages\",\n                  valuePropName: \"checked\",\n                  style: {\n                    margin: 0\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                    children: \"\\u5305\\u542B\\u56FE\\u7247\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"hasVideos\",\n                  valuePropName: \"checked\",\n                  style: {\n                    margin: 0\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                    children: \"\\u5305\\u542B\\u89C6\\u9891\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 308,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                  name: \"hasAttachments\",\n                  valuePropName: \"checked\",\n                  style: {\n                    margin: 0\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Checkbox, {\n                    children: \"\\u5305\\u542B\\u9644\\u4EF6\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 312,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 304,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)]\n          }, \"advanced\", true, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 226,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            textAlign: 'center',\n            marginTop: '24px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 21\n            }, this),\n            size: \"large\",\n            onClick: handleSearch,\n            children: \"\\u641C\\u7D22\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 320,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this), savedSearches.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u4FDD\\u5B58\\u7684\\u641C\\u7D22\",\n      style: {\n        marginTop: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        wrap: true,\n        children: savedSearches.map((saved, index) => /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"blue\",\n          style: {\n            cursor: 'pointer',\n            padding: '4px 8px'\n          },\n          onClick: () => handleLoadSavedSearch(saved.filters),\n          children: [/*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 17\n          }, this), \" \", saved.name]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 335,\n      columnNumber: 9\n    }, this), searchHistory.length > 0 && /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u641C\\u7D22\\u5386\\u53F2\",\n      style: {\n        marginTop: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        wrap: true,\n        children: searchHistory.slice(0, 10).map((query, index) => /*#__PURE__*/_jsxDEV(Tag, {\n          style: {\n            cursor: 'pointer'\n          },\n          onClick: () => handleQuickSearch(query),\n          children: [/*#__PURE__*/_jsxDEV(HistoryOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 361,\n            columnNumber: 17\n          }, this), \" \", query]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 166,\n    columnNumber: 5\n  }, this);\n};\n_s(AdvancedSearch, \"oHcsUqzX6+0uRvF/aPGCraL2rYA=\", false, function () {\n  return [Form.useForm];\n});\n_c = AdvancedSearch;\nexport default AdvancedSearch;\nvar _c;\n$RefreshReg$(_c, \"AdvancedSearch\");", "map": {"version": 3, "names": ["React", "useState", "Card", "Input", "Select", "DatePicker", "<PERSON><PERSON>", "Space", "Form", "Row", "Col", "Collapse", "Tag", "Typography", "Checkbox", "AutoComplete", "SearchOutlined", "FilterOutlined", "ClearOutlined", "HistoryOutlined", "BookOutlined", "jsxDEV", "_jsxDEV", "Search", "Option", "RangePicker", "Panel", "Text", "CheckboxGroup", "Group", "AdvancedSearch", "onSearch", "onSaveSearch", "savedSearches", "searchHistory", "availableCategories", "availableAuthors", "availableTags", "_s", "form", "useForm", "filters", "setFilters", "query", "type", "category", "author", "date<PERSON><PERSON><PERSON>", "tags", "difficulty", "hasImages", "hasVideos", "hasAttachments", "showAdvanced", "setShowAdvanced", "searchSuggestions", "setSearchSuggestions", "generateSuggestions", "value", "slice", "suggestions", "filter", "h", "toLowerCase", "includes", "t", "handleSearch", "handleQuickSearch", "newFilters", "handleClearFilters", "clearedFilters", "resetFields", "handleSaveSearch", "name", "prompt", "handleLoadSavedSearch", "savedFilters", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "typeOptions", "label", "difficultyOptions", "style", "padding", "children", "title", "extra", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "disabled", "length", "layout", "onValuesChange", "_", "allValues", "<PERSON><PERSON>", "options", "map", "s", "onSelect", "placeholder", "allowClear", "enterButton", "size", "marginBottom", "marginTop", "wrap", "tag", "cursor", "ghost", "active<PERSON><PERSON>", "onChange", "keys", "header", "gutter", "span", "mode", "cat", "width", "direction", "valuePropName", "margin", "textAlign", "saved", "index", "color", "_c", "$RefreshReg$"], "sources": ["D:/customerDemo/Link-BOM-S/frontend/src/components/help/AdvancedSearch.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { \n  Card, \n  Input, \n  Select, \n  DatePicker, \n  Button, \n  Space, \n  Form, \n  Row, \n  Col,\n  Collapse,\n  Tag,\n  Typography,\n  Divider,\n  Checkbox,\n  Slider,\n  AutoComplete\n} from 'antd';\nimport {\n  SearchOutlined,\n  FilterOutlined,\n  ClearOutlined,\n  HistoryOutlined,\n  BookOutlined,\n  TagsOutlined\n} from '@ant-design/icons';\nimport dayjs from 'dayjs';\n\nconst { Search } = Input;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\nconst { Panel } = Collapse;\nconst { Text } = Typography;\nconst CheckboxGroup = Checkbox.Group;\n\ninterface SearchFilters {\n  query: string;\n  type: string[];\n  category: string[];\n  author: string[];\n  dateRange: [dayjs.Dayjs, dayjs.Dayjs] | null;\n  tags: string[];\n  difficulty: string[];\n  hasImages: boolean;\n  hasVideos: boolean;\n  hasAttachments: boolean;\n}\n\ninterface AdvancedSearchProps {\n  onSearch: (filters: SearchFilters) => void;\n  onSaveSearch?: (name: string, filters: SearchFilters) => void;\n  savedSearches?: Array<{ name: string; filters: SearchFilters }>;\n  searchHistory?: string[];\n  availableCategories?: string[];\n  availableAuthors?: string[];\n  availableTags?: string[];\n}\n\nconst AdvancedSearch: React.FC<AdvancedSearchProps> = ({\n  onSearch,\n  onSaveSearch,\n  savedSearches = [],\n  searchHistory = [],\n  availableCategories = [],\n  availableAuthors = [],\n  availableTags = [],\n}) => {\n  const [form] = Form.useForm();\n  const [filters, setFilters] = useState<SearchFilters>({\n    query: '',\n    type: [],\n    category: [],\n    author: [],\n    dateRange: null,\n    tags: [],\n    difficulty: [],\n    hasImages: false,\n    hasVideos: false,\n    hasAttachments: false,\n  });\n  const [showAdvanced, setShowAdvanced] = useState(false);\n  const [searchSuggestions, setSearchSuggestions] = useState<string[]>([]);\n\n  // 搜索建议\n  const generateSuggestions = (value: string) => {\n    if (!value) {\n      setSearchSuggestions(searchHistory.slice(0, 5));\n      return;\n    }\n\n    const suggestions = [\n      ...searchHistory.filter(h => h.toLowerCase().includes(value.toLowerCase())),\n      ...availableTags.filter(t => t.toLowerCase().includes(value.toLowerCase())),\n    ].slice(0, 8);\n\n    setSearchSuggestions(suggestions);\n  };\n\n  const handleSearch = () => {\n    onSearch(filters);\n  };\n\n  const handleQuickSearch = (query: string) => {\n    const newFilters = { ...filters, query };\n    setFilters(newFilters);\n    onSearch(newFilters);\n  };\n\n  const handleClearFilters = () => {\n    const clearedFilters: SearchFilters = {\n      query: '',\n      type: [],\n      category: [],\n      author: [],\n      dateRange: null,\n      tags: [],\n      difficulty: [],\n      hasImages: false,\n      hasVideos: false,\n      hasAttachments: false,\n    };\n    setFilters(clearedFilters);\n    form.resetFields();\n  };\n\n  const handleSaveSearch = () => {\n    const name = prompt('请输入搜索名称:');\n    if (name && onSaveSearch) {\n      onSaveSearch(name, filters);\n    }\n  };\n\n  const handleLoadSavedSearch = (savedFilters: SearchFilters) => {\n    setFilters(savedFilters);\n    form.setFieldsValue({\n      query: savedFilters.query,\n      type: savedFilters.type,\n      category: savedFilters.category,\n      author: savedFilters.author,\n      dateRange: savedFilters.dateRange,\n      tags: savedFilters.tags,\n      difficulty: savedFilters.difficulty,\n      hasImages: savedFilters.hasImages,\n      hasVideos: savedFilters.hasVideos,\n      hasAttachments: savedFilters.hasAttachments,\n    });\n    onSearch(savedFilters);\n  };\n\n  const typeOptions = [\n    { label: '功能指南', value: 'function' },\n    { label: '角色权限', value: 'role' },\n    { label: '知识库', value: 'knowledge' },\n    { label: '工作流程', value: 'workflow' },\n    { label: '业务流程', value: 'process' },\n  ];\n\n  const difficultyOptions = [\n    { label: '初级', value: 'beginner' },\n    { label: '中级', value: 'intermediate' },\n    { label: '高级', value: 'advanced' },\n  ];\n\n  return (\n    <div style={{ padding: '24px' }}>\n      <Card title=\"高级搜索\" extra={\n        <Space>\n          <Button\n            icon={<BookOutlined />}\n            onClick={handleSaveSearch}\n            disabled={!filters.query && filters.type.length === 0}\n          >\n            保存搜索\n          </Button>\n          <Button icon={<ClearOutlined />} onClick={handleClearFilters}>\n            清空\n          </Button>\n        </Space>\n      }>\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onValuesChange={(_, allValues) => {\n            setFilters({ ...filters, ...allValues });\n          }}\n        >\n          {/* 基础搜索 */}\n          <Form.Item label=\"搜索关键词\" name=\"query\">\n            <AutoComplete\n              options={searchSuggestions.map(s => ({ value: s }))}\n              onSearch={generateSuggestions}\n              onSelect={handleQuickSearch}\n            >\n              <Search\n                placeholder=\"输入关键词搜索...\"\n                allowClear\n                enterButton={<SearchOutlined />}\n                onSearch={handleSearch}\n                size=\"large\"\n              />\n            </AutoComplete>\n          </Form.Item>\n\n          {/* 快速标签 */}\n          {availableTags.length > 0 && (\n            <div style={{ marginBottom: '16px' }}>\n              <Text type=\"secondary\">热门标签:</Text>\n              <div style={{ marginTop: '8px' }}>\n                <Space wrap>\n                  {availableTags.slice(0, 10).map(tag => (\n                    <Tag\n                      key={tag}\n                      style={{ cursor: 'pointer' }}\n                      onClick={() => handleQuickSearch(tag)}\n                    >\n                      {tag}\n                    </Tag>\n                  ))}\n                </Space>\n              </div>\n            </div>\n          )}\n\n          {/* 高级筛选 */}\n          <Collapse \n            ghost \n            activeKey={showAdvanced ? ['advanced'] : []}\n            onChange={(keys) => setShowAdvanced(keys.includes('advanced'))}\n          >\n            <Panel \n              header={\n                <Space>\n                  <FilterOutlined />\n                  <Text>高级筛选</Text>\n                </Space>\n              } \n              key=\"advanced\"\n            >\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item label=\"内容类型\" name=\"type\">\n                    <CheckboxGroup options={typeOptions} />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item label=\"难度等级\" name=\"difficulty\">\n                    <CheckboxGroup options={difficultyOptions} />\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item label=\"分类\" name=\"category\">\n                    <Select\n                      mode=\"multiple\"\n                      placeholder=\"选择分类\"\n                      allowClear\n                    >\n                      {availableCategories.map(cat => (\n                        <Option key={cat} value={cat}>{cat}</Option>\n                      ))}\n                    </Select>\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item label=\"作者\" name=\"author\">\n                    <Select\n                      mode=\"multiple\"\n                      placeholder=\"选择作者\"\n                      allowClear\n                    >\n                      {availableAuthors.map(author => (\n                        <Option key={author} value={author}>{author}</Option>\n                      ))}\n                    </Select>\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Row gutter={16}>\n                <Col span={12}>\n                  <Form.Item label=\"更新时间\" name=\"dateRange\">\n                    <RangePicker style={{ width: '100%' }} />\n                  </Form.Item>\n                </Col>\n                <Col span={12}>\n                  <Form.Item label=\"标签\" name=\"tags\">\n                    <Select\n                      mode=\"tags\"\n                      placeholder=\"输入或选择标签\"\n                      allowClear\n                    >\n                      {availableTags.map(tag => (\n                        <Option key={tag} value={tag}>{tag}</Option>\n                      ))}\n                    </Select>\n                  </Form.Item>\n                </Col>\n              </Row>\n\n              <Form.Item label=\"媒体内容\">\n                <Space direction=\"vertical\">\n                  <Form.Item name=\"hasImages\" valuePropName=\"checked\" style={{ margin: 0 }}>\n                    <Checkbox>包含图片</Checkbox>\n                  </Form.Item>\n                  <Form.Item name=\"hasVideos\" valuePropName=\"checked\" style={{ margin: 0 }}>\n                    <Checkbox>包含视频</Checkbox>\n                  </Form.Item>\n                  <Form.Item name=\"hasAttachments\" valuePropName=\"checked\" style={{ margin: 0 }}>\n                    <Checkbox>包含附件</Checkbox>\n                  </Form.Item>\n                </Space>\n              </Form.Item>\n            </Panel>\n          </Collapse>\n\n          {/* 搜索按钮 */}\n          <div style={{ textAlign: 'center', marginTop: '24px' }}>\n            <Button \n              type=\"primary\" \n              icon={<SearchOutlined />} \n              size=\"large\"\n              onClick={handleSearch}\n            >\n              搜索\n            </Button>\n          </div>\n        </Form>\n      </Card>\n\n      {/* 保存的搜索 */}\n      {savedSearches.length > 0 && (\n        <Card title=\"保存的搜索\" style={{ marginTop: '16px' }}>\n          <Space wrap>\n            {savedSearches.map((saved, index) => (\n              <Tag\n                key={index}\n                color=\"blue\"\n                style={{ cursor: 'pointer', padding: '4px 8px' }}\n                onClick={() => handleLoadSavedSearch(saved.filters)}\n              >\n                <BookOutlined /> {saved.name}\n              </Tag>\n            ))}\n          </Space>\n        </Card>\n      )}\n\n      {/* 搜索历史 */}\n      {searchHistory.length > 0 && (\n        <Card title=\"搜索历史\" style={{ marginTop: '16px' }}>\n          <Space wrap>\n            {searchHistory.slice(0, 10).map((query, index) => (\n              <Tag\n                key={index}\n                style={{ cursor: 'pointer' }}\n                onClick={() => handleQuickSearch(query)}\n              >\n                <HistoryOutlined /> {query}\n              </Tag>\n            ))}\n          </Space>\n        </Card>\n      )}\n    </div>\n  );\n};\n\nexport default AdvancedSearch;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,QAAQ,EACRC,GAAG,EACHC,UAAU,EAEVC,QAAQ,EAERC,YAAY,QACP,MAAM;AACb,SACEC,cAAc,EACdC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,YAAY,QAEP,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG3B,MAAM;EAAEC;AAAO,CAAC,GAAGpB,KAAK;AACxB,MAAM;EAAEqB;AAAO,CAAC,GAAGpB,MAAM;AACzB,MAAM;EAAEqB;AAAY,CAAC,GAAGpB,UAAU;AAClC,MAAM;EAAEqB;AAAM,CAAC,GAAGf,QAAQ;AAC1B,MAAM;EAAEgB;AAAK,CAAC,GAAGd,UAAU;AAC3B,MAAMe,aAAa,GAAGd,QAAQ,CAACe,KAAK;AAyBpC,MAAMC,cAA6C,GAAGA,CAAC;EACrDC,QAAQ;EACRC,YAAY;EACZC,aAAa,GAAG,EAAE;EAClBC,aAAa,GAAG,EAAE;EAClBC,mBAAmB,GAAG,EAAE;EACxBC,gBAAgB,GAAG,EAAE;EACrBC,aAAa,GAAG;AAClB,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,IAAI,CAAC,GAAG/B,IAAI,CAACgC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAgB;IACpD0C,KAAK,EAAE,EAAE;IACTC,IAAI,EAAE,EAAE;IACRC,QAAQ,EAAE,EAAE;IACZC,MAAM,EAAE,EAAE;IACVC,SAAS,EAAE,IAAI;IACfC,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,EAAE;IACdC,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,KAAK;IAChBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACsD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvD,QAAQ,CAAW,EAAE,CAAC;;EAExE;EACA,MAAMwD,mBAAmB,GAAIC,KAAa,IAAK;IAC7C,IAAI,CAACA,KAAK,EAAE;MACVF,oBAAoB,CAACtB,aAAa,CAACyB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAC/C;IACF;IAEA,MAAMC,WAAW,GAAG,CAClB,GAAG1B,aAAa,CAAC2B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACN,KAAK,CAACK,WAAW,CAAC,CAAC,CAAC,CAAC,EAC3E,GAAG1B,aAAa,CAACwB,MAAM,CAACI,CAAC,IAAIA,CAAC,CAACF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACN,KAAK,CAACK,WAAW,CAAC,CAAC,CAAC,CAAC,CAC5E,CAACJ,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAEbH,oBAAoB,CAACI,WAAW,CAAC;EACnC,CAAC;EAED,MAAMM,YAAY,GAAGA,CAAA,KAAM;IACzBnC,QAAQ,CAACU,OAAO,CAAC;EACnB,CAAC;EAED,MAAM0B,iBAAiB,GAAIxB,KAAa,IAAK;IAC3C,MAAMyB,UAAU,GAAG;MAAE,GAAG3B,OAAO;MAAEE;IAAM,CAAC;IACxCD,UAAU,CAAC0B,UAAU,CAAC;IACtBrC,QAAQ,CAACqC,UAAU,CAAC;EACtB,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,cAA6B,GAAG;MACpC3B,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE,EAAE;MACRC,QAAQ,EAAE,EAAE;MACZC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE,IAAI;MACfC,IAAI,EAAE,EAAE;MACRC,UAAU,EAAE,EAAE;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,KAAK;MAChBC,cAAc,EAAE;IAClB,CAAC;IACDV,UAAU,CAAC4B,cAAc,CAAC;IAC1B/B,IAAI,CAACgC,WAAW,CAAC,CAAC;EACpB,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,IAAI,GAAGC,MAAM,CAAC,UAAU,CAAC;IAC/B,IAAID,IAAI,IAAIzC,YAAY,EAAE;MACxBA,YAAY,CAACyC,IAAI,EAAEhC,OAAO,CAAC;IAC7B;EACF,CAAC;EAED,MAAMkC,qBAAqB,GAAIC,YAA2B,IAAK;IAC7DlC,UAAU,CAACkC,YAAY,CAAC;IACxBrC,IAAI,CAACsC,cAAc,CAAC;MAClBlC,KAAK,EAAEiC,YAAY,CAACjC,KAAK;MACzBC,IAAI,EAAEgC,YAAY,CAAChC,IAAI;MACvBC,QAAQ,EAAE+B,YAAY,CAAC/B,QAAQ;MAC/BC,MAAM,EAAE8B,YAAY,CAAC9B,MAAM;MAC3BC,SAAS,EAAE6B,YAAY,CAAC7B,SAAS;MACjCC,IAAI,EAAE4B,YAAY,CAAC5B,IAAI;MACvBC,UAAU,EAAE2B,YAAY,CAAC3B,UAAU;MACnCC,SAAS,EAAE0B,YAAY,CAAC1B,SAAS;MACjCC,SAAS,EAAEyB,YAAY,CAACzB,SAAS;MACjCC,cAAc,EAAEwB,YAAY,CAACxB;IAC/B,CAAC,CAAC;IACFrB,QAAQ,CAAC6C,YAAY,CAAC;EACxB,CAAC;EAED,MAAME,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,MAAM;IAAErB,KAAK,EAAE;EAAW,CAAC,EACpC;IAAEqB,KAAK,EAAE,MAAM;IAAErB,KAAK,EAAE;EAAO,CAAC,EAChC;IAAEqB,KAAK,EAAE,KAAK;IAAErB,KAAK,EAAE;EAAY,CAAC,EACpC;IAAEqB,KAAK,EAAE,MAAM;IAAErB,KAAK,EAAE;EAAW,CAAC,EACpC;IAAEqB,KAAK,EAAE,MAAM;IAAErB,KAAK,EAAE;EAAU,CAAC,CACpC;EAED,MAAMsB,iBAAiB,GAAG,CACxB;IAAED,KAAK,EAAE,IAAI;IAAErB,KAAK,EAAE;EAAW,CAAC,EAClC;IAAEqB,KAAK,EAAE,IAAI;IAAErB,KAAK,EAAE;EAAe,CAAC,EACtC;IAAEqB,KAAK,EAAE,IAAI;IAAErB,KAAK,EAAE;EAAW,CAAC,CACnC;EAED,oBACEpC,OAAA;IAAK2D,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAC9B7D,OAAA,CAACpB,IAAI;MAACkF,KAAK,EAAC,0BAAM;MAACC,KAAK,eACtB/D,OAAA,CAACf,KAAK;QAAA4E,QAAA,gBACJ7D,OAAA,CAAChB,MAAM;UACLgF,IAAI,eAAEhE,OAAA,CAACF,YAAY;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBC,OAAO,EAAEnB,gBAAiB;UAC1BoB,QAAQ,EAAE,CAACnD,OAAO,CAACE,KAAK,IAAIF,OAAO,CAACG,IAAI,CAACiD,MAAM,KAAK,CAAE;UAAAV,QAAA,EACvD;QAED;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpE,OAAA,CAAChB,MAAM;UAACgF,IAAI,eAAEhE,OAAA,CAACJ,aAAa;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACC,OAAO,EAAEtB,kBAAmB;UAAAc,QAAA,EAAC;QAE9D;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACR;MAAAP,QAAA,eACC7D,OAAA,CAACd,IAAI;QACH+B,IAAI,EAAEA,IAAK;QACXuD,MAAM,EAAC,UAAU;QACjBC,cAAc,EAAEA,CAACC,CAAC,EAAEC,SAAS,KAAK;UAChCvD,UAAU,CAAC;YAAE,GAAGD,OAAO;YAAE,GAAGwD;UAAU,CAAC,CAAC;QAC1C,CAAE;QAAAd,QAAA,gBAGF7D,OAAA,CAACd,IAAI,CAAC0F,IAAI;UAACnB,KAAK,EAAC,gCAAO;UAACN,IAAI,EAAC,OAAO;UAAAU,QAAA,eACnC7D,OAAA,CAACP,YAAY;YACXoF,OAAO,EAAE5C,iBAAiB,CAAC6C,GAAG,CAACC,CAAC,KAAK;cAAE3C,KAAK,EAAE2C;YAAE,CAAC,CAAC,CAAE;YACpDtE,QAAQ,EAAE0B,mBAAoB;YAC9B6C,QAAQ,EAAEnC,iBAAkB;YAAAgB,QAAA,eAE5B7D,OAAA,CAACC,MAAM;cACLgF,WAAW,EAAC,+CAAY;cACxBC,UAAU;cACVC,WAAW,eAAEnF,OAAA,CAACN,cAAc;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAChC3D,QAAQ,EAAEmC,YAAa;cACvBwC,IAAI,EAAC;YAAO;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACU;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAGXrD,aAAa,CAACwD,MAAM,GAAG,CAAC,iBACvBvE,OAAA;UAAK2D,KAAK,EAAE;YAAE0B,YAAY,EAAE;UAAO,CAAE;UAAAxB,QAAA,gBACnC7D,OAAA,CAACK,IAAI;YAACiB,IAAI,EAAC,WAAW;YAAAuC,QAAA,EAAC;UAAK;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnCpE,OAAA;YAAK2D,KAAK,EAAE;cAAE2B,SAAS,EAAE;YAAM,CAAE;YAAAzB,QAAA,eAC/B7D,OAAA,CAACf,KAAK;cAACsG,IAAI;cAAA1B,QAAA,EACR9C,aAAa,CAACsB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACyC,GAAG,CAACU,GAAG,iBACjCxF,OAAA,CAACV,GAAG;gBAEFqE,KAAK,EAAE;kBAAE8B,MAAM,EAAE;gBAAU,CAAE;gBAC7BpB,OAAO,EAAEA,CAAA,KAAMxB,iBAAiB,CAAC2C,GAAG,CAAE;gBAAA3B,QAAA,EAErC2B;cAAG,GAJCA,GAAG;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKL,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDpE,OAAA,CAACX,QAAQ;UACPqG,KAAK;UACLC,SAAS,EAAE5D,YAAY,GAAG,CAAC,UAAU,CAAC,GAAG,EAAG;UAC5C6D,QAAQ,EAAGC,IAAI,IAAK7D,eAAe,CAAC6D,IAAI,CAACnD,QAAQ,CAAC,UAAU,CAAC,CAAE;UAAAmB,QAAA,eAE/D7D,OAAA,CAACI,KAAK;YACJ0F,MAAM,eACJ9F,OAAA,CAACf,KAAK;cAAA4E,QAAA,gBACJ7D,OAAA,CAACL,cAAc;gBAAAsE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClBpE,OAAA,CAACK,IAAI;gBAAAwD,QAAA,EAAC;cAAI;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CACR;YAAAP,QAAA,gBAGD7D,OAAA,CAACb,GAAG;cAAC4G,MAAM,EAAE,EAAG;cAAAlC,QAAA,gBACd7D,OAAA,CAACZ,GAAG;gBAAC4G,IAAI,EAAE,EAAG;gBAAAnC,QAAA,eACZ7D,OAAA,CAACd,IAAI,CAAC0F,IAAI;kBAACnB,KAAK,EAAC,0BAAM;kBAACN,IAAI,EAAC,MAAM;kBAAAU,QAAA,eACjC7D,OAAA,CAACM,aAAa;oBAACuE,OAAO,EAAErB;kBAAY;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC9B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNpE,OAAA,CAACZ,GAAG;gBAAC4G,IAAI,EAAE,EAAG;gBAAAnC,QAAA,eACZ7D,OAAA,CAACd,IAAI,CAAC0F,IAAI;kBAACnB,KAAK,EAAC,0BAAM;kBAACN,IAAI,EAAC,YAAY;kBAAAU,QAAA,eACvC7D,OAAA,CAACM,aAAa;oBAACuE,OAAO,EAAEnB;kBAAkB;oBAAAO,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpE,OAAA,CAACb,GAAG;cAAC4G,MAAM,EAAE,EAAG;cAAAlC,QAAA,gBACd7D,OAAA,CAACZ,GAAG;gBAAC4G,IAAI,EAAE,EAAG;gBAAAnC,QAAA,eACZ7D,OAAA,CAACd,IAAI,CAAC0F,IAAI;kBAACnB,KAAK,EAAC,cAAI;kBAACN,IAAI,EAAC,UAAU;kBAAAU,QAAA,eACnC7D,OAAA,CAAClB,MAAM;oBACLmH,IAAI,EAAC,UAAU;oBACfhB,WAAW,EAAC,0BAAM;oBAClBC,UAAU;oBAAArB,QAAA,EAEThD,mBAAmB,CAACiE,GAAG,CAACoB,GAAG,iBAC1BlG,OAAA,CAACE,MAAM;sBAAWkC,KAAK,EAAE8D,GAAI;sBAAArC,QAAA,EAAEqC;oBAAG,GAArBA,GAAG;sBAAAjC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAA2B,CAC5C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNpE,OAAA,CAACZ,GAAG;gBAAC4G,IAAI,EAAE,EAAG;gBAAAnC,QAAA,eACZ7D,OAAA,CAACd,IAAI,CAAC0F,IAAI;kBAACnB,KAAK,EAAC,cAAI;kBAACN,IAAI,EAAC,QAAQ;kBAAAU,QAAA,eACjC7D,OAAA,CAAClB,MAAM;oBACLmH,IAAI,EAAC,UAAU;oBACfhB,WAAW,EAAC,0BAAM;oBAClBC,UAAU;oBAAArB,QAAA,EAET/C,gBAAgB,CAACgE,GAAG,CAACtD,MAAM,iBAC1BxB,OAAA,CAACE,MAAM;sBAAckC,KAAK,EAAEZ,MAAO;sBAAAqC,QAAA,EAAErC;oBAAM,GAA9BA,MAAM;sBAAAyC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAiC,CACrD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpE,OAAA,CAACb,GAAG;cAAC4G,MAAM,EAAE,EAAG;cAAAlC,QAAA,gBACd7D,OAAA,CAACZ,GAAG;gBAAC4G,IAAI,EAAE,EAAG;gBAAAnC,QAAA,eACZ7D,OAAA,CAACd,IAAI,CAAC0F,IAAI;kBAACnB,KAAK,EAAC,0BAAM;kBAACN,IAAI,EAAC,WAAW;kBAAAU,QAAA,eACtC7D,OAAA,CAACG,WAAW;oBAACwD,KAAK,EAAE;sBAAEwC,KAAK,EAAE;oBAAO;kBAAE;oBAAAlC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,eACNpE,OAAA,CAACZ,GAAG;gBAAC4G,IAAI,EAAE,EAAG;gBAAAnC,QAAA,eACZ7D,OAAA,CAACd,IAAI,CAAC0F,IAAI;kBAACnB,KAAK,EAAC,cAAI;kBAACN,IAAI,EAAC,MAAM;kBAAAU,QAAA,eAC/B7D,OAAA,CAAClB,MAAM;oBACLmH,IAAI,EAAC,MAAM;oBACXhB,WAAW,EAAC,4CAAS;oBACrBC,UAAU;oBAAArB,QAAA,EAET9C,aAAa,CAAC+D,GAAG,CAACU,GAAG,iBACpBxF,OAAA,CAACE,MAAM;sBAAWkC,KAAK,EAAEoD,GAAI;sBAAA3B,QAAA,EAAE2B;oBAAG,GAArBA,GAAG;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAA2B,CAC5C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACI;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENpE,OAAA,CAACd,IAAI,CAAC0F,IAAI;cAACnB,KAAK,EAAC,0BAAM;cAAAI,QAAA,eACrB7D,OAAA,CAACf,KAAK;gBAACmH,SAAS,EAAC,UAAU;gBAAAvC,QAAA,gBACzB7D,OAAA,CAACd,IAAI,CAAC0F,IAAI;kBAACzB,IAAI,EAAC,WAAW;kBAACkD,aAAa,EAAC,SAAS;kBAAC1C,KAAK,EAAE;oBAAE2C,MAAM,EAAE;kBAAE,CAAE;kBAAAzC,QAAA,eACvE7D,OAAA,CAACR,QAAQ;oBAAAqE,QAAA,EAAC;kBAAI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACZpE,OAAA,CAACd,IAAI,CAAC0F,IAAI;kBAACzB,IAAI,EAAC,WAAW;kBAACkD,aAAa,EAAC,SAAS;kBAAC1C,KAAK,EAAE;oBAAE2C,MAAM,EAAE;kBAAE,CAAE;kBAAAzC,QAAA,eACvE7D,OAAA,CAACR,QAAQ;oBAAAqE,QAAA,EAAC;kBAAI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC,eACZpE,OAAA,CAACd,IAAI,CAAC0F,IAAI;kBAACzB,IAAI,EAAC,gBAAgB;kBAACkD,aAAa,EAAC,SAAS;kBAAC1C,KAAK,EAAE;oBAAE2C,MAAM,EAAE;kBAAE,CAAE;kBAAAzC,QAAA,eAC5E7D,OAAA,CAACR,QAAQ;oBAAAqE,QAAA,EAAC;kBAAI;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAU;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GA7ER,UAAU;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA8ET;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGXpE,OAAA;UAAK2D,KAAK,EAAE;YAAE4C,SAAS,EAAE,QAAQ;YAAEjB,SAAS,EAAE;UAAO,CAAE;UAAAzB,QAAA,eACrD7D,OAAA,CAAChB,MAAM;YACLsC,IAAI,EAAC,SAAS;YACd0C,IAAI,eAAEhE,OAAA,CAACN,cAAc;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBgB,IAAI,EAAC,OAAO;YACZf,OAAO,EAAEzB,YAAa;YAAAiB,QAAA,EACvB;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGNzD,aAAa,CAAC4D,MAAM,GAAG,CAAC,iBACvBvE,OAAA,CAACpB,IAAI;MAACkF,KAAK,EAAC,gCAAO;MAACH,KAAK,EAAE;QAAE2B,SAAS,EAAE;MAAO,CAAE;MAAAzB,QAAA,eAC/C7D,OAAA,CAACf,KAAK;QAACsG,IAAI;QAAA1B,QAAA,EACRlD,aAAa,CAACmE,GAAG,CAAC,CAAC0B,KAAK,EAAEC,KAAK,kBAC9BzG,OAAA,CAACV,GAAG;UAEFoH,KAAK,EAAC,MAAM;UACZ/C,KAAK,EAAE;YAAE8B,MAAM,EAAE,SAAS;YAAE7B,OAAO,EAAE;UAAU,CAAE;UACjDS,OAAO,EAAEA,CAAA,KAAMhB,qBAAqB,CAACmD,KAAK,CAACrF,OAAO,CAAE;UAAA0C,QAAA,gBAEpD7D,OAAA,CAACF,YAAY;YAAAmE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,KAAC,EAACoC,KAAK,CAACrD,IAAI;QAAA,GALvBsD,KAAK;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACP,EAGAxD,aAAa,CAAC2D,MAAM,GAAG,CAAC,iBACvBvE,OAAA,CAACpB,IAAI;MAACkF,KAAK,EAAC,0BAAM;MAACH,KAAK,EAAE;QAAE2B,SAAS,EAAE;MAAO,CAAE;MAAAzB,QAAA,eAC9C7D,OAAA,CAACf,KAAK;QAACsG,IAAI;QAAA1B,QAAA,EACRjD,aAAa,CAACyB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACyC,GAAG,CAAC,CAACzD,KAAK,EAAEoF,KAAK,kBAC3CzG,OAAA,CAACV,GAAG;UAEFqE,KAAK,EAAE;YAAE8B,MAAM,EAAE;UAAU,CAAE;UAC7BpB,OAAO,EAAEA,CAAA,KAAMxB,iBAAiB,CAACxB,KAAK,CAAE;UAAAwC,QAAA,gBAExC7D,OAAA,CAACH,eAAe;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,KAAC,EAAC/C,KAAK;QAAA,GAJrBoF,KAAK;UAAAxC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKP,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CACP;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpD,EAAA,CArTIR,cAA6C;EAAA,QASlCtB,IAAI,CAACgC,OAAO;AAAA;AAAAyF,EAAA,GATvBnG,cAA6C;AAuTnD,eAAeA,cAAc;AAAC,IAAAmG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}