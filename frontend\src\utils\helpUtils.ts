import { HelpCategory, HelpContent, ContentType, ContentStatus, FeedbackType } from '../types/help';

/**
 * 帮助手册工具函数
 */

/**
 * 构建分类树结构
 */
export const buildCategoryTree = (categories: HelpCategory[]): HelpCategory[] => {
  const categoryMap = new Map<number, HelpCategory>();
  const rootCategories: HelpCategory[] = [];

  // 创建分类映射
  categories.forEach(category => {
    categoryMap.set(category.id, { ...category, children: [] });
  });

  // 构建树结构
  categories.forEach(category => {
    const categoryNode = categoryMap.get(category.id);
    if (!categoryNode) return;

    if (category.parentId) {
      const parent = categoryMap.get(category.parentId);
      if (parent) {
        parent.children = parent.children || [];
        parent.children.push(categoryNode);
      }
    } else {
      rootCategories.push(categoryNode);
    }
  });

  // 排序
  const sortCategories = (cats: HelpCategory[]) => {
    cats.sort((a, b) => a.sortOrder - b.sortOrder);
    cats.forEach(cat => {
      if (cat.children && cat.children.length > 0) {
        sortCategories(cat.children);
      }
    });
  };

  sortCategories(rootCategories);
  return rootCategories;
};

/**
 * 扁平化分类树
 */
export const flattenCategoryTree = (categories: HelpCategory[]): HelpCategory[] => {
  const result: HelpCategory[] = [];
  
  const flatten = (cats: HelpCategory[], level: number = 0) => {
    cats.forEach(cat => {
      result.push({ ...cat, level });
      if (cat.children && cat.children.length > 0) {
        flatten(cat.children, level + 1);
      }
    });
  };
  
  flatten(categories);
  return result;
};

/**
 * 获取分类路径
 */
export const getCategoryPath = (categoryId: number, categories: HelpCategory[]): HelpCategory[] => {
  const categoryMap = new Map<number, HelpCategory>();
  categories.forEach(cat => categoryMap.set(cat.id, cat));
  
  const path: HelpCategory[] = [];
  let current = categoryMap.get(categoryId);
  
  while (current) {
    path.unshift(current);
    current = current.parentId ? categoryMap.get(current.parentId) : undefined;
  }
  
  return path;
};

/**
 * 搜索分类
 */
export const searchCategories = (categories: HelpCategory[], keyword: string): HelpCategory[] => {
  const lowerKeyword = keyword.toLowerCase();
  
  const matchCategory = (category: HelpCategory): boolean => {
    return category.name.toLowerCase().includes(lowerKeyword) ||
           (category.description && category.description.toLowerCase().includes(lowerKeyword));
  };
  
  const searchInTree = (cats: HelpCategory[]): HelpCategory[] => {
    const results: HelpCategory[] = [];
    
    cats.forEach(cat => {
      const isMatch = matchCategory(cat);
      const childResults = cat.children ? searchInTree(cat.children) : [];
      
      if (isMatch || childResults.length > 0) {
        results.push({
          ...cat,
          children: childResults
        });
      }
    });
    
    return results;
  };
  
  return searchInTree(categories);
};

/**
 * 高亮搜索关键词
 */
export const highlightKeyword = (text: string, keyword: string): string => {
  if (!keyword || !text) return text;
  
  const regex = new RegExp(`(${escapeRegExp(keyword)})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
};

/**
 * 转义正则表达式特殊字符
 */
export const escapeRegExp = (string: string): string => {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

/**
 * 生成内容摘要
 */
export const generateSummary = (content: string, maxLength: number = 200): string => {
  // 移除HTML标签
  const textContent = content.replace(/<[^>]*>/g, '');
  
  if (textContent.length <= maxLength) {
    return textContent;
  }
  
  // 在单词边界截断
  const truncated = textContent.substring(0, maxLength);
  const lastSpaceIndex = truncated.lastIndexOf(' ');
  
  if (lastSpaceIndex > maxLength * 0.8) {
    return truncated.substring(0, lastSpaceIndex) + '...';
  }
  
  return truncated + '...';
};

/**
 * 提取内容关键词
 */
export const extractKeywords = (content: string, maxCount: number = 10): string[] => {
  // 移除HTML标签和标点符号
  const text = content.replace(/<[^>]*>/g, '').replace(/[^\w\s\u4e00-\u9fff]/g, ' ');
  
  // 分词（简单按空格分割）
  const words = text.toLowerCase().split(/\s+/).filter(word => word.length > 2);
  
  // 统计词频
  const wordCount = new Map<string, number>();
  words.forEach(word => {
    wordCount.set(word, (wordCount.get(word) || 0) + 1);
  });
  
  // 排序并返回前N个
  return Array.from(wordCount.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, maxCount)
    .map(([word]) => word);
};

/**
 * 格式化时间
 */
export const formatTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  
  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const week = 7 * day;
  const month = 30 * day;
  
  if (diff < minute) {
    return '刚刚';
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`;
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`;
  } else if (diff < week) {
    return `${Math.floor(diff / day)}天前`;
  } else if (diff < month) {
    return `${Math.floor(diff / week)}周前`;
  } else {
    return date.toLocaleDateString('zh-CN');
  }
};

/**
 * 获取内容类型图标
 */
export const getContentTypeIcon = (type: ContentType): string => {
  const iconMap = {
    [ContentType.FUNCTION_GUIDE]: 'BookOutlined',
    [ContentType.ROLE_PERMISSION]: 'UserOutlined',
    [ContentType.INDUSTRY_KNOWLEDGE]: 'DatabaseOutlined',
    [ContentType.WORKFLOW]: 'ToolOutlined',
    [ContentType.BUSINESS_PROCESS]: 'ApartmentOutlined',
    [ContentType.FAQ]: 'QuestionCircleOutlined',
    [ContentType.TUTORIAL]: 'FileTextOutlined',
    [ContentType.ANNOUNCEMENT]: 'NotificationOutlined',
    [ContentType.DIAGRAM]: 'ApartmentOutlined',
    [ContentType.VIDEO]: 'PlayCircleOutlined'
  };
  
  return iconMap[type] || 'FileTextOutlined';
};

/**
 * 获取内容状态颜色
 */
export const getContentStatusColor = (status: ContentStatus): string => {
  const colorMap = {
    [ContentStatus.DRAFT]: 'default',
    [ContentStatus.PENDING_REVIEW]: 'processing',
    [ContentStatus.APPROVED]: 'success',
    [ContentStatus.PUBLISHED]: 'success',
    [ContentStatus.ARCHIVED]: 'warning',
    [ContentStatus.REJECTED]: 'error'
  };
  
  return colorMap[status] || 'default';
};

/**
 * 获取反馈类型图标
 */
export const getFeedbackTypeIcon = (type: FeedbackType): string => {
  const iconMap = {
    [FeedbackType.LIKE]: 'LikeOutlined',
    [FeedbackType.DISLIKE]: 'DislikeOutlined',
    [FeedbackType.COMMENT]: 'MessageOutlined',
    [FeedbackType.SUGGESTION]: 'BulbOutlined',
    [FeedbackType.BUG_REPORT]: 'BugOutlined',
    [FeedbackType.CONTENT_ERROR]: 'ExclamationCircleOutlined',
    [FeedbackType.IMPROVEMENT]: 'RiseOutlined'
  };
  
  return iconMap[type] || 'MessageOutlined';
};

/**
 * 验证邮箱格式
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * 验证手机号格式
 */
export const validatePhone = (phone: string): boolean => {
  const phoneRegex = /^1[3-9]\d{9}$/;
  return phoneRegex.test(phone);
};

/**
 * 生成随机ID
 */
export const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

/**
 * 深拷贝对象
 */
export const deepClone = <T>(obj: T): T => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T;
    Object.keys(obj).forEach(key => {
      (cloned as any)[key] = deepClone((obj as any)[key]);
    });
    return cloned;
  }
  
  return obj;
};

/**
 * 防抖函数
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};

/**
 * 节流函数
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let lastCall = 0;
  
  return (...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  };
};

/**
 * 获取文件扩展名
 */
export const getFileExtension = (filename: string): string => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
};

/**
 * 检查是否为移动设备
 */
export const isMobile = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

/**
 * 滚动到指定元素
 */
export const scrollToElement = (elementId: string, offset: number = 0): void => {
  const element = document.getElementById(elementId);
  if (element) {
    const top = element.offsetTop - offset;
    window.scrollTo({
      top,
      behavior: 'smooth'
    });
  }
};

/**
 * 复制文本到剪贴板
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (err) {
    // 降级方案
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    const success = document.execCommand('copy');
    document.body.removeChild(textArea);
    return success;
  }
};

/**
 * 下载文件
 */
export const downloadFile = (url: string, filename: string): void => {
  const link = document.createElement('a');
  link.href = url;
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export default {
  buildCategoryTree,
  flattenCategoryTree,
  getCategoryPath,
  searchCategories,
  highlightKeyword,
  generateSummary,
  extractKeywords,
  formatTime,
  getContentTypeIcon,
  getContentStatusColor,
  getFeedbackTypeIcon,
  validateEmail,
  validatePhone,
  generateId,
  deepClone,
  debounce,
  throttle,
  getFileExtension,
  isMobile,
  scrollToElement,
  copyToClipboard,
  downloadFile
};
