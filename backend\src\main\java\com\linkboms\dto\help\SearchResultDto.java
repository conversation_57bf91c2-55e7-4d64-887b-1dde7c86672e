package com.linkboms.dto.help;

import com.linkboms.enums.help.ContentType;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 搜索结果DTO
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Data
public class SearchResultDto {

    /**
     * 内容ID
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 摘要
     */
    private String summary;

    /**
     * 内容类型
     */
    private ContentType type;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 浏览量
     */
    private Long viewCount;

    /**
     * 点赞数
     */
    private Long likeCount;

    /**
     * 是否特色
     */
    private Boolean featured;

    /**
     * 作者
     */
    private String author;

    /**
     * 标签列表
     */
    private List<HelpTagDto> tags;

    /**
     * 附件列表
     */
    private List<HelpAttachmentDto> attachments;

    /**
     * 搜索相关性得分
     */
    private Double relevanceScore;

    /**
     * 高亮片段
     */
    private List<String> highlights;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 匹配的关键词
     */
    private List<String> matchedKeywords;

    /**
     * 内容片段（用于搜索结果预览）
     */
    private String snippet;

    /**
     * 是否完全匹配
     */
    private Boolean exactMatch;

    /**
     * 匹配类型（标题匹配、内容匹配、标签匹配等）
     */
    private String matchType;
}
